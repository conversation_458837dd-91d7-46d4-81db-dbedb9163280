{"ast": null, "code": "var _jsxFileName = \"E:\\\\Suraj Patil\\\\Organization_Management_System\\\\meraki-frontend-main\\\\src\\\\screens\\\\Dashboard\\\\components\\\\Backgroundprovider.jsx\",\n  _s = $RefreshSig$();\nimport PropTypes, { element } from \"prop-types\";\nimport React, { createContext, useEffect, useState, useRef } from \"react\";\nimport { useDispatch, useSelector } from \"react-redux\";\nimport { ActivityActions, UserActions } from \"../../../slices/actions\";\nimport { ActivitySelector } from \"selectors/ActivitySelector\";\nimport { UserSelector } from \"selectors\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nexport const backContext = /*#__PURE__*/createContext();\nfunction Backgroundprovider({\n  children\n}) {\n  _s();\n  const dispatch = useDispatch();\n  const [todayActivity, setActivity] = useState([]);\n  const inactivityTimeout = useRef(null);\n  const [isInactive, setIsInactive] = useState(false);\n  const activities = useSelector(ActivitySelector.getActivityHistory());\n  const todayActivityRef = useRef(null);\n  const isInactiveRef = useRef(null);\n  const [slotController, setSlotController] = useState(true);\n  const profile = useSelector(UserSelector.profile());\n  const profileRef = useRef(null);\n  // const [idelStatus,setIdelStatus] = useState(\"IdelEnd\")\n\n  /*\r\n  useEffect(() => {\r\n    \r\n      const events = [\"mousemove\", \"keydown\", \"mousedown\", \"scroll\", \"touchstart\"];\r\n  \r\n      const handleEvent = () => {\r\n        if (\r\n          todayActivityRef.current !== null &&\r\n          !todayActivityRef.current[0]?.breakStatus &&\r\n          todayActivityRef.current.length > 0 &&\r\n          !todayActivityRef.current[0].checkOutTime\r\n        ) {\r\n          if (inactivityTimeout.current) {\r\n            clearTimeout(inactivityTimeout.current);\r\n            inactivityTimeout.current = null;\r\n          }\r\n          // console.log(\"15 Min \", todayActivityRef);\r\n          setIsInactive(false);\r\n        }\r\n    \r\n        if (!inactivityTimeout.current) {\r\n          inactivityTimeout.current = setTimeout(() => {\r\n            if (\r\n              todayActivityRef.current !== null &&\r\n              !todayActivityRef.current[0]?.breakStatus &&\r\n              todayActivityRef.current.length > 0 &&\r\n              !todayActivityRef.current[0].checkOutTime\r\n            ) {\r\n              // console.log(\"35 Min \", todayActivityRef);\r\n              setIsInactive(true);\r\n            }\r\n            inactivityTimeout.current = null; // Reset the timeout reference after it runs\r\n          }, 300000); // 300000ms = 5 minutes\r\n        }\r\n      };\r\n    \r\n      // Attach event listeners\r\n      events.forEach((event) => {\r\n        window.addEventListener(event, handleEvent);\r\n      });\r\n    \r\n      // Initialize the timeout\r\n      if (!inactivityTimeout.current) {\r\n        inactivityTimeout.current = setTimeout(() => {\r\n          if (\r\n            todayActivityRef.current !== null &&\r\n            !todayActivityRef.current[0]?.breakStatus &&\r\n            todayActivityRef.current.length > 0 &&\r\n            !todayActivityRef.current[0].checkOutTime\r\n          ) {\r\n            // console.log(\"35 Min \", todayActivityRef);\r\n            setIsInactive(true);\r\n          }\r\n          inactivityTimeout.current = null; // Reset the timeout reference after it runs\r\n        }, 300000); // 300000ms = 5 minutes\r\n      }\r\n      dispatch(UserActions.profileUser())\r\n    \r\n      return () => {\r\n        // Remove event listeners\r\n        events.forEach((event) => {\r\n          window.removeEventListener(event, handleEvent);\r\n        });\r\n        clearTimeout(inactivityTimeout.current); // Clear the timeout when component unmounts\r\n      };\r\n    // }\r\n  }, []);\r\n  */\n\n  useEffect(() => {\n    if (profile !== null && profile !== void 0 && profile.role.includes(\"employee\") || profile !== null && profile !== void 0 && profile.role.includes(\"admin\")) {\n      const productivityIn = setInterval(() => {\n        console.log(\"Ensure profile:\", profile);\n        dispatch(ActivityActions.getUserActivity({\n          id: profile._id\n        }));\n        console.log(\"Profile TODAY:\", todayActivityRef.current);\n        if (todayActivityRef.current && todayActivityRef.current.length > 0) {\n          if (!todayActivityRef.current[0].checkOutTime) {\n            var _todayActivityRef$cur, _todayActivityRef$cur2;\n            console.log(\"Today Activity interval ID: \", (_todayActivityRef$cur = todayActivityRef.current[0]) === null || _todayActivityRef$cur === void 0 ? void 0 : _todayActivityRef$cur.breakStatus);\n            if (!((_todayActivityRef$cur2 = todayActivityRef.current[0]) !== null && _todayActivityRef$cur2 !== void 0 && _todayActivityRef$cur2.breakStatus)) {\n              dispatch(ActivityActions.productivityStatusRed({\n                _id: todayActivityRef.current[0]._id,\n                totalSlot: 1,\n                filledSlot: isInactive ? 0 : 1,\n                user: profile._id\n              }));\n            } else {\n              dispatch(ActivityActions.productivityStatusRed({\n                _id: todayActivityRef.current[0]._id,\n                totalSlot: 0,\n                filledSlot: 0,\n                user: profile._id\n              }));\n            }\n          }\n        }\n      }, 60000);\n      return () => clearInterval(productivityIn);\n    }\n  }, [profile, dispatch, todayActivityRef, isInactive]);\n  useEffect(() => {\n    profileRef.current = profile;\n    // console.log(\"Updated Profile Ref Current:\", profileRef.current); // Debugging line\n\n    if (!profile) {\n      // Reset local state when profile is null (logged out)\n      setActivity([]);\n      setIsInactive(false);\n      todayActivityRef.current = null;\n      isInactiveRef.current = null;\n      clearTimeout(inactivityTimeout.current);\n      inactivityTimeout.current = null;\n    }\n  }, [profile]);\n\n  /*\r\n  useEffect(() => {\r\n    \r\n    if (profile?.role.includes(\"employee\")) {\r\n     \r\n      if (todayActivity.length > 0 && todayActivity[0]._id) {\r\n        switch (isInactive) {\r\n          case true:\r\n            if (!todayActivityRef.current[0]?.checkOutTime && !todayActivityRef.current[0]?.breakStatus) {\r\n         \r\n              dispatch(ActivityActions.idelStartRed({\r\n                _id: todayActivity[0]._id,\r\n                idelStart: new Date(new Date().setMilliseconds(0)),\r\n                user: profile._id\r\n              }));\r\n            }\r\n            break;\r\n  \r\n          case false:\r\n          \r\n    \r\n                if (!todayActivity[0]?.checkOutTime && !todayActivity[0]?.breakStatus && idelStatus !== \"IdelEnd\") {\r\n                console.log(\"Active:\", todayActivityRef.current.length, todayActivity[0]?.idelHistory.length, todayActivity[0]?.idelHistory[todayActivity[0].idelHistory.length - 1]);\r\n                if (\r\n                  todayActivity?.length > 0 &&\r\n                  todayActivity[0].idelHistory.length > 0 &&\r\n                  !('idleEndedTime' in todayActivity[0].idelHistory[todayActivity[0].idelHistory.length - 1])\r\n                ) {\r\n                  setIdelStatus(\"IdelEnd\")\r\n                  console.log(\"Idel End Reducer Called :\", todayActivity[0]._id);\r\n                  dispatch(ActivityActions.idelEndRed({\r\n                    _id: todayActivity[0]._id,\r\n                    idleEnd: new Date(new Date().setMilliseconds(0)),\r\n                    user: profile._id\r\n                  }));\r\n                }\r\n              }\r\n            break;\r\n  \r\n          default:\r\n            break;\r\n        }\r\n      }\r\n      isInactiveRef.current = isInactive;\r\n    }\r\n  }, [profile, dispatch, todayActivity.length > 0 ? todayActivity[0]._id : null, isInactive]); */\n\n  useEffect(() => {\n    var _profileRef$current, _profileRef$current2;\n    if ((_profileRef$current = profileRef.current) !== null && _profileRef$current !== void 0 && _profileRef$current.role.includes(\"employee\") || (_profileRef$current2 = profileRef.current) !== null && _profileRef$current2 !== void 0 && _profileRef$current2.role.includes(\"admin\")) {\n      if (activities && activities.length > 0) {\n        if (new Date(activities[activities.length - 1].checkInTime).getDate() === new Date().getDate()) {\n          setActivity([activities[activities.length - 1]]);\n          // console.warn(\"Should be Today actitivity : \", todayActivity);\n        }\n        setSlotController(true);\n        todayActivityRef.current = todayActivity;\n        console.log(\"Ref \", todayActivityRef.current);\n      }\n    }\n  }, [activities]);\n  return /*#__PURE__*/_jsxDEV(backContext.Provider, {\n    value: {\n      activities,\n      todayActivity,\n      profile\n    },\n    children: children\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 214,\n    columnNumber: 10\n  }, this);\n}\n_s(Backgroundprovider, \"Yo5uzA0Rvmo4u8xS87QuM4zohM4=\", false, function () {\n  return [useDispatch, useSelector, useSelector];\n});\n_c = Backgroundprovider;\nBackgroundprovider.propTypes = {\n  children: PropTypes.object\n};\nexport default Backgroundprovider;\nvar _c;\n$RefreshReg$(_c, \"Backgroundprovider\");", "map": {"version": 3, "names": ["PropTypes", "element", "React", "createContext", "useEffect", "useState", "useRef", "useDispatch", "useSelector", "ActivityActions", "UserActions", "ActivitySelector", "UserSelector", "jsxDEV", "_jsxDEV", "backContext", "<PERSON><PERSON><PERSON><PERSON>", "children", "_s", "dispatch", "todayActivity", "setActivity", "inactivityTimeout", "isInactive", "setIsInactive", "activities", "getActivityHistory", "todayActivityRef", "isInactiveRef", "slotController", "setSlotController", "profile", "profileRef", "role", "includes", "productivityIn", "setInterval", "console", "log", "getUserActivity", "id", "_id", "current", "length", "checkOutTime", "_todayActivityRef$cur", "_todayActivityRef$cur2", "breakStatus", "productivityStatusRed", "totalSlot", "filledSlot", "user", "clearInterval", "clearTimeout", "_profileRef$current", "_profileRef$current2", "Date", "checkInTime", "getDate", "Provider", "value", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "propTypes", "object", "$RefreshReg$"], "sources": ["E:/Suraj Patil/Organization_Management_System/meraki-frontend-main/src/screens/Dashboard/components/Backgroundprovider.jsx"], "sourcesContent": ["import PropTypes, { element } from \"prop-types\";\r\nimport React, { createContext, useEffect, useState, useRef } from \"react\";\r\nimport { useDispatch,useSelector } from \"react-redux\";\r\nimport { ActivityActions, UserActions } from \"../../../slices/actions\";\r\nimport { ActivitySelector } from \"selectors/ActivitySelector\";\r\nimport { UserSelector } from \"selectors\";\r\n\r\nexport const backContext = createContext();\r\n\r\nfunction Backgroundprovider({ children }) {\r\n  const dispatch = useDispatch();\r\n  const [todayActivity, setActivity] = useState([]);\r\n  const inactivityTimeout = useRef(null);\r\n  const [isInactive, setIsInactive] = useState(false);\r\n  const activities = useSelector(ActivitySelector.getActivityHistory())\r\n  const todayActivityRef = useRef(null);\r\n  const isInactiveRef = useRef(null);\r\n  const [slotController,setSlotController] = useState(true)\r\n  const profile = useSelector(UserSelector.profile())\r\n  const profileRef = useRef(null)\r\n  // const [idelStatus,setIdelStatus] = useState(\"IdelEnd\")\r\n  \r\n/*\r\nuseEffect(() => {\r\n  \r\n    const events = [\"mousemove\", \"keydown\", \"mousedown\", \"scroll\", \"touchstart\"];\r\n\r\n    const handleEvent = () => {\r\n      if (\r\n        todayActivityRef.current !== null &&\r\n        !todayActivityRef.current[0]?.breakStatus &&\r\n        todayActivityRef.current.length > 0 &&\r\n        !todayActivityRef.current[0].checkOutTime\r\n      ) {\r\n        if (inactivityTimeout.current) {\r\n          clearTimeout(inactivityTimeout.current);\r\n          inactivityTimeout.current = null;\r\n        }\r\n        // console.log(\"15 Min \", todayActivityRef);\r\n        setIsInactive(false);\r\n      }\r\n  \r\n      if (!inactivityTimeout.current) {\r\n        inactivityTimeout.current = setTimeout(() => {\r\n          if (\r\n            todayActivityRef.current !== null &&\r\n            !todayActivityRef.current[0]?.breakStatus &&\r\n            todayActivityRef.current.length > 0 &&\r\n            !todayActivityRef.current[0].checkOutTime\r\n          ) {\r\n            // console.log(\"35 Min \", todayActivityRef);\r\n            setIsInactive(true);\r\n          }\r\n          inactivityTimeout.current = null; // Reset the timeout reference after it runs\r\n        }, 300000); // 300000ms = 5 minutes\r\n      }\r\n    };\r\n  \r\n    // Attach event listeners\r\n    events.forEach((event) => {\r\n      window.addEventListener(event, handleEvent);\r\n    });\r\n  \r\n    // Initialize the timeout\r\n    if (!inactivityTimeout.current) {\r\n      inactivityTimeout.current = setTimeout(() => {\r\n        if (\r\n          todayActivityRef.current !== null &&\r\n          !todayActivityRef.current[0]?.breakStatus &&\r\n          todayActivityRef.current.length > 0 &&\r\n          !todayActivityRef.current[0].checkOutTime\r\n        ) {\r\n          // console.log(\"35 Min \", todayActivityRef);\r\n          setIsInactive(true);\r\n        }\r\n        inactivityTimeout.current = null; // Reset the timeout reference after it runs\r\n      }, 300000); // 300000ms = 5 minutes\r\n    }\r\n    dispatch(UserActions.profileUser())\r\n  \r\n    return () => {\r\n      // Remove event listeners\r\n      events.forEach((event) => {\r\n        window.removeEventListener(event, handleEvent);\r\n      });\r\n      clearTimeout(inactivityTimeout.current); // Clear the timeout when component unmounts\r\n    };\r\n  // }\r\n}, []);\r\n*/\r\n\r\nuseEffect(() => {\r\n\r\n  if (profile?.role.includes(\"employee\") || profile?.role.includes(\"admin\")) {\r\n    const productivityIn = setInterval(() => {\r\n      console.log(\"Ensure profile:\", profile);\r\n      dispatch(ActivityActions.getUserActivity({\r\n        id: profile._id,\r\n      }));\r\n      console.log(\"Profile TODAY:\", todayActivityRef.current);\r\n      if (todayActivityRef.current && todayActivityRef.current.length > 0) {\r\n        if (!todayActivityRef.current[0].checkOutTime) {\r\n          console.log(\"Today Activity interval ID: \", todayActivityRef.current[0]?.breakStatus);\r\n          if(!todayActivityRef.current[0]?.breakStatus) {\r\n            dispatch(\r\n              ActivityActions.productivityStatusRed({\r\n                _id: todayActivityRef.current[0]._id,\r\n                totalSlot: 1,\r\n                filledSlot: isInactive ? 0 : 1,\r\n                user:profile._id\r\n              })\r\n            );\r\n          } else {\r\n            dispatch(\r\n              ActivityActions.productivityStatusRed({\r\n                _id: todayActivityRef.current[0]._id,\r\n                totalSlot: 0,\r\n                filledSlot: 0,\r\n                user:profile._id\r\n              })\r\n            );\r\n          }\r\n         \r\n        }\r\n      }\r\n    }, 60000);\r\n\r\n    return () => clearInterval(productivityIn);\r\n  }\r\n}, [profile, dispatch, todayActivityRef, isInactive]);\r\n\r\nuseEffect(() => {\r\n  profileRef.current = profile;\r\n  // console.log(\"Updated Profile Ref Current:\", profileRef.current); // Debugging line\r\n  \r\n  if (!profile) {\r\n    // Reset local state when profile is null (logged out)\r\n    setActivity([]);\r\n    setIsInactive(false);\r\n    todayActivityRef.current = null;\r\n    isInactiveRef.current = null;\r\n    clearTimeout(inactivityTimeout.current);\r\n    inactivityTimeout.current = null;\r\n  }\r\n\r\n}, [profile]);\r\n\r\n/*\r\nuseEffect(() => {\r\n  \r\n  if (profile?.role.includes(\"employee\")) {\r\n   \r\n    if (todayActivity.length > 0 && todayActivity[0]._id) {\r\n      switch (isInactive) {\r\n        case true:\r\n          if (!todayActivityRef.current[0]?.checkOutTime && !todayActivityRef.current[0]?.breakStatus) {\r\n       \r\n            dispatch(ActivityActions.idelStartRed({\r\n              _id: todayActivity[0]._id,\r\n              idelStart: new Date(new Date().setMilliseconds(0)),\r\n              user: profile._id\r\n            }));\r\n          }\r\n          break;\r\n\r\n        case false:\r\n        \r\n  \r\n              if (!todayActivity[0]?.checkOutTime && !todayActivity[0]?.breakStatus && idelStatus !== \"IdelEnd\") {\r\n              console.log(\"Active:\", todayActivityRef.current.length, todayActivity[0]?.idelHistory.length, todayActivity[0]?.idelHistory[todayActivity[0].idelHistory.length - 1]);\r\n              if (\r\n                todayActivity?.length > 0 &&\r\n                todayActivity[0].idelHistory.length > 0 &&\r\n                !('idleEndedTime' in todayActivity[0].idelHistory[todayActivity[0].idelHistory.length - 1])\r\n              ) {\r\n                setIdelStatus(\"IdelEnd\")\r\n                console.log(\"Idel End Reducer Called :\", todayActivity[0]._id);\r\n                dispatch(ActivityActions.idelEndRed({\r\n                  _id: todayActivity[0]._id,\r\n                  idleEnd: new Date(new Date().setMilliseconds(0)),\r\n                  user: profile._id\r\n                }));\r\n              }\r\n            }\r\n          break;\r\n\r\n        default:\r\n          break;\r\n      }\r\n    }\r\n    isInactiveRef.current = isInactive;\r\n  }\r\n}, [profile, dispatch, todayActivity.length > 0 ? todayActivity[0]._id : null, isInactive]); */\r\n\r\n  useEffect(() => {\r\n\r\n    if(profileRef.current?.role.includes(\"employee\") || profileRef.current?.role.includes(\"admin\")) {\r\n\r\n      if (activities && activities.length > 0) {\r\n        if (new Date(activities[activities.length - 1].checkInTime).getDate() === new Date().getDate()) {\r\n          setActivity([activities[activities.length - 1]]);\r\n          // console.warn(\"Should be Today actitivity : \", todayActivity);\r\n        }\r\n     \r\n        setSlotController(true);\r\n        todayActivityRef.current = todayActivity;\r\n        console.log(\"Ref \",todayActivityRef.current) \r\n     \r\n      }\r\n    }\r\n\r\n  }, [activities]);\r\n\r\n  return <backContext.Provider value={{activities, todayActivity,profile}}>{children}</backContext.Provider>;\r\n}\r\n\r\nBackgroundprovider.propTypes = {\r\n  children: PropTypes.object,\r\n};\r\n\r\nexport default Backgroundprovider;\r\n"], "mappings": ";;AAAA,OAAOA,SAAS,IAAIC,OAAO,QAAQ,YAAY;AAC/C,OAAOC,KAAK,IAAIC,aAAa,EAAEC,SAAS,EAAEC,QAAQ,EAAEC,MAAM,QAAQ,OAAO;AACzE,SAASC,WAAW,EAACC,WAAW,QAAQ,aAAa;AACrD,SAASC,eAAe,EAAEC,WAAW,QAAQ,yBAAyB;AACtE,SAASC,gBAAgB,QAAQ,4BAA4B;AAC7D,SAASC,YAAY,QAAQ,WAAW;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEzC,OAAO,MAAMC,WAAW,gBAAGZ,aAAa,CAAC,CAAC;AAE1C,SAASa,kBAAkBA,CAAC;EAAEC;AAAS,CAAC,EAAE;EAAAC,EAAA;EACxC,MAAMC,QAAQ,GAAGZ,WAAW,CAAC,CAAC;EAC9B,MAAM,CAACa,aAAa,EAAEC,WAAW,CAAC,GAAGhB,QAAQ,CAAC,EAAE,CAAC;EACjD,MAAMiB,iBAAiB,GAAGhB,MAAM,CAAC,IAAI,CAAC;EACtC,MAAM,CAACiB,UAAU,EAAEC,aAAa,CAAC,GAAGnB,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAMoB,UAAU,GAAGjB,WAAW,CAACG,gBAAgB,CAACe,kBAAkB,CAAC,CAAC,CAAC;EACrE,MAAMC,gBAAgB,GAAGrB,MAAM,CAAC,IAAI,CAAC;EACrC,MAAMsB,aAAa,GAAGtB,MAAM,CAAC,IAAI,CAAC;EAClC,MAAM,CAACuB,cAAc,EAACC,iBAAiB,CAAC,GAAGzB,QAAQ,CAAC,IAAI,CAAC;EACzD,MAAM0B,OAAO,GAAGvB,WAAW,CAACI,YAAY,CAACmB,OAAO,CAAC,CAAC,CAAC;EACnD,MAAMC,UAAU,GAAG1B,MAAM,CAAC,IAAI,CAAC;EAC/B;;EAEF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;EAEAF,SAAS,CAAC,MAAM;IAEd,IAAI2B,OAAO,aAAPA,OAAO,eAAPA,OAAO,CAAEE,IAAI,CAACC,QAAQ,CAAC,UAAU,CAAC,IAAIH,OAAO,aAAPA,OAAO,eAAPA,OAAO,CAAEE,IAAI,CAACC,QAAQ,CAAC,OAAO,CAAC,EAAE;MACzE,MAAMC,cAAc,GAAGC,WAAW,CAAC,MAAM;QACvCC,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAEP,OAAO,CAAC;QACvCZ,QAAQ,CAACV,eAAe,CAAC8B,eAAe,CAAC;UACvCC,EAAE,EAAET,OAAO,CAACU;QACd,CAAC,CAAC,CAAC;QACHJ,OAAO,CAACC,GAAG,CAAC,gBAAgB,EAAEX,gBAAgB,CAACe,OAAO,CAAC;QACvD,IAAIf,gBAAgB,CAACe,OAAO,IAAIf,gBAAgB,CAACe,OAAO,CAACC,MAAM,GAAG,CAAC,EAAE;UACnE,IAAI,CAAChB,gBAAgB,CAACe,OAAO,CAAC,CAAC,CAAC,CAACE,YAAY,EAAE;YAAA,IAAAC,qBAAA,EAAAC,sBAAA;YAC7CT,OAAO,CAACC,GAAG,CAAC,8BAA8B,GAAAO,qBAAA,GAAElB,gBAAgB,CAACe,OAAO,CAAC,CAAC,CAAC,cAAAG,qBAAA,uBAA3BA,qBAAA,CAA6BE,WAAW,CAAC;YACrF,IAAG,GAAAD,sBAAA,GAACnB,gBAAgB,CAACe,OAAO,CAAC,CAAC,CAAC,cAAAI,sBAAA,eAA3BA,sBAAA,CAA6BC,WAAW,GAAE;cAC5C5B,QAAQ,CACNV,eAAe,CAACuC,qBAAqB,CAAC;gBACpCP,GAAG,EAAEd,gBAAgB,CAACe,OAAO,CAAC,CAAC,CAAC,CAACD,GAAG;gBACpCQ,SAAS,EAAE,CAAC;gBACZC,UAAU,EAAE3B,UAAU,GAAG,CAAC,GAAG,CAAC;gBAC9B4B,IAAI,EAACpB,OAAO,CAACU;cACf,CAAC,CACH,CAAC;YACH,CAAC,MAAM;cACLtB,QAAQ,CACNV,eAAe,CAACuC,qBAAqB,CAAC;gBACpCP,GAAG,EAAEd,gBAAgB,CAACe,OAAO,CAAC,CAAC,CAAC,CAACD,GAAG;gBACpCQ,SAAS,EAAE,CAAC;gBACZC,UAAU,EAAE,CAAC;gBACbC,IAAI,EAACpB,OAAO,CAACU;cACf,CAAC,CACH,CAAC;YACH;UAEF;QACF;MACF,CAAC,EAAE,KAAK,CAAC;MAET,OAAO,MAAMW,aAAa,CAACjB,cAAc,CAAC;IAC5C;EACF,CAAC,EAAE,CAACJ,OAAO,EAAEZ,QAAQ,EAAEQ,gBAAgB,EAAEJ,UAAU,CAAC,CAAC;EAErDnB,SAAS,CAAC,MAAM;IACd4B,UAAU,CAACU,OAAO,GAAGX,OAAO;IAC5B;;IAEA,IAAI,CAACA,OAAO,EAAE;MACZ;MACAV,WAAW,CAAC,EAAE,CAAC;MACfG,aAAa,CAAC,KAAK,CAAC;MACpBG,gBAAgB,CAACe,OAAO,GAAG,IAAI;MAC/Bd,aAAa,CAACc,OAAO,GAAG,IAAI;MAC5BW,YAAY,CAAC/B,iBAAiB,CAACoB,OAAO,CAAC;MACvCpB,iBAAiB,CAACoB,OAAO,GAAG,IAAI;IAClC;EAEF,CAAC,EAAE,CAACX,OAAO,CAAC,CAAC;;EAEb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;EAEE3B,SAAS,CAAC,MAAM;IAAA,IAAAkD,mBAAA,EAAAC,oBAAA;IAEd,IAAG,CAAAD,mBAAA,GAAAtB,UAAU,CAACU,OAAO,cAAAY,mBAAA,eAAlBA,mBAAA,CAAoBrB,IAAI,CAACC,QAAQ,CAAC,UAAU,CAAC,KAAAqB,oBAAA,GAAIvB,UAAU,CAACU,OAAO,cAAAa,oBAAA,eAAlBA,oBAAA,CAAoBtB,IAAI,CAACC,QAAQ,CAAC,OAAO,CAAC,EAAE;MAE9F,IAAIT,UAAU,IAAIA,UAAU,CAACkB,MAAM,GAAG,CAAC,EAAE;QACvC,IAAI,IAAIa,IAAI,CAAC/B,UAAU,CAACA,UAAU,CAACkB,MAAM,GAAG,CAAC,CAAC,CAACc,WAAW,CAAC,CAACC,OAAO,CAAC,CAAC,KAAK,IAAIF,IAAI,CAAC,CAAC,CAACE,OAAO,CAAC,CAAC,EAAE;UAC9FrC,WAAW,CAAC,CAACI,UAAU,CAACA,UAAU,CAACkB,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC;UAChD;QACF;QAEAb,iBAAiB,CAAC,IAAI,CAAC;QACvBH,gBAAgB,CAACe,OAAO,GAAGtB,aAAa;QACxCiB,OAAO,CAACC,GAAG,CAAC,MAAM,EAACX,gBAAgB,CAACe,OAAO,CAAC;MAE9C;IACF;EAEF,CAAC,EAAE,CAACjB,UAAU,CAAC,CAAC;EAEhB,oBAAOX,OAAA,CAACC,WAAW,CAAC4C,QAAQ;IAACC,KAAK,EAAE;MAACnC,UAAU;MAAEL,aAAa;MAACW;IAAO,CAAE;IAAAd,QAAA,EAAEA;EAAQ;IAAA4C,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAuB,CAAC;AAC5G;AAAC9C,EAAA,CA7MQF,kBAAkB;EAAA,QACRT,WAAW,EAITC,WAAW,EAIdA,WAAW;AAAA;AAAAyD,EAAA,GATpBjD,kBAAkB;AA+M3BA,kBAAkB,CAACkD,SAAS,GAAG;EAC7BjD,QAAQ,EAAEjB,SAAS,CAACmE;AACtB,CAAC;AAED,eAAenD,kBAAkB;AAAC,IAAAiD,EAAA;AAAAG,YAAA,CAAAH,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}