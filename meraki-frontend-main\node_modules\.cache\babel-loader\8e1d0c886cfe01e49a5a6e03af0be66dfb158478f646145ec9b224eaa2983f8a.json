{"ast": null, "code": "var _jsxFileName = \"E:\\\\Suraj Patil\\\\Organization_Management_System\\\\meraki-frontend-main\\\\src\\\\screens\\\\Dashboard\\\\components\\\\TimelineRequest.jsx\",\n  _s = $RefreshSig$();\nimport React, { useEffect, useState } from \"react\";\nimport { Dialog, DialogTitle, DialogContent, DialogActions, Button, TextField, MenuItem, Select, FormControl, Grid, Box, Typography } from \"@mui/material\";\nimport PropTypes from \"prop-types\";\nimport { useDispatch, useSelector } from \"react-redux\";\nimport { TimelineActions } from \"slices/actions\";\nimport { UserSelector, ProductSelector } from \"selectors\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction TimelineRequest({\n  startTime,\n  endTime,\n  addRequest,\n  setAddRequest,\n  fromRequest\n}) {\n  _s();\n  const profile = useSelector(UserSelector.profile());\n  const products = useSelector(ProductSelector.getOnGoingProductsTasksToday());\n  const [open, setOpen] = useState(false);\n  const [fromHour, setFromHour] = useState(12);\n  const [fromMinute, setFromMinute] = useState(0);\n  const [fromPeriod, setFromPeriod] = useState(\"PM\");\n  const [fromSeconds, setFromSeconds] = useState(0);\n  const [toSeconds, setToSeconds] = useState(0);\n  const [toHour, setToHour] = useState(12);\n  const [toMinute, setToMinute] = useState(0);\n  const [toPeriod, setToPeriod] = useState(\"PM\");\n  const [description, setDescription] = useState(\"\");\n  const [requestType, setRequestType] = useState(\"Productive\");\n  const [task, setTask] = useState({});\n  const dispatch = useDispatch();\n  useEffect(() => {\n    console.log(\"Products Timeline Task \", task);\n  }, [task]);\n  useEffect(() => {\n    setOpen(addRequest);\n    setFromHour(startTime.getHours() === 12 ? 12 : startTime.getHours() % 12);\n    setFromMinute(startTime.getMinutes());\n    setToHour(endTime.getHours() === 12 ? 12 : endTime.getHours() % 12);\n    setToMinute(endTime.getMinutes());\n    setFromSeconds(startTime.getSeconds());\n    setToSeconds(endTime.getSeconds());\n    setFromPeriod(startTime.getHours() >= 12 ? \"PM\" : \"AM\");\n    setToPeriod(endTime.getHours() >= 12 ? \"PM\" : \"AM\");\n  }, []);\n  const PmAmSetting = (str, period) => {\n    if (period === \"AM\") {\n      return str;\n    }\n    return str === 12 ? 12 : str + 12;\n  };\n  const handleClose = () => {\n    setAddRequest(false);\n  };\n  const makeRequestFunction = () => {\n    if ((startTime.getHours() === 12 ? 12 : startTime.getHours() % 12) === fromHour && startTime.getMinutes() === fromMinute && (endTime.getHours() === 12 ? 12 : endTime.getHours() % 12) === toHour && endTime.getMinutes() === toMinute) {\n      dispatch(TimelineActions.createTimelineRequest({\n        fromTime: startTime,\n        toTime: endTime,\n        description: description,\n        taskDetails: task,\n        userName: profile.name,\n        requestFrom: fromRequest,\n        user: profile._id\n      }));\n    } else {\n      dispatch(TimelineActions.createTimelineRequest({\n        fromTime: new Date(new Date().setHours(PmAmSetting(fromHour, fromPeriod), fromMinute, fromSeconds, 0)),\n        toTime: new Date(new Date().setHours(PmAmSetting(toHour, toPeriod), toMinute, toSeconds, 0)),\n        description: description,\n        taskDetails: task,\n        userName: profile.name,\n        requestFrom: fromRequest,\n        user: profile._id\n      }));\n    }\n    setAddRequest(false);\n  };\n  return /*#__PURE__*/_jsxDEV(Dialog, {\n    open: open,\n    onClose: handleClose,\n    maxWidth: \"sm\",\n    fullWidth: true,\n    children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n      children: \"Add Time Request\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 123,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n      children: [/*#__PURE__*/_jsxDEV(Box, {\n        mb: 2,\n        children: /*#__PURE__*/_jsxDEV(Grid, {\n          container: true,\n          spacing: 3,\n          children: [/*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            sm: 6,\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              children: \"From\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 128,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Grid, {\n              container: true,\n              spacing: 2,\n              children: [/*#__PURE__*/_jsxDEV(Grid, {\n                item: true,\n                xs: 4,\n                children: /*#__PURE__*/_jsxDEV(FormControl, {\n                  sx: {\n                    minWidth: 80\n                  },\n                  children: /*#__PURE__*/_jsxDEV(Select, {\n                    value: fromHour,\n                    onChange: e => setFromHour(e.target.value),\n                    children: [...Array(12).keys()].map(i => /*#__PURE__*/_jsxDEV(MenuItem, {\n                      value: i + 1,\n                      children: i + 1\n                    }, i + 1, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 137,\n                      columnNumber: 25\n                    }, this))\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 132,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 131,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 130,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Grid, {\n                item: true,\n                xs: 4,\n                children: /*#__PURE__*/_jsxDEV(FormControl, {\n                  sx: {\n                    minWidth: 80\n                  },\n                  children: /*#__PURE__*/_jsxDEV(Select, {\n                    value: fromMinute,\n                    onChange: e => setFromMinute(e.target.value),\n                    children: [...Array(60).keys()].map(i => /*#__PURE__*/_jsxDEV(MenuItem, {\n                      value: i,\n                      children: i < 10 ? `0${i}` : i\n                    }, i, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 151,\n                      columnNumber: 25\n                    }, this))\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 146,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 145,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 144,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Grid, {\n                item: true,\n                xs: 4,\n                children: /*#__PURE__*/_jsxDEV(FormControl, {\n                  sx: {\n                    minWidth: 80\n                  },\n                  children: /*#__PURE__*/_jsxDEV(Select, {\n                    value: fromPeriod,\n                    onChange: e => setFromPeriod(e.target.value),\n                    children: [/*#__PURE__*/_jsxDEV(MenuItem, {\n                      value: \"AM\",\n                      children: \"AM\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 164,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                      value: \"PM\",\n                      children: \"PM\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 165,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 160,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 159,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 158,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 129,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 127,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            sm: 6,\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              children: \"To\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 172,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Grid, {\n              container: true,\n              spacing: 2,\n              children: [/*#__PURE__*/_jsxDEV(Grid, {\n                item: true,\n                xs: 4,\n                children: /*#__PURE__*/_jsxDEV(FormControl, {\n                  sx: {\n                    minWidth: 80\n                  },\n                  children: /*#__PURE__*/_jsxDEV(Select, {\n                    value: toHour,\n                    onChange: e => setToHour(e.target.value),\n                    children: [...Array(12).keys()].map(i => /*#__PURE__*/_jsxDEV(MenuItem, {\n                      value: i + 1,\n                      children: i + 1\n                    }, i + 1, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 181,\n                      columnNumber: 25\n                    }, this))\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 176,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 175,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 174,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Grid, {\n                item: true,\n                xs: 4,\n                children: /*#__PURE__*/_jsxDEV(FormControl, {\n                  sx: {\n                    minWidth: 80\n                  },\n                  children: /*#__PURE__*/_jsxDEV(Select, {\n                    value: toMinute,\n                    onChange: e => setToMinute(e.target.value),\n                    children: [...Array(60).keys()].map(i => /*#__PURE__*/_jsxDEV(MenuItem, {\n                      value: i,\n                      children: i < 10 ? `0${i}` : i\n                    }, i, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 195,\n                      columnNumber: 25\n                    }, this))\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 190,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 189,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 188,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Grid, {\n                item: true,\n                xs: 4,\n                children: /*#__PURE__*/_jsxDEV(FormControl, {\n                  sx: {\n                    minWidth: 80\n                  },\n                  children: /*#__PURE__*/_jsxDEV(Select, {\n                    value: toPeriod,\n                    onChange: e => setToPeriod(e.target.value),\n                    children: [/*#__PURE__*/_jsxDEV(MenuItem, {\n                      value: \"AM\",\n                      children: \"AM\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 208,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                      value: \"PM\",\n                      children: \"PM\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 209,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 204,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 203,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 202,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 173,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 171,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 126,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 125,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Box, {\n        mb: 2,\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          children: \"Description\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 219,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(TextField, {\n          fullWidth: true,\n          margin: \"normal\",\n          label: \"Description\",\n          value: description,\n          onChange: e => setDescription(e.target.value)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 220,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 218,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Box, {\n        mb: 2,\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          children: \"Request Type\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 230,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(FormControl, {\n          fullWidth: true,\n          margin: \"normal\",\n          children: /*#__PURE__*/_jsxDEV(Select, {\n            value: requestType,\n            onChange: e => setRequestType(e.target.value),\n            children: [/*#__PURE__*/_jsxDEV(MenuItem, {\n              value: \"Productive\",\n              children: \"Productive\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 236,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n              value: \"Non-Productive\",\n              children: \"Non-Productive\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 237,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 232,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 231,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 229,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Box, {\n        mb: 2,\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          children: \"Task\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 243,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(FormControl, {\n          fullWidth: true,\n          margin: \"normal\",\n          children: /*#__PURE__*/_jsxDEV(Select, {\n            value: task.taskTitle,\n            onChange: e => setTask({\n              taskTitle: e.target.value,\n              userName: profile.name,\n              taskStatus: \"pending\"\n            }),\n            label: \"Task\",\n            children: (products === null || products === void 0 ? void 0 : products.length) > 0 ?\n            // Flatten all taskArr items into a single list\n            products.flatMap(product => {\n              var _product$taskArr;\n              return ((_product$taskArr = product.taskArr) === null || _product$taskArr === void 0 ? void 0 : _product$taskArr.map(task => /*#__PURE__*/_jsxDEV(MenuItem, {\n                value: task.taskTitle,\n                children: task.taskTitle\n              }, `${product.id}-${task.taskTitle}`, false, {\n                fileName: _jsxFileName,\n                lineNumber: 260,\n                columnNumber: 23\n              }, this))) || [];\n            } // Fallback if taskArr is undefined\n            ) : /*#__PURE__*/_jsxDEV(MenuItem, {\n              disabled: true,\n              children: \"No tasks available\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 269,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 245,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 244,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 242,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 124,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(DialogActions, {\n      children: [/*#__PURE__*/_jsxDEV(Button, {\n        onClick: handleClose,\n        color: \"primary\",\n        children: \"Close\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 277,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        onClick: makeRequestFunction,\n        color: \"primary\",\n        children: \"Save\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 280,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 276,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 122,\n    columnNumber: 5\n  }, this);\n}\n_s(TimelineRequest, \"/hD0MSGfI0Yp9dwh5M8GUdysLuQ=\", false, function () {\n  return [useSelector, useSelector, useDispatch];\n});\n_c = TimelineRequest;\nTimelineRequest.propTypes = {\n  startTime: PropTypes.object,\n  endTime: PropTypes.object,\n  addRequest: PropTypes.bool,\n  fromRequest: PropTypes.object,\n  setAddRequest: PropTypes.func\n};\nexport default TimelineRequest;\nvar _c;\n$RefreshReg$(_c, \"TimelineRequest\");", "map": {"version": 3, "names": ["React", "useEffect", "useState", "Dialog", "DialogTitle", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DialogActions", "<PERSON><PERSON>", "TextField", "MenuItem", "Select", "FormControl", "Grid", "Box", "Typography", "PropTypes", "useDispatch", "useSelector", "TimelineActions", "UserSelector", "ProductSelector", "jsxDEV", "_jsxDEV", "TimelineRequest", "startTime", "endTime", "addRequest", "setAddRequest", "fromRequest", "_s", "profile", "products", "getOnGoingProductsTasksToday", "open", "<PERSON><PERSON><PERSON>", "fromHour", "setFromHour", "fromMinute", "setFromMinute", "fromPeriod", "setFromPeriod", "fromSeconds", "setFromSeconds", "to<PERSON><PERSON><PERSON><PERSON>", "setToSeconds", "toHour", "setToHour", "toMinute", "setToMinute", "<PERSON><PERSON><PERSON><PERSON>", "setToPeriod", "description", "setDescription", "requestType", "setRequestType", "task", "setTask", "dispatch", "console", "log", "getHours", "getMinutes", "getSeconds", "PmAmSetting", "str", "period", "handleClose", "makeRequestFunction", "createTimelineRequest", "fromTime", "toTime", "taskDetails", "userName", "name", "requestFrom", "user", "_id", "Date", "setHours", "onClose", "max<PERSON><PERSON><PERSON>", "fullWidth", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "mb", "container", "spacing", "item", "xs", "sm", "sx", "min<PERSON><PERSON><PERSON>", "value", "onChange", "e", "target", "Array", "keys", "map", "i", "margin", "label", "taskTitle", "taskStatus", "length", "flatMap", "product", "_product$taskArr", "taskArr", "id", "disabled", "onClick", "color", "_c", "propTypes", "object", "bool", "func", "$RefreshReg$"], "sources": ["E:/Suraj Patil/Organization_Management_System/meraki-frontend-main/src/screens/Dashboard/components/TimelineRequest.jsx"], "sourcesContent": ["import React, { useEffect, useState } from \"react\";\r\nimport {\r\n  <PERSON><PERSON>,\r\n  <PERSON><PERSON>T<PERSON>le,\r\n  DialogContent,\r\n  <PERSON>alog<PERSON>ctions,\r\n  Button,\r\n  TextField,\r\n  MenuItem,\r\n  Select,\r\n  FormControl,\r\n  Grid,\r\n  Box,\r\n  Typography,\r\n} from \"@mui/material\";\r\nimport PropTypes from \"prop-types\";\r\nimport { useDispatch, useSelector } from \"react-redux\";\r\nimport { TimelineActions } from \"slices/actions\";\r\nimport { UserSelector, ProductSelector } from \"selectors\";\r\n\r\nfunction TimelineRequest({\r\n  startTime,\r\n  endTime,\r\n  addRequest,\r\n  setAddRequest,\r\n  fromRequest,\r\n}) {\r\n  const profile = useSelector(UserSelector.profile());\r\n  const products = useSelector(ProductSelector.getOnGoingProductsTasksToday());\r\n  const [open, setOpen] = useState(false);\r\n  const [fromHour, setFromHour] = useState(12);\r\n  const [fromMinute, setFromMinute] = useState(0);\r\n  const [fromPeriod, setFromPeriod] = useState(\"PM\");\r\n  const [from<PERSON><PERSON>onds, setFromSeconds] = useState(0);\r\n  const [toSeconds, setToSeconds] = useState(0);\r\n  const [toHour, setToHour] = useState(12);\r\n  const [toMinute, setToMinute] = useState(0);\r\n  const [toPeriod, setToPeriod] = useState(\"PM\");\r\n  const [description, setDescription] = useState(\"\");\r\n  const [requestType, setRequestType] = useState(\"Productive\");\r\n  const [task, setTask] = useState({});\r\n  const dispatch = useDispatch();\r\n\r\n  useEffect(() => {\r\n    console.log(\"Products Timeline Task \", task);\r\n  }, [task]);\r\n\r\n  useEffect(() => {\r\n    setOpen(addRequest);\r\n    setFromHour(startTime.getHours() === 12 ? 12 : startTime.getHours() % 12);\r\n    setFromMinute(startTime.getMinutes());\r\n    setToHour(endTime.getHours() === 12 ? 12 : endTime.getHours() % 12);\r\n    setToMinute(endTime.getMinutes());\r\n    setFromSeconds(startTime.getSeconds());\r\n    setToSeconds(endTime.getSeconds());\r\n    setFromPeriod(startTime.getHours() >= 12 ? \"PM\" : \"AM\");\r\n    setToPeriod(endTime.getHours() >= 12 ? \"PM\" : \"AM\");\r\n  }, []);\r\n\r\n  const PmAmSetting = (str, period) => {\r\n    if (period === \"AM\") {\r\n      return str;\r\n    }\r\n    return str === 12 ? 12 : str + 12;\r\n  };\r\n\r\n  const handleClose = () => {\r\n    setAddRequest(false);\r\n  };\r\n\r\n  const makeRequestFunction = () => {\r\n    if (\r\n      (startTime.getHours() === 12 ? 12 : startTime.getHours() % 12) ===\r\n        fromHour &&\r\n      startTime.getMinutes() === fromMinute &&\r\n      (endTime.getHours() === 12 ? 12 : endTime.getHours() % 12) === toHour &&\r\n      endTime.getMinutes() === toMinute\r\n    ) {\r\n      dispatch(\r\n        TimelineActions.createTimelineRequest({\r\n          fromTime: startTime,\r\n          toTime: endTime,\r\n          description: description,\r\n          taskDetails: task,\r\n          userName: profile.name,\r\n          requestFrom: fromRequest,\r\n          user: profile._id,\r\n        })\r\n      );\r\n    } else {\r\n      dispatch(\r\n        TimelineActions.createTimelineRequest({\r\n          fromTime: new Date(\r\n            new Date().setHours(\r\n              PmAmSetting(fromHour, fromPeriod),\r\n              fromMinute,\r\n              fromSeconds,\r\n              0\r\n            )\r\n          ),\r\n          toTime: new Date(\r\n            new Date().setHours(\r\n              PmAmSetting(toHour, toPeriod),\r\n              toMinute,\r\n              toSeconds,\r\n              0\r\n            )\r\n          ),\r\n          description: description,\r\n          taskDetails: task,\r\n          userName: profile.name,\r\n          requestFrom: fromRequest,\r\n          user: profile._id,\r\n        })\r\n      );\r\n    }\r\n\r\n    setAddRequest(false);\r\n  };\r\n\r\n  return (\r\n    <Dialog open={open} onClose={handleClose} maxWidth=\"sm\" fullWidth>\r\n      <DialogTitle>Add Time Request</DialogTitle>\r\n      <DialogContent>\r\n        <Box mb={2}>\r\n          <Grid container spacing={3}>\r\n            <Grid item xs={12} sm={6}>\r\n              <Typography>From</Typography>\r\n              <Grid container spacing={2}>\r\n                <Grid item xs={4}>\r\n                  <FormControl sx={{ minWidth: 80 }}>\r\n                    <Select\r\n                      value={fromHour}\r\n                      onChange={(e) => setFromHour(e.target.value)}\r\n                    >\r\n                      {[...Array(12).keys()].map((i) => (\r\n                        <MenuItem key={i + 1} value={i + 1}>\r\n                          {i + 1}\r\n                        </MenuItem>\r\n                      ))}\r\n                    </Select>\r\n                  </FormControl>\r\n                </Grid>\r\n                <Grid item xs={4}>\r\n                  <FormControl sx={{ minWidth: 80 }}>\r\n                    <Select\r\n                      value={fromMinute}\r\n                      onChange={(e) => setFromMinute(e.target.value)}\r\n                    >\r\n                      {[...Array(60).keys()].map((i) => (\r\n                        <MenuItem key={i} value={i}>\r\n                          {i < 10 ? `0${i}` : i}\r\n                        </MenuItem>\r\n                      ))}\r\n                    </Select>\r\n                  </FormControl>\r\n                </Grid>\r\n                <Grid item xs={4}>\r\n                  <FormControl sx={{ minWidth: 80 }}>\r\n                    <Select\r\n                      value={fromPeriod}\r\n                      onChange={(e) => setFromPeriod(e.target.value)}\r\n                    >\r\n                      <MenuItem value=\"AM\">AM</MenuItem>\r\n                      <MenuItem value=\"PM\">PM</MenuItem>\r\n                    </Select>\r\n                  </FormControl>\r\n                </Grid>\r\n              </Grid>\r\n            </Grid>\r\n            <Grid item xs={12} sm={6}>\r\n              <Typography>To</Typography>\r\n              <Grid container spacing={2}>\r\n                <Grid item xs={4}>\r\n                  <FormControl sx={{ minWidth: 80 }}>\r\n                    <Select\r\n                      value={toHour}\r\n                      onChange={(e) => setToHour(e.target.value)}\r\n                    >\r\n                      {[...Array(12).keys()].map((i) => (\r\n                        <MenuItem key={i + 1} value={i + 1}>\r\n                          {i + 1}\r\n                        </MenuItem>\r\n                      ))}\r\n                    </Select>\r\n                  </FormControl>\r\n                </Grid>\r\n                <Grid item xs={4}>\r\n                  <FormControl sx={{ minWidth: 80 }}>\r\n                    <Select\r\n                      value={toMinute}\r\n                      onChange={(e) => setToMinute(e.target.value)}\r\n                    >\r\n                      {[...Array(60).keys()].map((i) => (\r\n                        <MenuItem key={i} value={i}>\r\n                          {i < 10 ? `0${i}` : i}\r\n                        </MenuItem>\r\n                      ))}\r\n                    </Select>\r\n                  </FormControl>\r\n                </Grid>\r\n                <Grid item xs={4}>\r\n                  <FormControl sx={{ minWidth: 80 }}>\r\n                    <Select\r\n                      value={toPeriod}\r\n                      onChange={(e) => setToPeriod(e.target.value)}\r\n                    >\r\n                      <MenuItem value=\"AM\">AM</MenuItem>\r\n                      <MenuItem value=\"PM\">PM</MenuItem>\r\n                    </Select>\r\n                  </FormControl>\r\n                </Grid>\r\n              </Grid>\r\n            </Grid>\r\n          </Grid>\r\n        </Box>\r\n\r\n        <Box mb={2}>\r\n          <Typography>Description</Typography>\r\n          <TextField\r\n            fullWidth\r\n            margin=\"normal\"\r\n            label=\"Description\"\r\n            value={description}\r\n            onChange={(e) => setDescription(e.target.value)}\r\n          />\r\n        </Box>\r\n\r\n        <Box mb={2}>\r\n          <Typography>Request Type</Typography>\r\n          <FormControl fullWidth margin=\"normal\">\r\n            <Select\r\n              value={requestType}\r\n              onChange={(e) => setRequestType(e.target.value)}\r\n            >\r\n              <MenuItem value=\"Productive\">Productive</MenuItem>\r\n              <MenuItem value=\"Non-Productive\">Non-Productive</MenuItem>\r\n            </Select>\r\n          </FormControl>\r\n        </Box>\r\n\r\n        <Box mb={2}>\r\n            <Typography>Task</Typography>\r\n          <FormControl fullWidth margin=\"normal\">\r\n            <Select\r\n              value={task.taskTitle}\r\n              onChange={(e) => setTask({\r\n                taskTitle: e.target.value,\r\n                userName: profile.name,\r\n                taskStatus: \"pending\",\r\n              \r\n              })}\r\n              label=\"Task\"\r\n            >\r\n              {products?.length > 0 ? (\r\n                // Flatten all taskArr items into a single list\r\n                products.flatMap(\r\n                  (product) =>\r\n                    product.taskArr?.map((task) => (\r\n                      <MenuItem\r\n                        value={task.taskTitle}\r\n                        key={`${product.id}-${task.taskTitle}`} // Better key\r\n                      >\r\n                        {task.taskTitle}\r\n                      </MenuItem>\r\n                    )) || [] // Fallback if taskArr is undefined\r\n                )\r\n              ) : (\r\n                <MenuItem disabled>No tasks available</MenuItem>\r\n              )}\r\n            </Select>\r\n          </FormControl>\r\n        </Box>\r\n      </DialogContent>\r\n\r\n      <DialogActions>\r\n        <Button onClick={handleClose} color=\"primary\">\r\n          Close\r\n        </Button>\r\n        <Button onClick={makeRequestFunction} color=\"primary\">\r\n          Save\r\n        </Button>\r\n      </DialogActions>\r\n    </Dialog>\r\n  );\r\n}\r\n\r\nTimelineRequest.propTypes = {\r\n  startTime: PropTypes.object,\r\n  endTime: PropTypes.object,\r\n  addRequest: PropTypes.bool,\r\n  fromRequest: PropTypes.object,\r\n  setAddRequest: PropTypes.func,\r\n};\r\n\r\nexport default TimelineRequest;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAClD,SACEC,MAAM,EACNC,WAAW,EACXC,aAAa,EACbC,aAAa,EACbC,MAAM,EACNC,SAAS,EACTC,QAAQ,EACRC,MAAM,EACNC,WAAW,EACXC,IAAI,EACJC,GAAG,EACHC,UAAU,QACL,eAAe;AACtB,OAAOC,SAAS,MAAM,YAAY;AAClC,SAASC,WAAW,EAAEC,WAAW,QAAQ,aAAa;AACtD,SAASC,eAAe,QAAQ,gBAAgB;AAChD,SAASC,YAAY,EAAEC,eAAe,QAAQ,WAAW;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE1D,SAASC,eAAeA,CAAC;EACvBC,SAAS;EACTC,OAAO;EACPC,UAAU;EACVC,aAAa;EACbC;AACF,CAAC,EAAE;EAAAC,EAAA;EACD,MAAMC,OAAO,GAAGb,WAAW,CAACE,YAAY,CAACW,OAAO,CAAC,CAAC,CAAC;EACnD,MAAMC,QAAQ,GAAGd,WAAW,CAACG,eAAe,CAACY,4BAA4B,CAAC,CAAC,CAAC;EAC5E,MAAM,CAACC,IAAI,EAAEC,OAAO,CAAC,GAAGhC,QAAQ,CAAC,KAAK,CAAC;EACvC,MAAM,CAACiC,QAAQ,EAAEC,WAAW,CAAC,GAAGlC,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAACmC,UAAU,EAAEC,aAAa,CAAC,GAAGpC,QAAQ,CAAC,CAAC,CAAC;EAC/C,MAAM,CAACqC,UAAU,EAAEC,aAAa,CAAC,GAAGtC,QAAQ,CAAC,IAAI,CAAC;EAClD,MAAM,CAACuC,WAAW,EAAEC,cAAc,CAAC,GAAGxC,QAAQ,CAAC,CAAC,CAAC;EACjD,MAAM,CAACyC,SAAS,EAAEC,YAAY,CAAC,GAAG1C,QAAQ,CAAC,CAAC,CAAC;EAC7C,MAAM,CAAC2C,MAAM,EAAEC,SAAS,CAAC,GAAG5C,QAAQ,CAAC,EAAE,CAAC;EACxC,MAAM,CAAC6C,QAAQ,EAAEC,WAAW,CAAC,GAAG9C,QAAQ,CAAC,CAAC,CAAC;EAC3C,MAAM,CAAC+C,QAAQ,EAAEC,WAAW,CAAC,GAAGhD,QAAQ,CAAC,IAAI,CAAC;EAC9C,MAAM,CAACiD,WAAW,EAAEC,cAAc,CAAC,GAAGlD,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAACmD,WAAW,EAAEC,cAAc,CAAC,GAAGpD,QAAQ,CAAC,YAAY,CAAC;EAC5D,MAAM,CAACqD,IAAI,EAAEC,OAAO,CAAC,GAAGtD,QAAQ,CAAC,CAAC,CAAC,CAAC;EACpC,MAAMuD,QAAQ,GAAGzC,WAAW,CAAC,CAAC;EAE9Bf,SAAS,CAAC,MAAM;IACdyD,OAAO,CAACC,GAAG,CAAC,yBAAyB,EAAEJ,IAAI,CAAC;EAC9C,CAAC,EAAE,CAACA,IAAI,CAAC,CAAC;EAEVtD,SAAS,CAAC,MAAM;IACdiC,OAAO,CAACR,UAAU,CAAC;IACnBU,WAAW,CAACZ,SAAS,CAACoC,QAAQ,CAAC,CAAC,KAAK,EAAE,GAAG,EAAE,GAAGpC,SAAS,CAACoC,QAAQ,CAAC,CAAC,GAAG,EAAE,CAAC;IACzEtB,aAAa,CAACd,SAAS,CAACqC,UAAU,CAAC,CAAC,CAAC;IACrCf,SAAS,CAACrB,OAAO,CAACmC,QAAQ,CAAC,CAAC,KAAK,EAAE,GAAG,EAAE,GAAGnC,OAAO,CAACmC,QAAQ,CAAC,CAAC,GAAG,EAAE,CAAC;IACnEZ,WAAW,CAACvB,OAAO,CAACoC,UAAU,CAAC,CAAC,CAAC;IACjCnB,cAAc,CAAClB,SAAS,CAACsC,UAAU,CAAC,CAAC,CAAC;IACtClB,YAAY,CAACnB,OAAO,CAACqC,UAAU,CAAC,CAAC,CAAC;IAClCtB,aAAa,CAAChB,SAAS,CAACoC,QAAQ,CAAC,CAAC,IAAI,EAAE,GAAG,IAAI,GAAG,IAAI,CAAC;IACvDV,WAAW,CAACzB,OAAO,CAACmC,QAAQ,CAAC,CAAC,IAAI,EAAE,GAAG,IAAI,GAAG,IAAI,CAAC;EACrD,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMG,WAAW,GAAGA,CAACC,GAAG,EAAEC,MAAM,KAAK;IACnC,IAAIA,MAAM,KAAK,IAAI,EAAE;MACnB,OAAOD,GAAG;IACZ;IACA,OAAOA,GAAG,KAAK,EAAE,GAAG,EAAE,GAAGA,GAAG,GAAG,EAAE;EACnC,CAAC;EAED,MAAME,WAAW,GAAGA,CAAA,KAAM;IACxBvC,aAAa,CAAC,KAAK,CAAC;EACtB,CAAC;EAED,MAAMwC,mBAAmB,GAAGA,CAAA,KAAM;IAChC,IACE,CAAC3C,SAAS,CAACoC,QAAQ,CAAC,CAAC,KAAK,EAAE,GAAG,EAAE,GAAGpC,SAAS,CAACoC,QAAQ,CAAC,CAAC,GAAG,EAAE,MAC3DzB,QAAQ,IACVX,SAAS,CAACqC,UAAU,CAAC,CAAC,KAAKxB,UAAU,IACrC,CAACZ,OAAO,CAACmC,QAAQ,CAAC,CAAC,KAAK,EAAE,GAAG,EAAE,GAAGnC,OAAO,CAACmC,QAAQ,CAAC,CAAC,GAAG,EAAE,MAAMf,MAAM,IACrEpB,OAAO,CAACoC,UAAU,CAAC,CAAC,KAAKd,QAAQ,EACjC;MACAU,QAAQ,CACNvC,eAAe,CAACkD,qBAAqB,CAAC;QACpCC,QAAQ,EAAE7C,SAAS;QACnB8C,MAAM,EAAE7C,OAAO;QACf0B,WAAW,EAAEA,WAAW;QACxBoB,WAAW,EAAEhB,IAAI;QACjBiB,QAAQ,EAAE1C,OAAO,CAAC2C,IAAI;QACtBC,WAAW,EAAE9C,WAAW;QACxB+C,IAAI,EAAE7C,OAAO,CAAC8C;MAChB,CAAC,CACH,CAAC;IACH,CAAC,MAAM;MACLnB,QAAQ,CACNvC,eAAe,CAACkD,qBAAqB,CAAC;QACpCC,QAAQ,EAAE,IAAIQ,IAAI,CAChB,IAAIA,IAAI,CAAC,CAAC,CAACC,QAAQ,CACjBf,WAAW,CAAC5B,QAAQ,EAAEI,UAAU,CAAC,EACjCF,UAAU,EACVI,WAAW,EACX,CACF,CACF,CAAC;QACD6B,MAAM,EAAE,IAAIO,IAAI,CACd,IAAIA,IAAI,CAAC,CAAC,CAACC,QAAQ,CACjBf,WAAW,CAAClB,MAAM,EAAEI,QAAQ,CAAC,EAC7BF,QAAQ,EACRJ,SAAS,EACT,CACF,CACF,CAAC;QACDQ,WAAW,EAAEA,WAAW;QACxBoB,WAAW,EAAEhB,IAAI;QACjBiB,QAAQ,EAAE1C,OAAO,CAAC2C,IAAI;QACtBC,WAAW,EAAE9C,WAAW;QACxB+C,IAAI,EAAE7C,OAAO,CAAC8C;MAChB,CAAC,CACH,CAAC;IACH;IAEAjD,aAAa,CAAC,KAAK,CAAC;EACtB,CAAC;EAED,oBACEL,OAAA,CAACnB,MAAM;IAAC8B,IAAI,EAAEA,IAAK;IAAC8C,OAAO,EAAEb,WAAY;IAACc,QAAQ,EAAC,IAAI;IAACC,SAAS;IAAAC,QAAA,gBAC/D5D,OAAA,CAAClB,WAAW;MAAA8E,QAAA,EAAC;IAAgB;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAa,CAAC,eAC3ChE,OAAA,CAACjB,aAAa;MAAA6E,QAAA,gBACZ5D,OAAA,CAACT,GAAG;QAAC0E,EAAE,EAAE,CAAE;QAAAL,QAAA,eACT5D,OAAA,CAACV,IAAI;UAAC4E,SAAS;UAACC,OAAO,EAAE,CAAE;UAAAP,QAAA,gBACzB5D,OAAA,CAACV,IAAI;YAAC8E,IAAI;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAAV,QAAA,gBACvB5D,OAAA,CAACR,UAAU;cAAAoE,QAAA,EAAC;YAAI;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eAC7BhE,OAAA,CAACV,IAAI;cAAC4E,SAAS;cAACC,OAAO,EAAE,CAAE;cAAAP,QAAA,gBACzB5D,OAAA,CAACV,IAAI;gBAAC8E,IAAI;gBAACC,EAAE,EAAE,CAAE;gBAAAT,QAAA,eACf5D,OAAA,CAACX,WAAW;kBAACkF,EAAE,EAAE;oBAAEC,QAAQ,EAAE;kBAAG,CAAE;kBAAAZ,QAAA,eAChC5D,OAAA,CAACZ,MAAM;oBACLqF,KAAK,EAAE5D,QAAS;oBAChB6D,QAAQ,EAAGC,CAAC,IAAK7D,WAAW,CAAC6D,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;oBAAAb,QAAA,EAE5C,CAAC,GAAGiB,KAAK,CAAC,EAAE,CAAC,CAACC,IAAI,CAAC,CAAC,CAAC,CAACC,GAAG,CAAEC,CAAC,iBAC3BhF,OAAA,CAACb,QAAQ;sBAAasF,KAAK,EAAEO,CAAC,GAAG,CAAE;sBAAApB,QAAA,EAChCoB,CAAC,GAAG;oBAAC,GADOA,CAAC,GAAG,CAAC;sBAAAnB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAEV,CACX;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACI;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACV,CAAC,eACPhE,OAAA,CAACV,IAAI;gBAAC8E,IAAI;gBAACC,EAAE,EAAE,CAAE;gBAAAT,QAAA,eACf5D,OAAA,CAACX,WAAW;kBAACkF,EAAE,EAAE;oBAAEC,QAAQ,EAAE;kBAAG,CAAE;kBAAAZ,QAAA,eAChC5D,OAAA,CAACZ,MAAM;oBACLqF,KAAK,EAAE1D,UAAW;oBAClB2D,QAAQ,EAAGC,CAAC,IAAK3D,aAAa,CAAC2D,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;oBAAAb,QAAA,EAE9C,CAAC,GAAGiB,KAAK,CAAC,EAAE,CAAC,CAACC,IAAI,CAAC,CAAC,CAAC,CAACC,GAAG,CAAEC,CAAC,iBAC3BhF,OAAA,CAACb,QAAQ;sBAASsF,KAAK,EAAEO,CAAE;sBAAApB,QAAA,EACxBoB,CAAC,GAAG,EAAE,GAAG,IAAIA,CAAC,EAAE,GAAGA;oBAAC,GADRA,CAAC;sBAAAnB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAEN,CACX;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACI;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACV,CAAC,eACPhE,OAAA,CAACV,IAAI;gBAAC8E,IAAI;gBAACC,EAAE,EAAE,CAAE;gBAAAT,QAAA,eACf5D,OAAA,CAACX,WAAW;kBAACkF,EAAE,EAAE;oBAAEC,QAAQ,EAAE;kBAAG,CAAE;kBAAAZ,QAAA,eAChC5D,OAAA,CAACZ,MAAM;oBACLqF,KAAK,EAAExD,UAAW;oBAClByD,QAAQ,EAAGC,CAAC,IAAKzD,aAAa,CAACyD,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;oBAAAb,QAAA,gBAE/C5D,OAAA,CAACb,QAAQ;sBAACsF,KAAK,EAAC,IAAI;sBAAAb,QAAA,EAAC;oBAAE;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAU,CAAC,eAClChE,OAAA,CAACb,QAAQ;sBAACsF,KAAK,EAAC,IAAI;sBAAAb,QAAA,EAAC;oBAAE;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAU,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC5B;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACV,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eACPhE,OAAA,CAACV,IAAI;YAAC8E,IAAI;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAAV,QAAA,gBACvB5D,OAAA,CAACR,UAAU;cAAAoE,QAAA,EAAC;YAAE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eAC3BhE,OAAA,CAACV,IAAI;cAAC4E,SAAS;cAACC,OAAO,EAAE,CAAE;cAAAP,QAAA,gBACzB5D,OAAA,CAACV,IAAI;gBAAC8E,IAAI;gBAACC,EAAE,EAAE,CAAE;gBAAAT,QAAA,eACf5D,OAAA,CAACX,WAAW;kBAACkF,EAAE,EAAE;oBAAEC,QAAQ,EAAE;kBAAG,CAAE;kBAAAZ,QAAA,eAChC5D,OAAA,CAACZ,MAAM;oBACLqF,KAAK,EAAElD,MAAO;oBACdmD,QAAQ,EAAGC,CAAC,IAAKnD,SAAS,CAACmD,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;oBAAAb,QAAA,EAE1C,CAAC,GAAGiB,KAAK,CAAC,EAAE,CAAC,CAACC,IAAI,CAAC,CAAC,CAAC,CAACC,GAAG,CAAEC,CAAC,iBAC3BhF,OAAA,CAACb,QAAQ;sBAAasF,KAAK,EAAEO,CAAC,GAAG,CAAE;sBAAApB,QAAA,EAChCoB,CAAC,GAAG;oBAAC,GADOA,CAAC,GAAG,CAAC;sBAAAnB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAEV,CACX;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACI;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACV,CAAC,eACPhE,OAAA,CAACV,IAAI;gBAAC8E,IAAI;gBAACC,EAAE,EAAE,CAAE;gBAAAT,QAAA,eACf5D,OAAA,CAACX,WAAW;kBAACkF,EAAE,EAAE;oBAAEC,QAAQ,EAAE;kBAAG,CAAE;kBAAAZ,QAAA,eAChC5D,OAAA,CAACZ,MAAM;oBACLqF,KAAK,EAAEhD,QAAS;oBAChBiD,QAAQ,EAAGC,CAAC,IAAKjD,WAAW,CAACiD,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;oBAAAb,QAAA,EAE5C,CAAC,GAAGiB,KAAK,CAAC,EAAE,CAAC,CAACC,IAAI,CAAC,CAAC,CAAC,CAACC,GAAG,CAAEC,CAAC,iBAC3BhF,OAAA,CAACb,QAAQ;sBAASsF,KAAK,EAAEO,CAAE;sBAAApB,QAAA,EACxBoB,CAAC,GAAG,EAAE,GAAG,IAAIA,CAAC,EAAE,GAAGA;oBAAC,GADRA,CAAC;sBAAAnB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAEN,CACX;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACI;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACV,CAAC,eACPhE,OAAA,CAACV,IAAI;gBAAC8E,IAAI;gBAACC,EAAE,EAAE,CAAE;gBAAAT,QAAA,eACf5D,OAAA,CAACX,WAAW;kBAACkF,EAAE,EAAE;oBAAEC,QAAQ,EAAE;kBAAG,CAAE;kBAAAZ,QAAA,eAChC5D,OAAA,CAACZ,MAAM;oBACLqF,KAAK,EAAE9C,QAAS;oBAChB+C,QAAQ,EAAGC,CAAC,IAAK/C,WAAW,CAAC+C,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;oBAAAb,QAAA,gBAE7C5D,OAAA,CAACb,QAAQ;sBAACsF,KAAK,EAAC,IAAI;sBAAAb,QAAA,EAAC;oBAAE;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAU,CAAC,eAClChE,OAAA,CAACb,QAAQ;sBAACsF,KAAK,EAAC,IAAI;sBAAAb,QAAA,EAAC;oBAAE;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAU,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC5B;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACV,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eAENhE,OAAA,CAACT,GAAG;QAAC0E,EAAE,EAAE,CAAE;QAAAL,QAAA,gBACT5D,OAAA,CAACR,UAAU;UAAAoE,QAAA,EAAC;QAAW;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eACpChE,OAAA,CAACd,SAAS;UACRyE,SAAS;UACTsB,MAAM,EAAC,QAAQ;UACfC,KAAK,EAAC,aAAa;UACnBT,KAAK,EAAE5C,WAAY;UACnB6C,QAAQ,EAAGC,CAAC,IAAK7C,cAAc,CAAC6C,CAAC,CAACC,MAAM,CAACH,KAAK;QAAE;UAAAZ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eAENhE,OAAA,CAACT,GAAG;QAAC0E,EAAE,EAAE,CAAE;QAAAL,QAAA,gBACT5D,OAAA,CAACR,UAAU;UAAAoE,QAAA,EAAC;QAAY;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eACrChE,OAAA,CAACX,WAAW;UAACsE,SAAS;UAACsB,MAAM,EAAC,QAAQ;UAAArB,QAAA,eACpC5D,OAAA,CAACZ,MAAM;YACLqF,KAAK,EAAE1C,WAAY;YACnB2C,QAAQ,EAAGC,CAAC,IAAK3C,cAAc,CAAC2C,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;YAAAb,QAAA,gBAEhD5D,OAAA,CAACb,QAAQ;cAACsF,KAAK,EAAC,YAAY;cAAAb,QAAA,EAAC;YAAU;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAU,CAAC,eAClDhE,OAAA,CAACb,QAAQ;cAACsF,KAAK,EAAC,gBAAgB;cAAAb,QAAA,EAAC;YAAc;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAU,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpD;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACX,CAAC,eAENhE,OAAA,CAACT,GAAG;QAAC0E,EAAE,EAAE,CAAE;QAAAL,QAAA,gBACP5D,OAAA,CAACR,UAAU;UAAAoE,QAAA,EAAC;QAAI;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eAC/BhE,OAAA,CAACX,WAAW;UAACsE,SAAS;UAACsB,MAAM,EAAC,QAAQ;UAAArB,QAAA,eACpC5D,OAAA,CAACZ,MAAM;YACLqF,KAAK,EAAExC,IAAI,CAACkD,SAAU;YACtBT,QAAQ,EAAGC,CAAC,IAAKzC,OAAO,CAAC;cACvBiD,SAAS,EAAER,CAAC,CAACC,MAAM,CAACH,KAAK;cACzBvB,QAAQ,EAAE1C,OAAO,CAAC2C,IAAI;cACtBiC,UAAU,EAAE;YAEd,CAAC,CAAE;YACHF,KAAK,EAAC,MAAM;YAAAtB,QAAA,EAEX,CAAAnD,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAE4E,MAAM,IAAG,CAAC;YACnB;YACA5E,QAAQ,CAAC6E,OAAO,CACbC,OAAO;cAAA,IAAAC,gBAAA;cAAA,OACN,EAAAA,gBAAA,GAAAD,OAAO,CAACE,OAAO,cAAAD,gBAAA,uBAAfA,gBAAA,CAAiBT,GAAG,CAAE9C,IAAI,iBACxBjC,OAAA,CAACb,QAAQ;gBACPsF,KAAK,EAAExC,IAAI,CAACkD,SAAU;gBAAAvB,QAAA,EAGrB3B,IAAI,CAACkD;cAAS,GAFV,GAAGI,OAAO,CAACG,EAAE,IAAIzD,IAAI,CAACkD,SAAS,EAAE;gBAAAtB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAG9B,CACX,CAAC,KAAI,EAAE;YAAA,EAAC;YACb,CAAC,gBAEDhE,OAAA,CAACb,QAAQ;cAACwG,QAAQ;cAAA/B,QAAA,EAAC;YAAkB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAU;UAChD;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACK;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACX,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACO,CAAC,eAEhBhE,OAAA,CAAChB,aAAa;MAAA4E,QAAA,gBACZ5D,OAAA,CAACf,MAAM;QAAC2G,OAAO,EAAEhD,WAAY;QAACiD,KAAK,EAAC,SAAS;QAAAjC,QAAA,EAAC;MAE9C;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eACThE,OAAA,CAACf,MAAM;QAAC2G,OAAO,EAAE/C,mBAAoB;QAACgD,KAAK,EAAC,SAAS;QAAAjC,QAAA,EAAC;MAEtD;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACI,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACV,CAAC;AAEb;AAACzD,EAAA,CAzQQN,eAAe;EAAA,QAONN,WAAW,EACVA,WAAW,EAaXD,WAAW;AAAA;AAAAoG,EAAA,GArBrB7F,eAAe;AA2QxBA,eAAe,CAAC8F,SAAS,GAAG;EAC1B7F,SAAS,EAAET,SAAS,CAACuG,MAAM;EAC3B7F,OAAO,EAAEV,SAAS,CAACuG,MAAM;EACzB5F,UAAU,EAAEX,SAAS,CAACwG,IAAI;EAC1B3F,WAAW,EAAEb,SAAS,CAACuG,MAAM;EAC7B3F,aAAa,EAAEZ,SAAS,CAACyG;AAC3B,CAAC;AAED,eAAejG,eAAe;AAAC,IAAA6F,EAAA;AAAAK,YAAA,CAAAL,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}