{"ast": null, "code": "var _jsxFileName = \"E:\\\\Suraj Patil\\\\Organization_Management_System\\\\meraki-frontend-main\\\\src\\\\App.js\";\nimport React, { lazy, Suspense } from \"react\";\nimport './App.css';\nimport { ThemeProvider } from \"@mui/material\";\nimport theme from \"./theme\";\nimport { ToastContainer } from \"react-toastify\";\nimport 'react-toastify/dist/ReactToastify.css';\nimport { ConnectedRouter } from \"connected-react-router\";\nimport { history } from \"./store\";\nimport Backgroundprovider from 'screens/Dashboard/components/Backgroundprovider';\nimport LoadingScreen from \"./components/LoadingScreen\";\n\n// Lazy load routes\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Routes = /*#__PURE__*/lazy(_c = () => import(\"./routes\"));\n\n/**\r\n * Main Application Component\r\n *\r\n * This is the root component of the application that sets up:\r\n * - Theme provider for Material UI\r\n * - Toast notifications\r\n * - Router configuration with code splitting\r\n * - Background provider for consistent styling\r\n *\r\n * @returns {JSX.Element} The rendered application\r\n */\n_c2 = Routes;\nexport default function App() {\n  return /*#__PURE__*/_jsxDEV(Backgroundprovider, {\n    children: /*#__PURE__*/_jsxDEV(ThemeProvider, {\n      theme: theme,\n      children: [/*#__PURE__*/_jsxDEV(ToastContainer, {\n        autoClose: 3000,\n        position: \"top-right\",\n        hideProgressBar: true,\n        theme: \"light\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 30,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(ConnectedRouter, {\n        history: history,\n        children: /*#__PURE__*/_jsxDEV(Suspense, {\n          fallback: /*#__PURE__*/_jsxDEV(LoadingScreen, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 37,\n            columnNumber: 31\n          }, this),\n          children: /*#__PURE__*/_jsxDEV(Routes, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 38,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 37,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 36,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 29,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 28,\n    columnNumber: 5\n  }, this);\n}\n_c3 = App;\nvar _c, _c2, _c3;\n$RefreshReg$(_c, \"Routes$lazy\");\n$RefreshReg$(_c2, \"Routes\");\n$RefreshReg$(_c3, \"App\");", "map": {"version": 3, "names": ["React", "lazy", "Suspense", "ThemeProvider", "theme", "ToastContainer", "ConnectedRouter", "history", "<PERSON><PERSON><PERSON><PERSON>", "LoadingScreen", "jsxDEV", "_jsxDEV", "Routes", "_c", "_c2", "App", "children", "autoClose", "position", "hideProgressBar", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "fallback", "_c3", "$RefreshReg$"], "sources": ["E:/Suraj Patil/Organization_Management_System/meraki-frontend-main/src/App.js"], "sourcesContent": ["import React, { lazy, Suspense } from \"react\";\r\nimport './App.css';\r\nimport { ThemeProvider } from \"@mui/material\";\r\nimport theme from \"./theme\";\r\nimport { ToastContainer } from \"react-toastify\";\r\nimport 'react-toastify/dist/ReactToastify.css';\r\nimport { ConnectedRouter } from \"connected-react-router\";\r\nimport { history } from \"./store\";\r\nimport Backgroundprovider from 'screens/Dashboard/components/Backgroundprovider';\r\nimport LoadingScreen from \"./components/LoadingScreen\";\r\n\r\n// Lazy load routes\r\nconst Routes = lazy(() => import(\"./routes\"));\r\n\r\n/**\r\n * Main Application Component\r\n *\r\n * This is the root component of the application that sets up:\r\n * - Theme provider for Material UI\r\n * - Toast notifications\r\n * - Router configuration with code splitting\r\n * - Background provider for consistent styling\r\n *\r\n * @returns {JSX.Element} The rendered application\r\n */\r\nexport default function App() {\r\n  return (\r\n    <Backgroundprovider>\r\n      <ThemeProvider theme={theme}>\r\n        <ToastContainer\r\n          autoClose={3000}\r\n          position=\"top-right\"\r\n          hideProgressBar\r\n          theme=\"light\"\r\n        />\r\n        <ConnectedRouter history={history}>\r\n          <Suspense fallback={<LoadingScreen />}>\r\n            <Routes />\r\n          </Suspense>\r\n        </ConnectedRouter>\r\n      </ThemeProvider>\r\n   </Backgroundprovider>\r\n  );\r\n}"], "mappings": ";AAAA,OAAOA,KAAK,IAAIC,IAAI,EAAEC,QAAQ,QAAQ,OAAO;AAC7C,OAAO,WAAW;AAClB,SAASC,aAAa,QAAQ,eAAe;AAC7C,OAAOC,KAAK,MAAM,SAAS;AAC3B,SAASC,cAAc,QAAQ,gBAAgB;AAC/C,OAAO,uCAAuC;AAC9C,SAASC,eAAe,QAAQ,wBAAwB;AACxD,SAASC,OAAO,QAAQ,SAAS;AACjC,OAAOC,kBAAkB,MAAM,iDAAiD;AAChF,OAAOC,aAAa,MAAM,4BAA4B;;AAEtD;AAAA,SAAAC,MAAA,IAAAC,OAAA;AACA,MAAMC,MAAM,gBAAGX,IAAI,CAAAY,EAAA,GAACA,CAAA,KAAM,MAAM,CAAC,UAAU,CAAC,CAAC;;AAE7C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAVAC,GAAA,GAFMF,MAAM;AAaZ,eAAe,SAASG,GAAGA,CAAA,EAAG;EAC5B,oBACEJ,OAAA,CAACH,kBAAkB;IAAAQ,QAAA,eACjBL,OAAA,CAACR,aAAa;MAACC,KAAK,EAAEA,KAAM;MAAAY,QAAA,gBAC1BL,OAAA,CAACN,cAAc;QACbY,SAAS,EAAE,IAAK;QAChBC,QAAQ,EAAC,WAAW;QACpBC,eAAe;QACff,KAAK,EAAC;MAAO;QAAAgB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACd,CAAC,eACFZ,OAAA,CAACL,eAAe;QAACC,OAAO,EAAEA,OAAQ;QAAAS,QAAA,eAChCL,OAAA,CAACT,QAAQ;UAACsB,QAAQ,eAAEb,OAAA,CAACF,aAAa;YAAAW,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAAAP,QAAA,eACpCL,OAAA,CAACC,MAAM;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CAAC;AAExB;AAACE,GAAA,GAlBuBV,GAAG;AAAA,IAAAF,EAAA,EAAAC,GAAA,EAAAW,GAAA;AAAAC,YAAA,CAAAb,EAAA;AAAAa,YAAA,CAAAZ,GAAA;AAAAY,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}