import PropTypes from "prop-types";
import React, { useState, useCallback, useEffect } from "react";
import "../../../App.css";
import { useDispatch, useSelector } from "react-redux";

import { TimelineSelector, UserSelector,ProductSelector } from "selectors";


import {
  Tooltip
} from '@mui/material';
import { ProductActions } from "slices/actions";

const TaskProgressBar = () => {

    const profile = useSelector(UserSelector.profile());
    const todayTimeLineRequests = useSelector(TimelineSelector.getTimelineRequestsToday());
    const products = useSelector(ProductSelector.getOnGoingProductsTasksToday())
    const dispatch = useDispatch()
 
    useEffect(() => {
        dispatch(ProductActions.getOnGoingProductsTasksToday())
    },[])

    useEffect(() => {
        console.log("Task Progress bar Products ",products)
    },[products])
 
    useEffect(() => {
      console.log("Time Line Request ",todayTimeLineRequests)
     
    },[todayTimeLineRequests])


  let minArr = [
    0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20,
    21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39,
    40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58,
    59,
  ];
  let minArrRev = [...minArr].reverse();

  const hours = Array.from({ length: 24 }, (_, i) => `${i} AM`);
  hours[12] = "12 PM";
  for (let i = 13; i < 24; i++) {
    hours[i] = `${i - 12} PM`;
  } 
  const [toolTipTitle, setTooltipTitle] = useState("");
  const [toolTipController,setToolTipController] = useState(false)
  

  const getSlotColor = (hour, minute) => {
    const slotTime = new Date(new Date().setHours(hour, minute, 0));
    if(products > 0) {
      for (let i = 0; i < products.length; i++) {
        const task = products[i];
        const taskStartDate = new Date(task.startTime);
        let taskEndDate="";
        if(task.endTime !== null) {

             taskEndDate = new Date(task.endTime);
        }
        taskEndDate = Date.now()
        console.log("Task Found",taskStartDate,taskEndDate,slotTime)
  
        if (slotTime >= taskStartDate && slotTime <= taskEndDate) {
          return "green";
        }
      }
    }
    return "lightgrey";
  };

  const normalizeRGB = useCallback((rgb) => {
    const result = rgb.match(/\d+/g);
    return result ? `rgb(${result[0]},${result[1]},${result[2]})` : rgb;
  },[]);

  const dateFormat = useCallback((startTime, endTime) => {
    const startTimeStr = new Date(startTime);
    const endTimeStr = new Date(endTime);
    let result = (endTimeStr - startTimeStr) / 60000;
    return result < 60 ? `${Math.floor(result)}m` : `${Math.floor(result / 60)}h ${Math.floor(result % 60)}m `;
  },[]);

  const handleMouseEnter = (event, hour, minute) => {
    const divColor = getComputedStyle(event.currentTarget).backgroundColor;
    console.log("Handle Mouse Eneter ",divColor)

    switch (normalizeRGB(divColor)) {
      case "rgb(255,255,0)": {
        setToolTipController(true)
        const activityDate = new Date(new Date().setHours(hour, minute-1, 0, 0));
        let idleFound = false;
        
        if (!idleFound) {
          console.log("Not Idel FOund")
          setTooltipTitle("Idle");
        }
        break;
      }
      case "rgb(50,205,50)": {
        setToolTipController(true)
        const activityDate = new Date(
          new Date().setHours(hour, minute-1, 0, 0)
        );
        let workFound = false;
        
        if (!workFound) {
          setTooltipTitle("Work");
        }
        break;
      }
      case "rgb(255,0,0)": {
        setToolTipController(true)
        const activityDate = new Date(new Date().setHours(hour, minute-1, 0, 0));
        let breakFound = false;
        
        if (!breakFound) {
          console.log("Breka N")
          setTooltipTitle("Break");
        }
        break;
      }

      case "rgb(0,0,255)": {
        console.log("Blue")
        setToolTipController(true)
        const activityDate = new Date(new Date().setHours(hour, minute-1, 0, 0));
        let timelineFound = false;
        let status = "Approved"
      
        if (!timelineFound) {
          console.log("Timeline N")
          setTooltipTitle("Request");
        }
        break;
      }
                                
      default: {
        // console.log("Blue")
        setToolTipController(false)
      
        break;
      }
    }
  };

  const handleMouseClick = (event, hour, minute) => {
    const divColor = getComputedStyle(event.currentTarget).backgroundColor;
  
    switch (normalizeRGB(divColor)) {
      case "rgb(255,255,0)": {
        const activityDate = new Date(new Date().setHours(hour, minute - 1, 0, 0));
        break;
      }
  
      case "rgb(255,0,0)": {
        const activityDate = new Date(new Date().setHours(hour, minute - 1, 0, 0));
        
        break;
      }
      
      default:  console.log("Default")
        break;
    }
   
  }
  

  const renderProgressBars = () => {
    const progressBars = [];
    let currentActivity = null;
    let currentActivityStart = 0;
    let currentActivityWidth = 0;

    hours.forEach((hour, hourIndex) => {
      minArr.forEach((minute) => {
        const activity = getSlotColor(hourIndex, minute);
        if (activity !== currentActivity) {
          if (currentActivity !== null) {
            // Push the current accumulated div
            progressBars.push(
              <div
                key={`${hourIndex}-${minute}`}
                className="progress-bar"
                role="progressbar"
                style={{
                  width: `${currentActivityWidth}%`,
                  backgroundColor: currentActivity,
                }}
                onMouseEnter={(event) => handleMouseEnter(event, hourIndex, minute)}
                onClick={(event) => handleMouseClick(event, hourIndex, minute)}
              >
               {toolTipController ? <Tooltip title={toolTipTitle} arrow>
                  <div
                    style={{ padding: "20px", display: "inline-block" }}
                  ></div>
                </Tooltip> : null }
              </div>
            );
          }
          // Start a new activity block
          currentActivity = activity;
          currentActivityStart = minute;
          currentActivityWidth = 1.04;
        } else {
          // Accumulate width for the same activity
          currentActivityWidth += 1.04;
        }
      });
    });


    if (currentActivity !== null) {
      // console.log("Accumulated Cell")
      progressBars.push(
        <div
          key={`last-${currentActivityStart}`}
          className="progress-bar"
          role="progressbar"
          style={{
            width: `${currentActivityWidth}%`,
            backgroundColor: currentActivity,
          }}
          onMouseEnter={(event) => handleMouseEnter(event, hours.length - 1, minArr.length - 1) }
          // onMouseLeave={handleMouseLeave}
        >
           {toolTipController ? <Tooltip title={toolTipTitle} arrow >
                  <div 
                    style={{ padding: "20px", display: "inline-block" }}
                    ></div>
                </Tooltip> : null }
        </div>
      );
    }

    return progressBars;
  };

  return (
    <>
   
      <div style={{ marginBottom: "1px" }}>
        <div className="progress" style={{ height: "10px" }}>
          {renderProgressBars()}
        </div>
      </div>
      <div className="d-flex justify-content-between mt-1">
        <li className="timeSlotLi">12AM</li>
        <li className="timeSlotLi">1AM</li>
        <li className="timeSlotLi">2AM</li>
        <li className="timeSlotLi">3AM</li>
        <li className="timeSlotLi">4AM</li>
        <li className="timeSlotLi">5AM</li>
        <li className="timeSlotLi">6AM</li>
        <li className="timeSlotLi">7AM</li>
        <li className="timeSlotLi">8AM</li>
        <li className="timeSlotLi">9AM</li>
        <li className="timeSlotLi">10AM</li>
        <li className="timeSlotLi">11AM</li>
        <li className="timeSlotLi">12PM</li>
        <li className="timeSlotLi">1PM</li>
        <li className="timeSlotLi">2PM</li>
        <li className="timeSlotLi">3PM</li>
        <li className="timeSlotLi">4PM</li>
        <li className="timeSlotLi">5PM</li>
        <li className="timeSlotLi">6PM</li>
        <li className="timeSlotLi">7PM</li>
        <li className="timeSlotLi">8PM</li>
        <li className="timeSlotLi">9PM</li>
        <li className="timeSlotLi">10PM</li>
        <li className="timeSlotLi">11PM</li>
      </div>
    </>
  );
};

// TaskProgressBar.propTypes = {
//   products: PropTypes.array,
// };

export default TaskProgressBar;
