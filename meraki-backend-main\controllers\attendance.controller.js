'use strict';

const { db } = require("../models");
const Attendance = db.attendance;
const Activity = db.activity;
const User = db.user;
const attendanceService = require('../services/attendance.service');

// Basic CRUD operations
exports.fetchAllAttendances = async (req, res) => {
  try {
    const result = await attendanceService.getAttendancesByQuery(req.query);
    res.status(200).json(result);
  } catch (error) {
    res.status(500).json({ message: error.message });
  }
};

exports.fetchAttendanceUserToday = async (req, res) => {
  try {
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    
    const tomorrow = new Date(today);
    tomorrow.setDate(tomorrow.getDate() + 1);
    
    const attendance = await Attendance.findOne({
      user: req.userId,
      checkIn: { $gte: today, $lt: tomorrow }
    });
    
    res.status(200).json(attendance || null);
  } catch (error) {
    res.status(500).json({ message: error.message });
  }
};

exports.createAttendance = async (req, res) => {
    try {
        const { body } = req;

        // Validate user ID
        if (!body.user || typeof body.user !== 'string' || body.user.trim() === '') {
            return res.status(400).send({
                message: "Invalid user ID. User ID is required."
            });
        }

        // Ensure checkIn is a valid date
        if (!body.checkIn) {
            body.checkIn = new Date();
        }

        // Create attendance record
        const result = await AttendanceService.createAttendance(body);

        if (!result) {
            return res.status(500).send({
                message: "Failed to create attendance record."
            });
        }

        // Initialize activity record with the same timestamp
        // This ensures the checkInTime in Activity matches the checkIn in Attendance
    
        // const Activity = require('../models').db.activity;
        // const existingActivity = await Activity.findOne({
        //     user: body.user,
        //     checkInTime: {
        //         $gte: new Date(new Date().setHours(0, 0, 0, 0)),
        //         $lt: new Date(new Date().setHours(23, 59, 59, 999))
        //     }
        // });

        // Only create activity if one doesn't already exist for today
        // if (!existingActivity) {
        //     await Activity.create({
        //         user: body.user,
        //         checkInTime: body.checkIn, // Use the same timestamp
        //         lateCheckInStatus: new Date(body.checkIn).getHours() >= 10 // Simple late check-in logic
        //     });
        // }

        return res.status(200).send({
            message: "Successfully created attendance record.",
            _id: result._id
        });
    } catch (error) {
        return res.status(500).send({
            message: "Error creating attendance record",
            error: error.message
        });
    }

}

exports.fetchAttendanceById = async (req, res) => {
  try {
    const result = await attendanceService.getAttendanceById(req.params.id);
    if (!result) {
      return res.status(404).json({ message: "Attendance not found" });
    }
    res.status(200).json(result);
  } catch (error) {
    res.status(500).json({ message: error.message });
  }
};

exports.fetchAttendanceByMonth = async (req, res) => {
  try {
    const { startDate, endDate } = req.params;
    const result = await attendanceService.getAttendanceByMonth({ startDate, endDate }, req.query);
    res.status(200).json(result);
  } catch (error) {
    res.status(500).json({ message: error.message });
  }
};

exports.updateAttendance = async (req, res) => {
  try {
    const result = await attendanceService.updateAttendance(req.params.id, req.body);
    res.status(200).json(result);
  } catch (error) {
    res.status(500).json({ message: error.message });
  }
};

exports.deleteAttendance = async (req, res) => {
  try {
    const result = await attendanceService.deleteAttendance(req.params.id);
    res.status(200).json(result);
  } catch (error) {
    res.status(500).json({ message: error.message });
  }
};

// Lunch break operations
exports.createLunchBreak = async (req, res) => {
  try {
    const attendance = await Attendance.findById(req.params.id);
    if (!attendance) {
      return res.status(404).json({ message: "Attendance not found" });
    }
    
    attendance.lunchIn = attendance.lunchIn || [];
    attendance.lunchIn.push(new Date());
    
    await attendance.save();
    res.status(200).json(attendance);
  } catch (error) {
    res.status(500).json({ message: error.message });
  }
};

exports.updateLunchBreak = async (req, res) => {
  try {
    const attendance = await Attendance.findById(req.params.id);
    if (!attendance) {
      return res.status(404).json({ message: "Attendance not found" });
    }
    
    attendance.lunchOut = attendance.lunchOut || [];
    attendance.lunchOut.push(new Date());
    
    await attendance.save();
    res.status(200).json(attendance);
  } catch (error) {
    res.status(500).json({ message: error.message });
  }
};

exports.deleteCheckOut = async (req, res) => {
  try {
    const result = await attendanceService.deleteCheckOut(req.body._id);
    res.status(200).json(result);
  } catch (error) {
    res.status(500).json({ message: error.message });
  }
};

/**
 * Manually trigger auto-checkout for users who forgot to checkout today
 * This can be called via an API endpoint by admins
 */
exports.triggerAutoCheckout = async (req, res) => {
  try {
    // Set date range for today
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    
    const tomorrow = new Date(today);
    tomorrow.setDate(tomorrow.getDate() + 1);
    
    // Find activities without checkout time for today
    const activities = await Activity.find({
      checkInTime: { $gte: today, $lt: tomorrow },
      checkOutTime: { $exists: false }
    });
    
    // Process each activity that needs auto-checkout
    for (const activity of activities) {
      // Set checkout time to current time
      const checkoutTime = new Date();
      
      // Update activity with checkout time and auto-checkout flag
      activity.checkOutTime = checkoutTime;
      activity.autoCheckout = true;
      
      // Calculate work hours
      const totalMinutes = Math.floor((checkoutTime - activity.checkInTime) / 60000);
      activity.totalWorkingTime = totalMinutes;
      
      // Get user's assigned work hours
      const user = await User.findById(activity.user);
      const assignedWorkHours = user?.workHours || 8.5;
      
      // Calculate actual hours and overtime
      const actualHoursWorked = Math.floor(totalMinutes / 60);
      const overtimeHours = actualHoursWorked > assignedWorkHours ?
        (actualHoursWorked - assignedWorkHours) : 0;
      
      // Set work hours status flags
      activity.earlyCheckOutStatus = actualHoursWorked < assignedWorkHours;
      activity.halfDayStatus = actualHoursWorked < (assignedWorkHours / 2);
      activity.overtimeStatus = actualHoursWorked > assignedWorkHours;
      activity.actualHours = actualHoursWorked;
      activity.assignedHours = assignedWorkHours;
      activity.overtimeHours = overtimeHours;
      
      // Save the updated activity
      await activity.save();
      
      // Update the corresponding attendance record
      const attendance = await Attendance.findOne({
        user: activity.user,
        checkIn: { $gte: today, $lt: tomorrow },
        checkOut: { $exists: false }
      });
      
      if (attendance) {
        attendance.checkOut = checkoutTime;
        await attendance.save();
      }
    }
    
    return res.status(200).json({
      success: true,
      message: `Auto-checkout completed for ${activities.length} users`,
      count: activities.length
    });
  } catch (error) {
    return res.status(500).json({
      success: false,
      message: "Error performing auto-checkout",
      error: error.message
    });
  }
};