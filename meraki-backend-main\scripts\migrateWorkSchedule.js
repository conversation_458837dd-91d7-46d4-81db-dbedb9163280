const mongoose = require('mongoose');

async function migrate() {
  try {
    await mongoose.connect('mongodb://localhost:27017/meraki');

    // Create a simple schema for migration
    const UserSchema = new mongoose.Schema({}, { strict: false });
    const User = mongoose.model('User', UserSchema);

    // Find all users with string workSchedule or missing workSchedule
    const users = await User.find({
      $or: [
        { workSchedule: { $type: "string" } },
        { workSchedule: { $exists: false } },
        { workSchedule: null }
      ]
    });

    let updated = 0;
    for (const user of users) {
      await User.updateOne(
        { _id: user._id },
        {
          $set: {
            workSchedule: {
              scheduleTemplate: 'day_shift',
              shiftStart: new Date(),
              shiftEnd: new Date(),
              startTime: '09:00',
              endTime: '17:00',
              minimumHours: 8.0
            }
          }
        }
      );
      updated++;
    }

    await mongoose.connection.close();

  } catch (error) {
    console.error('Migration failed:', error);
    process.exit(1);
  }
}

migrate();
