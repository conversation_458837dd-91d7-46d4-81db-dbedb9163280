'use strict';

/**
 * Database Connection and Model Registration Module
 *
 * This module initializes the MongoDB connection using Mongoose and
 * registers all the models used in the application.
 */

const mongoose = require("mongoose");

// Use native promises for Mongoose
mongoose.Promise = global.Promise;

/**
 * Database object that holds all model references
 * This allows easy access to all models from a single import
 */
const db = {};

// Store mongoose instance for potential direct access
db.mongoose = mongoose;

// Register all models
// User and Authentication models
db.user = require("./user.model");

// Organization Structure models
db.department = require("./department.model");
db.designation = require("./designation.model");

// HR Management models
db.attendance = require("./attendance.model");
db.expenses = require("./expenses.model");
db.leave = require("./leave.model");

// Activity Tracking models
db.activity = require("./activity.model");
db.timeline = require("./timeline.model");

// Project Management models
db.product = require("./product.model");
db.client = require("./client.model");
db.screenshot = require("./screenshot.model")
db.sprint = require("./sprint.model");

// System Configuration models
db.setting = require("./setting.model");

// Export the db object with all models
exports.db = db;

/**
 * Connect to MongoDB database
 * Uses different connection strings based on environment
 *
 * @returns {Promise} Resolves when connection is established
 */
exports.connectDatabase = async () => {
    // Determine which connection string to use based on environment
    let connectionString = process.env.MONGODB_URL;
    console.log("Connection String ",connectionString)

    // if (process.env.ENVIRONMENT === 'production') {
    //     connectionString = process.env.PROD_MONGODB_URL;
    // }

    // Connect to MongoDB with recommended options
    await mongoose.connect(connectionString, {
        useNewUrlParser: true,
        useUnifiedTopology: true
    });
}