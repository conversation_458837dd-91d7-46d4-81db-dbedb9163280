{"ast": null, "code": "import { createSlice } from \"@reduxjs/toolkit\";\nexport const ActivitySlice = createSlice({\n  name: \"Activity\",\n  initialState: {\n    activityArr: [],\n    // For single user activity (legacy)\n    multiUserActivityArr: [] // For multi-user activity data (new)\n  },\n  reducers: {\n    createTodayGoal: () => {},\n    createTodayStatus: () => {},\n    getUserActivitySuccessfull: (state, action) => {\n      if (action.payload.length === 0) {\n        state.activityArr = [];\n        state.multiUserActivityArr = [];\n        // console.log(\"1 REDUCER ACTIVITY LOG \",action.payload)\n      } else {\n        // Check if this is multi-user data (has properties like name, email, clockin, etc.)\n        // vs single-user data (has properties like user, checkInTime, etc.)\n        const isMultiUserData = action.payload.length > 0 && action.payload[0].hasOwnProperty('name') && action.payload[0].hasOwnProperty('clockin');\n        if (isMultiUserData) {\n          state.multiUserActivityArr = action.payload;\n          console.log(\"REDUCER MULTI-USER ACTIVITY LOG \", action.payload);\n        } else {\n          state.activityArr = action.payload;\n          console.log(\"REDUCER SINGLE-USER ACTIVITY LOG \", action.payload);\n        }\n      }\n    },\n    getUserActivity: () => {},\n    checkOutStatusUpdate: () => {},\n    breakStartRed: () => {},\n    breakEndRed: () => {},\n    lateCheckIn: () => {},\n    earlyCheckOut: () => {},\n    idelStartRed: () => {},\n    idelEndRed: () => {},\n    productivityStatusRed: () => {},\n    overLimitBreakRed: () => {},\n    eraseActivity: (state = []) => {\n      state.activityArr = [];\n    },\n    createTimelineRequest: () => {},\n    updateTimelineRequest: () => {},\n    getTimelineRequests: () => {}\n  }\n});\nexport default ActivitySlice;", "map": {"version": 3, "names": ["createSlice", "ActivitySlice", "name", "initialState", "activityArr", "multiUserActivityArr", "reducers", "createTodayGoal", "createTodayStatus", "getUserActivitySuccessfull", "state", "action", "payload", "length", "isMultiUserData", "hasOwnProperty", "console", "log", "getUserActivity", "checkOutStatusUpdate", "breakStartRed", "breakEndRed", "lateCheckIn", "earlyCheckOut", "idelStartRed", "idelEndRed", "productivityStatusRed", "overLimitBreakRed", "eraseActivity", "createTimelineRequest", "updateTimelineRequest", "getTimelineRequests"], "sources": ["E:/Suraj Patil/Organization_Management_System/meraki-frontend-main/src/slices/slice/ActivitySlice.js"], "sourcesContent": ["import { createSlice } from \"@reduxjs/toolkit\";\r\n\r\n\r\nexport const ActivitySlice = createSlice({\r\n    name: \"Activity\",\r\n    initialState: {\r\n        activityArr: [], // For single user activity (legacy)\r\n        multiUserActivityArr: [], // For multi-user activity data (new)\r\n    },\r\n    reducers: {\r\n        createTodayGoal: () => {\r\n        },\r\n        createTodayStatus: () => {\r\n        },\r\n        getUserActivitySuccessfull: (state,action) => {\r\n\r\n            if(action.payload.length === 0) {\r\n                state.activityArr = []\r\n                state.multiUserActivityArr = []\r\n                // console.log(\"1 REDUCER ACTIVITY LOG \",action.payload)\r\n            } else {\r\n                // Check if this is multi-user data (has properties like name, email, clockin, etc.)\r\n                // vs single-user data (has properties like user, checkInTime, etc.)\r\n                const isMultiUserData = action.payload.length > 0 &&\r\n                    action.payload[0].hasOwnProperty('name') &&\r\n                    action.payload[0].hasOwnProperty('clockin');\r\n\r\n                if (isMultiUserData) {\r\n                    state.multiUserActivityArr = action.payload;\r\n                    console.log(\"REDUCER MULTI-USER ACTIVITY LOG \", action.payload);\r\n                } else {\r\n                    state.activityArr = action.payload;\r\n                    console.log(\"REDUCER SINGLE-USER ACTIVITY LOG \", action.payload);\r\n                }\r\n            }\r\n\r\n        },\r\n        getUserActivity: () => {\r\n        },\r\n        checkOutStatusUpdate: () => {},\r\n        breakStartRed: () => {},\r\n        breakEndRed: () => {},\r\n        lateCheckIn: () => {},\r\n        earlyCheckOut: () => {},\r\n        idelStartRed: () => {},\r\n        idelEndRed: () => {},\r\n        productivityStatusRed: () => {},\r\n        overLimitBreakRed: () => {},\r\n        eraseActivity: (state = []) => {\r\n            state.activityArr = []\r\n        },\r\n        createTimelineRequest: () => {},\r\n        updateTimelineRequest: () => {},\r\n        getTimelineRequests: () => {}\r\n    }\r\n});\r\n\r\nexport default ActivitySlice;"], "mappings": "AAAA,SAASA,WAAW,QAAQ,kBAAkB;AAG9C,OAAO,MAAMC,aAAa,GAAGD,WAAW,CAAC;EACrCE,IAAI,EAAE,UAAU;EAChBC,YAAY,EAAE;IACVC,WAAW,EAAE,EAAE;IAAE;IACjBC,oBAAoB,EAAE,EAAE,CAAE;EAC9B,CAAC;EACDC,QAAQ,EAAE;IACNC,eAAe,EAAEA,CAAA,KAAM,CACvB,CAAC;IACDC,iBAAiB,EAAEA,CAAA,KAAM,CACzB,CAAC;IACDC,0BAA0B,EAAEA,CAACC,KAAK,EAACC,MAAM,KAAK;MAE1C,IAAGA,MAAM,CAACC,OAAO,CAACC,MAAM,KAAK,CAAC,EAAE;QAC5BH,KAAK,CAACN,WAAW,GAAG,EAAE;QACtBM,KAAK,CAACL,oBAAoB,GAAG,EAAE;QAC/B;MACJ,CAAC,MAAM;QACH;QACA;QACA,MAAMS,eAAe,GAAGH,MAAM,CAACC,OAAO,CAACC,MAAM,GAAG,CAAC,IAC7CF,MAAM,CAACC,OAAO,CAAC,CAAC,CAAC,CAACG,cAAc,CAAC,MAAM,CAAC,IACxCJ,MAAM,CAACC,OAAO,CAAC,CAAC,CAAC,CAACG,cAAc,CAAC,SAAS,CAAC;QAE/C,IAAID,eAAe,EAAE;UACjBJ,KAAK,CAACL,oBAAoB,GAAGM,MAAM,CAACC,OAAO;UAC3CI,OAAO,CAACC,GAAG,CAAC,kCAAkC,EAAEN,MAAM,CAACC,OAAO,CAAC;QACnE,CAAC,MAAM;UACHF,KAAK,CAACN,WAAW,GAAGO,MAAM,CAACC,OAAO;UAClCI,OAAO,CAACC,GAAG,CAAC,mCAAmC,EAAEN,MAAM,CAACC,OAAO,CAAC;QACpE;MACJ;IAEJ,CAAC;IACDM,eAAe,EAAEA,CAAA,KAAM,CACvB,CAAC;IACDC,oBAAoB,EAAEA,CAAA,KAAM,CAAC,CAAC;IAC9BC,aAAa,EAAEA,CAAA,KAAM,CAAC,CAAC;IACvBC,WAAW,EAAEA,CAAA,KAAM,CAAC,CAAC;IACrBC,WAAW,EAAEA,CAAA,KAAM,CAAC,CAAC;IACrBC,aAAa,EAAEA,CAAA,KAAM,CAAC,CAAC;IACvBC,YAAY,EAAEA,CAAA,KAAM,CAAC,CAAC;IACtBC,UAAU,EAAEA,CAAA,KAAM,CAAC,CAAC;IACpBC,qBAAqB,EAAEA,CAAA,KAAM,CAAC,CAAC;IAC/BC,iBAAiB,EAAEA,CAAA,KAAM,CAAC,CAAC;IAC3BC,aAAa,EAAEA,CAAClB,KAAK,GAAG,EAAE,KAAK;MAC3BA,KAAK,CAACN,WAAW,GAAG,EAAE;IAC1B,CAAC;IACDyB,qBAAqB,EAAEA,CAAA,KAAM,CAAC,CAAC;IAC/BC,qBAAqB,EAAEA,CAAA,KAAM,CAAC,CAAC;IAC/BC,mBAAmB,EAAEA,CAAA,KAAM,CAAC;EAChC;AACJ,CAAC,CAAC;AAEF,eAAe9B,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}