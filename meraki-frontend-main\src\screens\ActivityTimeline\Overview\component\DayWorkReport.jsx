import React, { useEffect } from "react";
import PropTypes from "prop-types";
import { useSelector, useDispatch } from "react-redux";
import {
  Avatar,
  Box,
  Card,
  Typography,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  LinearProgress
} from "@mui/material";
import { format, parseISO } from "date-fns";
import { ActivityActions } from "../../../../slices/actions";

const getInitials = (name) =>
  name.split(" ").map((n) => n[0]).join("");

 const formatTime = (timeStr) => {
  if (!timeStr || timeStr === "--") { return "--" }

  const minutes = parseInt(timeStr, 10); // 👈 radix added
  if (isNaN(minutes)) { return timeStr }

  const hours = Math.floor(minutes / 60);
  const mins = minutes % 60;
  return hours > 0 ? `${hours}h ${mins}m` : `${mins}m`;
};


const DayWorkReportFull = ({ dateRange }) => {
  const dispatch = useDispatch();

  // Get the entire Activity state for debugging (note: lowercase 'activity' in store)
  const activityState = useSelector((state) => state.activity || {});
  const { multiUserActivityArr = [], activityArr: legacyActivityArr = [] } = activityState;

  // Use multiUserActivityArr for the ActivityTimeline components
  const activityArr = multiUserActivityArr;

  // Debug logging for state
  console.log("DayWorkReport - Full Activity State:", activityState);
  console.log("DayWorkReport - multiUserActivityArr:", multiUserActivityArr);
  console.log("DayWorkReport - legacyActivityArr:", legacyActivityArr);

  // Format the selected date for display
  const displayDate = dateRange?.startDate ? format(parseISO(dateRange.startDate), "EEE, MMM d, yyyy") : format(new Date(), "EEE, MMM d, yyyy");

  // Note: API call is handled by the parent Overview component to avoid duplicate requests
  // useEffect(() => {
  //   if (dateRange?.startDate && dateRange?.endDate) {
  //     dispatch(ActivityActions.getUserActivity({
  //       startDate: dateRange.startDate,
  //       endDate: dateRange.endDate,
  //       view: 'day'
  //     }));
  //   }
  // }, [dateRange, dispatch]);

  // If data is not available, show placeholder
  if (!activityArr || activityArr.length === 0) {
    return (
      <Box p={3}>
        <Typography variant="h6" gutterBottom>
          Daily Work Report – {displayDate}
        </Typography>
        <Typography>
          No employee data available for {displayDate}
        </Typography>
      </Box>
    );
  }

  return (
    <Box p={3}>
      {/* <Typography variant="h6" gutterBottom>
        Daily Work Report – {displayDate}
      </Typography> */}
      <TableContainer component={Card}>
        <Table>
          <TableHead>
            <TableRow>
              <TableCell><strong>Name</strong></TableCell>
              <TableCell></TableCell>
              <TableCell>Clock In</TableCell>
              <TableCell>Clock Out</TableCell>
              <TableCell align="center" colSpan={2}>Entry</TableCell>
              <TableCell align="center" colSpan={2}>Exit</TableCell>
              <TableCell>Time At Work</TableCell>
              <TableCell>Productive Time</TableCell>
              <TableCell>Focus Time</TableCell>
            </TableRow>
            <TableRow>
              <TableCell />
              <TableCell />
              <TableCell />
              <TableCell />
              <TableCell>Early</TableCell>
              <TableCell>Late</TableCell>
              <TableCell>Early</TableCell>
              <TableCell>Late</TableCell>
              <TableCell />
              <TableCell />
              <TableCell />
            </TableRow>
          </TableHead>
          <TableBody>
            {activityArr.map((emp, i) => {
              // Calculate progress based on productivity and focus time
           const parseTime = (timeStr) => {
  if (!timeStr || timeStr === "-" || timeStr === "--") {
    return 0;
  }

 const match = timeStr.match(/(?<hours>\d+)h\s*(?<minutes>\d+)m/);

if (!match || !match.groups) { return 0 }

const hours = parseInt(match.groups.hours, 10);
const minutes = parseInt(match.groups.minutes, 10);
return (hours * 60) + minutes;
};

              
              const totalWorkMinutes = parseTime(emp.atwork);
              const productiveMinutes = parseTime(emp.productivitytime);
              const focusMinutes = productiveMinutes; // Using productivity time as focus time
              
              // Calculate progress
              const progress = totalWorkMinutes > 0 ? Math.min(100, ((productiveMinutes + focusMinutes) / totalWorkMinutes) * 100) : 0;
              
              // Determine color based on progress
              let barColor = "inherit";
              if (progress >= 90) { barColor = "success" }
              else if (progress >= 60) { barColor = "warning" }
              else if (progress > 0) { barColor = "error" }
              
              return (
                <TableRow key={i}>
                  <TableCell>
                    <Box display="flex" alignItems="center" gap={1}>
                      <Avatar>{getInitials(emp.name || "")}</Avatar>
                      <Box>
                        <Typography>{emp.name || ""}</Typography>
                      </Box>
                    </Box>
                  </TableCell>
                  <TableCell>
                    <LinearProgress
                      variant="determinate"
                      value={Math.round(progress)}
                      color={barColor}
                      sx={{ height: 6, borderRadius: 4, width: "120px" }}
                    />
                  </TableCell>
                  <TableCell>{emp.clockin !== "--" ? emp.clockin : "absent"}</TableCell>
                  <TableCell>{emp.clockout !== "--" ? emp.clockout : "absent"}</TableCell>
                  <TableCell sx={{ color: "green" }}>-</TableCell>
                  <TableCell sx={{ color: "red" }}>{emp.entrylate || "-"}</TableCell>
                  <TableCell sx={{ color: "green" }}>{emp.exitearly || "-"}</TableCell>
                  <TableCell sx={{ color: "red" }}>{emp.exitlate || "-"}</TableCell>
                  <TableCell>{emp.atwork !== "--" ? emp.atwork : "-"}</TableCell>
                  <TableCell>{emp.productivitytime !== "--" ? emp.productivitytime : "-"}</TableCell>
                  <TableCell>{emp.idletime !== "--" ? emp.idletime : "-"}</TableCell>
                </TableRow>
              );
            })}
          </TableBody>
        </Table>
      </TableContainer>
    </Box>
  );
};

DayWorkReportFull.propTypes = {
  dateRange: PropTypes.shape({
    startDate: PropTypes.string,
    endDate: PropTypes.string
  })
};

export default DayWorkReportFull;