{"ast": null, "code": "import { createSelector } from \"@reduxjs/toolkit\";\nconst productSelector = state => state.product;\nconst getProducts = () => createSelector(productSelector, product => product.products);\nconst getPagination = () => createSelector(productSelector, product => product.pagination);\nconst getProductById = () => createSelector(productSelector, product => product.product);\nconst getTasks = () => createSelector(productSelector, product => product.tasks);\nconst getTaskPagination = () => createSelector(productSelector, product => product.paginationTask);\nconst getOnGoingProductsTasksToday = () => createSelector(productSelector, product => product.ongoingProductTaskToday);\nexport const ProductSelector = {\n  getProducts,\n  getPagination,\n  getProductById,\n  getTaskPagination,\n  getOnGoingProductsTasksToday,\n  getTasks\n};", "map": {"version": 3, "names": ["createSelector", "productSelector", "state", "product", "getProducts", "products", "getPagination", "pagination", "getProductById", "getTasks", "tasks", "getTaskPagination", "paginationTask", "getOnGoingProductsTasksToday", "ongoingProductTaskToday", "ProductSelector"], "sources": ["E:/Suraj Patil/Organization_Management_System/meraki-frontend-main/src/selectors/ProductSelector.js"], "sourcesContent": ["import {createSelector} from \"@reduxjs/toolkit\";\r\n\r\nconst productSelector = (state) => state.product;\r\n\r\nconst getProducts = () => createSelector(\r\n    productSelector,\r\n    product => product.products\r\n)\r\n\r\nconst getPagination = () => createSelector(\r\n    productSelector,\r\n    product => product.pagination\r\n)\r\n\r\nconst getProductById = () => createSelector(\r\n    productSelector,\r\n    product => product.product\r\n)\r\n\r\nconst getTasks = () => createSelector(\r\n    productSelector,\r\n    product => product.tasks\r\n)\r\n\r\nconst getTaskPagination = () => createSelector(\r\n    productSelector,\r\n    product => product.paginationTask\r\n)\r\n\r\nconst getOnGoingProductsTasksToday = () => createSelector(\r\n    productSelector,\r\n    product => product.ongoingProductTaskToday\r\n)\r\n\r\nexport const ProductSelector = {\r\n    getProducts,\r\n    getPagination,\r\n    getProductById,\r\n    getTaskPagination,\r\n    getOnGoingProductsTasksToday,\r\n    getTasks \r\n}"], "mappings": "AAAA,SAAQA,cAAc,QAAO,kBAAkB;AAE/C,MAAMC,eAAe,GAAIC,KAAK,IAAKA,KAAK,CAACC,OAAO;AAEhD,MAAMC,WAAW,GAAGA,CAAA,KAAMJ,cAAc,CACpCC,eAAe,EACfE,OAAO,IAAIA,OAAO,CAACE,QACvB,CAAC;AAED,MAAMC,aAAa,GAAGA,CAAA,KAAMN,cAAc,CACtCC,eAAe,EACfE,OAAO,IAAIA,OAAO,CAACI,UACvB,CAAC;AAED,MAAMC,cAAc,GAAGA,CAAA,KAAMR,cAAc,CACvCC,eAAe,EACfE,OAAO,IAAIA,OAAO,CAACA,OACvB,CAAC;AAED,MAAMM,QAAQ,GAAGA,CAAA,KAAMT,cAAc,CACjCC,eAAe,EACfE,OAAO,IAAIA,OAAO,CAACO,KACvB,CAAC;AAED,MAAMC,iBAAiB,GAAGA,CAAA,KAAMX,cAAc,CAC1CC,eAAe,EACfE,OAAO,IAAIA,OAAO,CAACS,cACvB,CAAC;AAED,MAAMC,4BAA4B,GAAGA,CAAA,KAAMb,cAAc,CACrDC,eAAe,EACfE,OAAO,IAAIA,OAAO,CAACW,uBACvB,CAAC;AAED,OAAO,MAAMC,eAAe,GAAG;EAC3BX,WAAW;EACXE,aAAa;EACbE,cAAc;EACdG,iBAAiB;EACjBE,4BAA4B;EAC5BJ;AACJ,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}