{"ast": null, "code": "import { ProductService } from \"services/ProductService\";\nimport { ProductActions, GeneralActions } from \"../slices/actions\";\nimport { all, call, put, takeLatest } from 'redux-saga/effects';\nfunction* createProduct({\n  type,\n  payload\n}) {\n  try {\n    yield put(GeneralActions.removeError(type));\n    yield put(GeneralActions.startLoading(type));\n    console.warn(\"Create Product \", payload);\n\n    // Extract filter from payload\n    const filter = payload.filter || {};\n    console.warn(\"Using filter for product creation:\", filter);\n\n    // Create the product\n    const result = yield call(ProductService.createProduct, payload);\n\n    // Get the response status to check if creation was successful\n    if (result.status !== 200) {\n      const errorData = yield result.json();\n      throw new Error(errorData.error || \"Failed to create product\");\n    }\n\n    // Fetch products with the same filter that was used in the request\n    const resultHis = yield call(ProductService.getProducts, filter);\n    console.warn(\"Create Product data \", resultHis);\n    yield put(ProductActions.getSuccessfullyProducts(resultHis));\n    yield put(GeneralActions.stopLoading(type));\n  } catch (err) {\n    var _err$response, _err$response$data;\n    console.error(\"Error in createProduct saga:\", err);\n    yield put(GeneralActions.stopLoading(type));\n    yield put(GeneralActions.addError({\n      action: type,\n      message: ((_err$response = err.response) === null || _err$response === void 0 ? void 0 : (_err$response$data = _err$response.data) === null || _err$response$data === void 0 ? void 0 : _err$response$data.error) || err.message || \"Unknown error in createProduct\"\n    }));\n  }\n}\nfunction* createTaskByAdmin({\n  type,\n  payload\n}) {\n  try {\n    yield put(GeneralActions.removeError(type));\n    yield put(GeneralActions.startLoading(type));\n    console.warn(\"Create Task By Admin \", payload);\n    const result = yield call(ProductService.createProductsTask, payload.id, payload);\n    const resultHis = yield call(ProductService.getProducts);\n    // No need to call .json() as getProducts already returns parsed data\n    console.warn(\"Create Task data By Admin \", resultHis);\n    yield put(ProductActions.getSuccessfullyProducts(resultHis));\n    yield put(GeneralActions.stopLoading(type));\n  } catch (err) {\n    var _err$response2, _err$response2$data;\n    console.error(\"Error in createTaskByAdmin saga:\", err);\n    yield put(GeneralActions.stopLoading(type));\n    yield put(GeneralActions.addError({\n      action: type,\n      message: ((_err$response2 = err.response) === null || _err$response2 === void 0 ? void 0 : (_err$response2$data = _err$response2.data) === null || _err$response2$data === void 0 ? void 0 : _err$response2$data.error) || err.message || \"Unknown error in createTaskByAdmin\"\n    }));\n  }\n}\nfunction* updateProduct({\n  type,\n  payload\n}) {\n  try {\n    yield put(GeneralActions.removeError(type));\n    yield put(GeneralActions.startLoading(type));\n    console.warn(\"Update Product \", payload.id, payload);\n    const result1 = yield call(ProductService.updateProduct, payload.id, payload);\n    // For updateProduct, we still need to call .json() as it returns a Response object\n    const data1 = yield result1.json();\n    console.log(\"Updated Product Result \", data1);\n    const result = yield call(ProductService.getProducts);\n    // No need to call .json() as getProducts already returns parsed data\n    console.log(\"Get Products Result \", result);\n    yield put(ProductActions.getSuccessfullyProducts(result));\n    yield put(GeneralActions.stopLoading(type));\n  } catch (err) {\n    var _err$response3, _err$response3$data;\n    console.error(\"Error in updateProduct saga:\", err);\n    yield put(GeneralActions.stopLoading(type));\n    yield put(GeneralActions.addError({\n      action: type,\n      message: ((_err$response3 = err.response) === null || _err$response3 === void 0 ? void 0 : (_err$response3$data = _err$response3.data) === null || _err$response3$data === void 0 ? void 0 : _err$response3$data.error) || err.message || \"Unknown error in updateProduct\"\n    }));\n  }\n}\nfunction* deleteProduct({\n  type,\n  payload\n}) {\n  try {\n    yield put(GeneralActions.removeError(type));\n    yield put(GeneralActions.startLoading(type));\n    console.warn(\"Delete Product payload \", payload);\n    const result = yield call(ProductService.deleteProduct, payload);\n    // For deleteProduct, we still need to call .json() as it returns a Response object\n    const data = yield result.json();\n    console.warn(\"After Delete, payload.user: \", payload.user);\n    const result1 = yield call(ProductService.getProducts);\n    // No need to call .json() as getProducts already returns parsed data\n    console.warn(\"Get Products after delete: \", result1);\n    yield put(ProductActions.getSuccessfullyProducts(result1));\n    yield put(GeneralActions.stopLoading(type));\n  } catch (err) {\n    var _err$response4, _err$response4$data;\n    console.error(\"Error in deleteProduct saga:\", err);\n    yield put(GeneralActions.stopLoading(type));\n    yield put(GeneralActions.addError({\n      action: type,\n      message: ((_err$response4 = err.response) === null || _err$response4 === void 0 ? void 0 : (_err$response4$data = _err$response4.data) === null || _err$response4$data === void 0 ? void 0 : _err$response4$data.error) || err.message || \"Unknown error in deleteProduct\"\n    }));\n  }\n}\nfunction* getProducts({\n  type,\n  payload\n}) {\n  try {\n    yield put(GeneralActions.removeError(type));\n    yield put(GeneralActions.startLoading(type));\n    console.warn(\"Get Products payload \", payload);\n\n    // Ensure we're passing the filter correctly\n    const filter = payload || {};\n    console.warn(\"Using filter for getProducts:\", filter);\n\n    // Call the service with the filter\n    const result = yield call(ProductService.getProducts, filter);\n    console.warn(\"Get Products result:\", result);\n    yield put(ProductActions.getSuccessfullyProducts(result));\n    yield put(GeneralActions.stopLoading(type));\n  } catch (err) {\n    var _err$response5, _err$response5$data;\n    console.error(\"Error in getProducts saga:\", err);\n    yield put(GeneralActions.stopLoading(type));\n    yield put(GeneralActions.addError({\n      action: type,\n      message: ((_err$response5 = err.response) === null || _err$response5 === void 0 ? void 0 : (_err$response5$data = _err$response5.data) === null || _err$response5$data === void 0 ? void 0 : _err$response5$data.error) || err.message || \"Unknown error in getProducts\"\n    }));\n  }\n}\nfunction* createProductsTaskByUser({\n  type,\n  payload\n}) {\n  try {\n    yield put(GeneralActions.removeError(type));\n    yield put(GeneralActions.startLoading(type));\n    console.log(\"Task By User with payload:\", payload);\n\n    // Make sure sprintId is properly passed\n    if (payload.sprintId) {\n      console.log(\"Sprint ID is present:\", payload.sprintId);\n      console.log(\"addToSprint flag:\", payload.addToSprint);\n    }\n\n    // Fix: Use createProductsTask instead of createTask\n    const result = yield call(ProductService.createProductsTask, payload.id, payload);\n    console.log(\"Task creation result:\", result);\n    const resultHis = yield call(ProductService.getProducts);\n    console.warn(\"Products after task creation:\", resultHis);\n    yield put(ProductActions.getSuccessfullyProducts(resultHis));\n    yield put(GeneralActions.stopLoading(type));\n  } catch (err) {\n    var _err$response6, _err$response6$data;\n    console.error(\"Error in createProductsTaskByUser saga:\", err);\n    yield put(GeneralActions.stopLoading(type));\n    yield put(GeneralActions.addError({\n      action: type,\n      message: ((_err$response6 = err.response) === null || _err$response6 === void 0 ? void 0 : (_err$response6$data = _err$response6.data) === null || _err$response6$data === void 0 ? void 0 : _err$response6$data.error) || err.message || \"Unknown error in createProductsTaskByUser\"\n    }));\n  }\n}\nfunction* getProductsByUser({\n  type,\n  payload\n}) {\n  try {\n    yield put(GeneralActions.removeError(type));\n    yield put(GeneralActions.startLoading(type));\n    console.warn(\"Get Products By User, id: \", payload.id);\n    const result = yield call(ProductService.getProductsByUser, payload.id);\n    // For getProductsByUser, we still need to call .json() as it returns a Response object\n    const data = yield result.json();\n    console.log(\"Get Products By User Result \", data);\n    yield put(ProductActions.getSuccessfullyProducts(data));\n    yield put(GeneralActions.stopLoading(type));\n  } catch (err) {\n    var _err$response7, _err$response7$data;\n    console.error(\"Error in getProductsByUser saga:\", err);\n    yield put(GeneralActions.stopLoading(type));\n    yield put(GeneralActions.addError({\n      action: type,\n      message: ((_err$response7 = err.response) === null || _err$response7 === void 0 ? void 0 : (_err$response7$data = _err$response7.data) === null || _err$response7$data === void 0 ? void 0 : _err$response7$data.error) || err.message || \"Unknown error in getProductsByUser\"\n    }));\n  }\n}\nfunction* getProductById({\n  type,\n  payload\n}) {\n  try {\n    yield put(GeneralActions.removeError(type));\n    yield put(GeneralActions.startLoading(type));\n    console.warn(\"Get Product By Id, payload: \", payload);\n    const result = yield call(ProductService.getProductById, payload.id);\n    // For getProductById, we still need to call .json() as it returns a Response object\n    const data = yield result.json();\n    console.log(\"Get Product By Id Result \", data);\n    yield put(ProductActions.getSuccessfullyProductById(data));\n    yield put(GeneralActions.stopLoading(type));\n  } catch (err) {\n    var _err$response8, _err$response8$data;\n    console.error(\"Error in getProductById saga:\", err);\n    yield put(GeneralActions.stopLoading(type));\n    yield put(GeneralActions.addError({\n      action: type,\n      message: ((_err$response8 = err.response) === null || _err$response8 === void 0 ? void 0 : (_err$response8$data = _err$response8.data) === null || _err$response8$data === void 0 ? void 0 : _err$response8$data.error) || err.message || \"Unknown error in getProductById\"\n    }));\n  }\n}\nfunction* updateTask({\n  type,\n  payload\n}) {\n  try {\n    yield put(GeneralActions.removeError(type));\n    yield put(GeneralActions.startLoading(type));\n    console.warn(\"Updating Task:\", payload);\n\n    // Fix parameter naming to match what the service expects\n    const productId = payload.productId || payload.productid;\n    const taskId = payload.taskId || payload.taskid;\n    const body = payload.updatedTask || payload.body;\n    if (!productId || !taskId) {\n      throw new Error(\"Missing required fields in payload\");\n    }\n    console.log(`Updating task ${taskId} in product ${productId} with body:`, body);\n\n    // Call the service with the correct parameters\n    const result1 = yield call(ProductService.updateTask, productId, taskId, body || {});\n    const data1 = result1;\n    console.log(\"Updated Task Result\", data1);\n\n    // Refresh product list\n    const result = yield call(ProductService.getProducts);\n    yield put(ProductActions.getSuccessfullyProducts(result));\n    yield put(GeneralActions.stopLoading(type));\n  } catch (err) {\n    var _err$response9, _err$response9$data;\n    console.error(\"Error in updateTask saga:\", err);\n    yield put(GeneralActions.stopLoading(type));\n    yield put(GeneralActions.addError({\n      action: type,\n      message: ((_err$response9 = err.response) === null || _err$response9 === void 0 ? void 0 : (_err$response9$data = _err$response9.data) === null || _err$response9$data === void 0 ? void 0 : _err$response9$data.error) || err.message || \"Unknown error in updateTask\"\n    }));\n  }\n}\n\n// New saga for starting a task\nfunction* startTask({\n  type,\n  payload\n}) {\n  try {\n    yield put(GeneralActions.removeError(type));\n    yield put(GeneralActions.startLoading(type));\n    console.warn(\"Starting Task \", payload.taskId, payload.projectId);\n\n    // Pass the entire payload to the startTask service\n    const result = yield call(ProductService.startTask, payload);\n    console.log(\"Started Task Result \", result);\n\n    // Refresh the product list after starting the task\n    const productsResult = yield call(ProductService.getProducts);\n    yield put(ProductActions.getSuccessfullyProducts(productsResult));\n    yield put(GeneralActions.stopLoading(type));\n  } catch (err) {\n    var _err$response0, _err$response0$data;\n    console.error(\"Error in startTask saga:\", err);\n    yield put(GeneralActions.stopLoading(type));\n    yield put(GeneralActions.addError({\n      action: type,\n      message: ((_err$response0 = err.response) === null || _err$response0 === void 0 ? void 0 : (_err$response0$data = _err$response0.data) === null || _err$response0$data === void 0 ? void 0 : _err$response0$data.error) || err.message || \"Unknown error in startTask\"\n    }));\n  }\n}\n\n// New saga for stopping a task\nfunction* stopTask({\n  type,\n  payload\n}) {\n  try {\n    yield put(GeneralActions.removeError(type));\n    yield put(GeneralActions.startLoading(type));\n    console.warn(\"Stopping Task \", payload.taskId, payload.projectId);\n\n    // Pass the entire payload to the stopTask service\n    const result = yield call(ProductService.stopTask, payload);\n    console.log(\"Stopped Task Result \", result);\n\n    // Refresh the product list after stopping the task\n    const productsResult = yield call(ProductService.getProducts);\n    yield put(ProductActions.getSuccessfullyProducts(productsResult));\n    yield put(GeneralActions.stopLoading(type));\n  } catch (err) {\n    var _err$response1, _err$response1$data;\n    console.error(\"Error in stopTask saga:\", err);\n    yield put(GeneralActions.stopLoading(type));\n    yield put(GeneralActions.addError({\n      action: type,\n      message: ((_err$response1 = err.response) === null || _err$response1 === void 0 ? void 0 : (_err$response1$data = _err$response1.data) === null || _err$response1$data === void 0 ? void 0 : _err$response1$data.error) || err.message || \"Unknown error in stopTask\"\n    }));\n  }\n}\n\n// New saga for pausing a task\nfunction* pauseTask({\n  type,\n  payload\n}) {\n  try {\n    yield put(GeneralActions.removeError(type));\n    yield put(GeneralActions.startLoading(type));\n    console.warn(\"Pausing Task \", payload.taskId, payload.projectId);\n    console.log(\"Pause payload:\", payload);\n\n    // Pass the entire payload to the pauseTask service\n    const result = yield call(ProductService.pauseTask, payload);\n    console.log(\"Paused Task Result \", result);\n\n    // Refresh the product list after pausing the task\n    const productsResult = yield call(ProductService.getProducts);\n    yield put(ProductActions.getSuccessfullyProducts(productsResult));\n    yield put(GeneralActions.stopLoading(type));\n  } catch (err) {\n    var _err$response10, _err$response10$data;\n    console.error(\"Error in pauseTask saga:\", err);\n    yield put(GeneralActions.stopLoading(type));\n    yield put(GeneralActions.addError({\n      action: type,\n      message: ((_err$response10 = err.response) === null || _err$response10 === void 0 ? void 0 : (_err$response10$data = _err$response10.data) === null || _err$response10$data === void 0 ? void 0 : _err$response10$data.error) || err.message || \"Unknown error in pauseTask\"\n    }));\n  }\n}\nfunction* getOnGoingProductsTasksToday({\n  type,\n  payload\n}) {\n  try {\n    yield put(GeneralActions.removeError(type));\n    yield put(GeneralActions.startLoading(type));\n    console.warn(\"Get On Going Products\");\n    const result = yield call(ProductService.getOnGoingProductsTasksToday);\n    // For getOnGoingProducts, we still need to call .json() as it returns a Response object\n    const data = yield result.json();\n    console.log(\"Get On Going Products Result \", data);\n    yield put(ProductActions.getSuccessfullyOnGoingProductsTasksToday(data));\n    yield put(GeneralActions.stopLoading(type));\n  } catch (err) {\n    var _err$response11, _err$response11$data;\n    console.error(\"Error in getOnGoingProducts saga:\", err);\n    yield put(GeneralActions.stopLoading(type));\n    yield put(GeneralActions.addError({\n      action: type,\n      message: ((_err$response11 = err.response) === null || _err$response11 === void 0 ? void 0 : (_err$response11$data = _err$response11.data) === null || _err$response11$data === void 0 ? void 0 : _err$response11$data.error) || err.message || \"Unknown error in getOnGoingProducts\"\n    }));\n  }\n}\nexport function* ProductWatcher() {\n  yield all([yield takeLatest(ProductActions.createProduct.type, createProduct), yield takeLatest(ProductActions.getProducts.type, getProducts), yield takeLatest(ProductActions.updateProduct.type, updateProduct), yield takeLatest(ProductActions.deleteProduct.type, deleteProduct), yield takeLatest(ProductActions.createTaskByAdmin.type, createTaskByAdmin), yield takeLatest(ProductActions.createProductsTaskByUser.type, createProductsTaskByUser), yield takeLatest(ProductActions.getOnGoingProductsTasksToday.type, getOnGoingProductsTasksToday), yield takeLatest(ProductActions.getProductById.type, getProductById), yield takeLatest(ProductActions.getProductsByUser.type, getProductsByUser), yield takeLatest(ProductActions.updateTask.type, updateTask), yield takeLatest(ProductActions.startTask.type, startTask),\n  // Start task watcher\n  yield takeLatest(ProductActions.stopTask.type, stopTask),\n  // Stop task watcher\n  yield takeLatest(ProductActions.pauseTask.type, pauseTask) // Pause task watcher\n  ]);\n}\n_c = ProductWatcher;\nvar _c;\n$RefreshReg$(_c, \"ProductWatcher\");", "map": {"version": 3, "names": ["ProductService", "ProductActions", "GeneralActions", "all", "call", "put", "take<PERSON><PERSON>t", "createProduct", "type", "payload", "removeError", "startLoading", "console", "warn", "filter", "result", "status", "errorData", "json", "Error", "error", "resultHis", "getProducts", "getSuccessfullyProducts", "stopLoading", "err", "_err$response", "_err$response$data", "addError", "action", "message", "response", "data", "createTaskByAdmin", "createProductsTask", "id", "_err$response2", "_err$response2$data", "updateProduct", "result1", "data1", "log", "_err$response3", "_err$response3$data", "deleteProduct", "user", "_err$response4", "_err$response4$data", "_err$response5", "_err$response5$data", "createProductsTaskByUser", "sprintId", "addToSprint", "_err$response6", "_err$response6$data", "getProductsByUser", "_err$response7", "_err$response7$data", "getProductById", "getSuccessfullyProductById", "_err$response8", "_err$response8$data", "updateTask", "productId", "productid", "taskId", "taskid", "body", "updatedTask", "_err$response9", "_err$response9$data", "startTask", "projectId", "productsResult", "_err$response0", "_err$response0$data", "stopTask", "_err$response1", "_err$response1$data", "pauseTask", "_err$response10", "_err$response10$data", "getOnGoingProductsTasksToday", "getSuccessfullyOnGoingProductsTasksToday", "_err$response11", "_err$response11$data", "ProductWatcher", "_c", "$RefreshReg$"], "sources": ["E:/Suraj Patil/Organization_Management_System/meraki-frontend-main/src/sagas/ProductSaga.js"], "sourcesContent": ["import { ProductService } from \"services/ProductService\";\r\nimport { ProductActions, GeneralActions } from \"../slices/actions\";\r\nimport { all, call, put, takeLatest } from 'redux-saga/effects';\r\n\r\nfunction *createProduct({ type, payload }) {\r\n    try {\r\n        yield put(GeneralActions.removeError(type));\r\n        yield put(GeneralActions.startLoading(type));\r\n        console.warn(\"Create Product \", payload);\r\n\r\n        // Extract filter from payload\r\n        const filter = payload.filter || {};\r\n        console.warn(\"Using filter for product creation:\", filter);\r\n\r\n        // Create the product\r\n        const result = yield call(ProductService.createProduct, payload);\r\n\r\n        // Get the response status to check if creation was successful\r\n        if (result.status !== 200) {\r\n            const errorData = yield result.json();\r\n            throw new Error(errorData.error || \"Failed to create product\");\r\n        }\r\n\r\n        // Fetch products with the same filter that was used in the request\r\n        const resultHis = yield call(ProductService.getProducts, filter);\r\n        console.warn(\"Create Product data \", resultHis);\r\n\r\n        yield put(ProductActions.getSuccessfullyProducts(resultHis));\r\n        yield put(GeneralActions.stopLoading(type));\r\n    } catch (err) {\r\n        console.error(\"Error in createProduct saga:\", err);\r\n        yield put(GeneralActions.stopLoading(type));\r\n        yield put(GeneralActions.addError({\r\n            action: type,\r\n            message: err.response?.data?.error || err.message || \"Unknown error in createProduct\"\r\n        }));\r\n    }\r\n}\r\n\r\nfunction *createTaskByAdmin({ type, payload }) {\r\n    try {\r\n        yield put(GeneralActions.removeError(type));\r\n        yield put(GeneralActions.startLoading(type));\r\n        console.warn(\"Create Task By Admin \", payload);\r\n        const result = yield call(ProductService.createProductsTask, payload.id, payload);\r\n        const resultHis = yield call(ProductService.getProducts);\r\n        // No need to call .json() as getProducts already returns parsed data\r\n        console.warn(\"Create Task data By Admin \", resultHis);\r\n        yield put(ProductActions.getSuccessfullyProducts(resultHis));\r\n        yield put(GeneralActions.stopLoading(type));\r\n    } catch (err) {\r\n        console.error(\"Error in createTaskByAdmin saga:\", err);\r\n        yield put(GeneralActions.stopLoading(type));\r\n        yield put(GeneralActions.addError({\r\n            action: type,\r\n            message: err.response?.data?.error || err.message || \"Unknown error in createTaskByAdmin\"\r\n        }));\r\n    }\r\n}\r\n\r\nfunction *updateProduct({ type, payload }) {\r\n    try {\r\n        yield put(GeneralActions.removeError(type));\r\n        yield put(GeneralActions.startLoading(type));\r\n        console.warn(\"Update Product \", payload.id, payload);\r\n        const result1 = yield call(ProductService.updateProduct, payload.id, payload);\r\n        // For updateProduct, we still need to call .json() as it returns a Response object\r\n        const data1 = yield result1.json();\r\n        console.log(\"Updated Product Result \", data1);\r\n        const result = yield call(ProductService.getProducts);\r\n        // No need to call .json() as getProducts already returns parsed data\r\n        console.log(\"Get Products Result \", result);\r\n        yield put(ProductActions.getSuccessfullyProducts(result));\r\n        yield put(GeneralActions.stopLoading(type));\r\n    } catch (err) {\r\n        console.error(\"Error in updateProduct saga:\", err);\r\n        yield put(GeneralActions.stopLoading(type));\r\n        yield put(GeneralActions.addError({\r\n            action: type,\r\n            message: err.response?.data?.error || err.message || \"Unknown error in updateProduct\"\r\n        }));\r\n    }\r\n}\r\n\r\nfunction *deleteProduct({ type, payload }) {\r\n    try {\r\n        yield put(GeneralActions.removeError(type));\r\n        yield put(GeneralActions.startLoading(type));\r\n        console.warn(\"Delete Product payload \", payload);\r\n        const result = yield call(ProductService.deleteProduct, payload);\r\n        // For deleteProduct, we still need to call .json() as it returns a Response object\r\n        const data = yield result.json();\r\n        console.warn(\"After Delete, payload.user: \", payload.user);\r\n        const result1 = yield call(ProductService.getProducts);\r\n        // No need to call .json() as getProducts already returns parsed data\r\n        console.warn(\"Get Products after delete: \", result1);\r\n        yield put(ProductActions.getSuccessfullyProducts(result1));\r\n        yield put(GeneralActions.stopLoading(type));\r\n    } catch (err) {\r\n        console.error(\"Error in deleteProduct saga:\", err);\r\n        yield put(GeneralActions.stopLoading(type));\r\n        yield put(GeneralActions.addError({\r\n            action: type,\r\n            message: err.response?.data?.error || err.message || \"Unknown error in deleteProduct\"\r\n        }));\r\n    }\r\n}\r\n\r\nfunction *getProducts({ type, payload }) {\r\n    try {\r\n        yield put(GeneralActions.removeError(type));\r\n        yield put(GeneralActions.startLoading(type));\r\n        console.warn(\"Get Products payload \", payload);\r\n\r\n        // Ensure we're passing the filter correctly\r\n        const filter = payload || {};\r\n        console.warn(\"Using filter for getProducts:\", filter);\r\n\r\n        // Call the service with the filter\r\n        const result = yield call(ProductService.getProducts, filter);\r\n        console.warn(\"Get Products result:\", result);\r\n\r\n        yield put(ProductActions.getSuccessfullyProducts(result));\r\n        yield put(GeneralActions.stopLoading(type));\r\n    } catch (err) {\r\n        console.error(\"Error in getProducts saga:\", err);\r\n        yield put(GeneralActions.stopLoading(type));\r\n        yield put(GeneralActions.addError({\r\n            action: type,\r\n            message: err.response?.data?.error || err.message || \"Unknown error in getProducts\"\r\n        }));\r\n    }\r\n}\r\n\r\nfunction *createProductsTaskByUser({ type, payload }) {\r\n    try {\r\n        yield put(GeneralActions.removeError(type));\r\n        yield put(GeneralActions.startLoading(type));\r\n        console.log(\"Task By User with payload:\", payload);\r\n        \r\n        // Make sure sprintId is properly passed\r\n        if (payload.sprintId) {\r\n            console.log(\"Sprint ID is present:\", payload.sprintId);\r\n            console.log(\"addToSprint flag:\", payload.addToSprint);\r\n        }\r\n        \r\n        // Fix: Use createProductsTask instead of createTask\r\n        const result = yield call(ProductService.createProductsTask, payload.id, payload);\r\n        console.log(\"Task creation result:\", result);\r\n        \r\n        const resultHis = yield call(ProductService.getProducts);\r\n        console.warn(\"Products after task creation:\", resultHis);\r\n        \r\n        yield put(ProductActions.getSuccessfullyProducts(resultHis));\r\n        yield put(GeneralActions.stopLoading(type));\r\n    } catch (err) {\r\n        console.error(\"Error in createProductsTaskByUser saga:\", err);\r\n        yield put(GeneralActions.stopLoading(type));\r\n        yield put(GeneralActions.addError({\r\n            action: type,\r\n            message: err.response?.data?.error || err.message || \"Unknown error in createProductsTaskByUser\"\r\n        }));\r\n    }\r\n}\r\n\r\nfunction *getProductsByUser({ type, payload }) {\r\n    try {\r\n        yield put(GeneralActions.removeError(type));\r\n        yield put(GeneralActions.startLoading(type));\r\n        console.warn(\"Get Products By User, id: \", payload.id);\r\n        const result = yield call(ProductService.getProductsByUser, payload.id);\r\n        // For getProductsByUser, we still need to call .json() as it returns a Response object\r\n        const data = yield result.json();\r\n        console.log(\"Get Products By User Result \", data);\r\n        yield put(ProductActions.getSuccessfullyProducts(data));\r\n        yield put(GeneralActions.stopLoading(type));\r\n    } catch (err) {\r\n        console.error(\"Error in getProductsByUser saga:\", err);\r\n        yield put(GeneralActions.stopLoading(type));\r\n        yield put(GeneralActions.addError({\r\n            action: type,\r\n            message: err.response?.data?.error || err.message || \"Unknown error in getProductsByUser\"\r\n        }));\r\n    }\r\n}\r\n\r\nfunction *getProductById({ type, payload }) {\r\n    try {\r\n        yield put(GeneralActions.removeError(type));\r\n        yield put(GeneralActions.startLoading(type));\r\n        console.warn(\"Get Product By Id, payload: \", payload);\r\n        const result = yield call(ProductService.getProductById, payload.id);\r\n        // For getProductById, we still need to call .json() as it returns a Response object\r\n        const data = yield result.json();\r\n        console.log(\"Get Product By Id Result \", data);\r\n        yield put(ProductActions.getSuccessfullyProductById(data));\r\n        yield put(GeneralActions.stopLoading(type));\r\n    } catch (err) {\r\n        console.error(\"Error in getProductById saga:\", err);\r\n        yield put(GeneralActions.stopLoading(type));\r\n        yield put(GeneralActions.addError({\r\n            action: type,\r\n            message: err.response?.data?.error || err.message || \"Unknown error in getProductById\"\r\n        }));\r\n    }\r\n}\r\nfunction *updateTask({ type, payload }) {\r\n    try {\r\n        yield put(GeneralActions.removeError(type));\r\n        yield put(GeneralActions.startLoading(type));\r\n\r\n        console.warn(\"Updating Task:\", payload);\r\n\r\n        // Fix parameter naming to match what the service expects\r\n        const productId = payload.productId || payload.productid;\r\n        const taskId = payload.taskId || payload.taskid;\r\n        const body = payload.updatedTask || payload.body;\r\n\r\n        if (!productId || !taskId) {\r\n            throw new Error(\"Missing required fields in payload\");\r\n        }\r\n\r\n        console.log(`Updating task ${taskId} in product ${productId} with body:`, body);\r\n\r\n        // Call the service with the correct parameters\r\n        const result1 = yield call(ProductService.updateTask, productId, taskId, body || {});\r\n        const data1 = result1;\r\n        console.log(\"Updated Task Result\", data1);\r\n\r\n        // Refresh product list\r\n        const result = yield call(ProductService.getProducts);\r\n        yield put(ProductActions.getSuccessfullyProducts(result));\r\n        yield put(GeneralActions.stopLoading(type));\r\n    } catch (err) {\r\n        console.error(\"Error in updateTask saga:\", err);\r\n        yield put(GeneralActions.stopLoading(type));\r\n        yield put(GeneralActions.addError({\r\n            action: type,\r\n            message: err.response?.data?.error || err.message || \"Unknown error in updateTask\"\r\n        }));\r\n    }\r\n}\r\n\r\n\r\n// New saga for starting a task\r\nfunction *startTask({ type, payload }) {\r\n    try {\r\n        yield put(GeneralActions.removeError(type));\r\n        yield put(GeneralActions.startLoading(type));\r\n        console.warn(\"Starting Task \", payload.taskId, payload.projectId);\r\n\r\n        // Pass the entire payload to the startTask service\r\n        const result = yield call(ProductService.startTask, payload);\r\n        console.log(\"Started Task Result \", result);\r\n\r\n        // Refresh the product list after starting the task\r\n        const productsResult = yield call(ProductService.getProducts);\r\n        yield put(ProductActions.getSuccessfullyProducts(productsResult));\r\n        yield put(GeneralActions.stopLoading(type));\r\n    } catch (err) {\r\n        console.error(\"Error in startTask saga:\", err);\r\n        yield put(GeneralActions.stopLoading(type));\r\n        yield put(GeneralActions.addError({\r\n            action: type,\r\n            message: err.response?.data?.error || err.message || \"Unknown error in startTask\"\r\n        }));\r\n    }\r\n}\r\n\r\n// New saga for stopping a task\r\nfunction *stopTask({ type, payload }) {\r\n    try {\r\n        yield put(GeneralActions.removeError(type));\r\n        yield put(GeneralActions.startLoading(type));\r\n        console.warn(\"Stopping Task \", payload.taskId, payload.projectId);\r\n\r\n        // Pass the entire payload to the stopTask service\r\n        const result = yield call(ProductService.stopTask, payload);\r\n        console.log(\"Stopped Task Result \", result);\r\n\r\n        // Refresh the product list after stopping the task\r\n        const productsResult = yield call(ProductService.getProducts);\r\n        yield put(ProductActions.getSuccessfullyProducts(productsResult));\r\n        yield put(GeneralActions.stopLoading(type));\r\n    } catch (err) {\r\n        console.error(\"Error in stopTask saga:\", err);\r\n        yield put(GeneralActions.stopLoading(type));\r\n        yield put(GeneralActions.addError({\r\n            action: type,\r\n            message: err.response?.data?.error || err.message || \"Unknown error in stopTask\"\r\n        }));\r\n    }\r\n}\r\n\r\n// New saga for pausing a task\r\nfunction *pauseTask({ type, payload }) {\r\n    try {\r\n        yield put(GeneralActions.removeError(type));\r\n        yield put(GeneralActions.startLoading(type));\r\n        console.warn(\"Pausing Task \", payload.taskId, payload.projectId);\r\n        console.log(\"Pause payload:\", payload);\r\n\r\n        // Pass the entire payload to the pauseTask service\r\n        const result = yield call(ProductService.pauseTask, payload);\r\n        console.log(\"Paused Task Result \", result);\r\n\r\n        // Refresh the product list after pausing the task\r\n        const productsResult = yield call(ProductService.getProducts);\r\n        yield put(ProductActions.getSuccessfullyProducts(productsResult));\r\n        yield put(GeneralActions.stopLoading(type));\r\n    } catch (err) {\r\n        console.error(\"Error in pauseTask saga:\", err);\r\n        yield put(GeneralActions.stopLoading(type));\r\n        yield put(GeneralActions.addError({\r\n            action: type,\r\n            message: err.response?.data?.error || err.message || \"Unknown error in pauseTask\"\r\n        }));\r\n    }\r\n}\r\n\r\nfunction *getOnGoingProductsTasksToday({ type, payload }) {\r\n    try {\r\n        yield put(GeneralActions.removeError(type));\r\n        yield put(GeneralActions.startLoading(type));\r\n        console.warn(\"Get On Going Products\");\r\n        const result = yield call(ProductService.getOnGoingProductsTasksToday);\r\n        // For getOnGoingProducts, we still need to call .json() as it returns a Response object\r\n        const data = yield result.json();\r\n        console.log(\"Get On Going Products Result \", data);\r\n        yield put(ProductActions.getSuccessfullyOnGoingProductsTasksToday(data));\r\n        yield put(GeneralActions.stopLoading(type));\r\n    } catch (err) {\r\n        console.error(\"Error in getOnGoingProducts saga:\", err);\r\n        yield put(GeneralActions.stopLoading(type));\r\n        yield put(GeneralActions.addError({\r\n            action: type,\r\n            message: err.response?.data?.error || err.message || \"Unknown error in getOnGoingProducts\"\r\n        }));\r\n    }\r\n}\r\n\r\nexport function *ProductWatcher() {\r\n    yield all([\r\n        yield takeLatest(ProductActions.createProduct.type, createProduct),\r\n        yield takeLatest(ProductActions.getProducts.type, getProducts),\r\n        yield takeLatest(ProductActions.updateProduct.type, updateProduct),\r\n        yield takeLatest(ProductActions.deleteProduct.type, deleteProduct),\r\n        yield takeLatest(ProductActions.createTaskByAdmin.type, createTaskByAdmin),\r\n        yield takeLatest(ProductActions.createProductsTaskByUser.type, createProductsTaskByUser),\r\n        yield takeLatest(ProductActions.getOnGoingProductsTasksToday.type, getOnGoingProductsTasksToday),\r\n        yield takeLatest(ProductActions.getProductById.type, getProductById),\r\n        yield takeLatest(ProductActions.getProductsByUser.type, getProductsByUser),\r\n        yield takeLatest(ProductActions.updateTask.type, updateTask),\r\n        yield takeLatest(ProductActions.startTask.type, startTask), // Start task watcher\r\n        yield takeLatest(ProductActions.stopTask.type, stopTask),   // Stop task watcher\r\n        yield takeLatest(ProductActions.pauseTask.type, pauseTask)  // Pause task watcher\r\n    ]);\r\n}"], "mappings": "AAAA,SAASA,cAAc,QAAQ,yBAAyB;AACxD,SAASC,cAAc,EAAEC,cAAc,QAAQ,mBAAmB;AAClE,SAASC,GAAG,EAAEC,IAAI,EAAEC,GAAG,EAAEC,UAAU,QAAQ,oBAAoB;AAE/D,UAAUC,aAAaA,CAAC;EAAEC,IAAI;EAAEC;AAAQ,CAAC,EAAE;EACvC,IAAI;IACA,MAAMJ,GAAG,CAACH,cAAc,CAACQ,WAAW,CAACF,IAAI,CAAC,CAAC;IAC3C,MAAMH,GAAG,CAACH,cAAc,CAACS,YAAY,CAACH,IAAI,CAAC,CAAC;IAC5CI,OAAO,CAACC,IAAI,CAAC,iBAAiB,EAAEJ,OAAO,CAAC;;IAExC;IACA,MAAMK,MAAM,GAAGL,OAAO,CAACK,MAAM,IAAI,CAAC,CAAC;IACnCF,OAAO,CAACC,IAAI,CAAC,oCAAoC,EAAEC,MAAM,CAAC;;IAE1D;IACA,MAAMC,MAAM,GAAG,MAAMX,IAAI,CAACJ,cAAc,CAACO,aAAa,EAAEE,OAAO,CAAC;;IAEhE;IACA,IAAIM,MAAM,CAACC,MAAM,KAAK,GAAG,EAAE;MACvB,MAAMC,SAAS,GAAG,MAAMF,MAAM,CAACG,IAAI,CAAC,CAAC;MACrC,MAAM,IAAIC,KAAK,CAACF,SAAS,CAACG,KAAK,IAAI,0BAA0B,CAAC;IAClE;;IAEA;IACA,MAAMC,SAAS,GAAG,MAAMjB,IAAI,CAACJ,cAAc,CAACsB,WAAW,EAAER,MAAM,CAAC;IAChEF,OAAO,CAACC,IAAI,CAAC,sBAAsB,EAAEQ,SAAS,CAAC;IAE/C,MAAMhB,GAAG,CAACJ,cAAc,CAACsB,uBAAuB,CAACF,SAAS,CAAC,CAAC;IAC5D,MAAMhB,GAAG,CAACH,cAAc,CAACsB,WAAW,CAAChB,IAAI,CAAC,CAAC;EAC/C,CAAC,CAAC,OAAOiB,GAAG,EAAE;IAAA,IAAAC,aAAA,EAAAC,kBAAA;IACVf,OAAO,CAACQ,KAAK,CAAC,8BAA8B,EAAEK,GAAG,CAAC;IAClD,MAAMpB,GAAG,CAACH,cAAc,CAACsB,WAAW,CAAChB,IAAI,CAAC,CAAC;IAC3C,MAAMH,GAAG,CAACH,cAAc,CAAC0B,QAAQ,CAAC;MAC9BC,MAAM,EAAErB,IAAI;MACZsB,OAAO,EAAE,EAAAJ,aAAA,GAAAD,GAAG,CAACM,QAAQ,cAAAL,aAAA,wBAAAC,kBAAA,GAAZD,aAAA,CAAcM,IAAI,cAAAL,kBAAA,uBAAlBA,kBAAA,CAAoBP,KAAK,KAAIK,GAAG,CAACK,OAAO,IAAI;IACzD,CAAC,CAAC,CAAC;EACP;AACJ;AAEA,UAAUG,iBAAiBA,CAAC;EAAEzB,IAAI;EAAEC;AAAQ,CAAC,EAAE;EAC3C,IAAI;IACA,MAAMJ,GAAG,CAACH,cAAc,CAACQ,WAAW,CAACF,IAAI,CAAC,CAAC;IAC3C,MAAMH,GAAG,CAACH,cAAc,CAACS,YAAY,CAACH,IAAI,CAAC,CAAC;IAC5CI,OAAO,CAACC,IAAI,CAAC,uBAAuB,EAAEJ,OAAO,CAAC;IAC9C,MAAMM,MAAM,GAAG,MAAMX,IAAI,CAACJ,cAAc,CAACkC,kBAAkB,EAAEzB,OAAO,CAAC0B,EAAE,EAAE1B,OAAO,CAAC;IACjF,MAAMY,SAAS,GAAG,MAAMjB,IAAI,CAACJ,cAAc,CAACsB,WAAW,CAAC;IACxD;IACAV,OAAO,CAACC,IAAI,CAAC,4BAA4B,EAAEQ,SAAS,CAAC;IACrD,MAAMhB,GAAG,CAACJ,cAAc,CAACsB,uBAAuB,CAACF,SAAS,CAAC,CAAC;IAC5D,MAAMhB,GAAG,CAACH,cAAc,CAACsB,WAAW,CAAChB,IAAI,CAAC,CAAC;EAC/C,CAAC,CAAC,OAAOiB,GAAG,EAAE;IAAA,IAAAW,cAAA,EAAAC,mBAAA;IACVzB,OAAO,CAACQ,KAAK,CAAC,kCAAkC,EAAEK,GAAG,CAAC;IACtD,MAAMpB,GAAG,CAACH,cAAc,CAACsB,WAAW,CAAChB,IAAI,CAAC,CAAC;IAC3C,MAAMH,GAAG,CAACH,cAAc,CAAC0B,QAAQ,CAAC;MAC9BC,MAAM,EAAErB,IAAI;MACZsB,OAAO,EAAE,EAAAM,cAAA,GAAAX,GAAG,CAACM,QAAQ,cAAAK,cAAA,wBAAAC,mBAAA,GAAZD,cAAA,CAAcJ,IAAI,cAAAK,mBAAA,uBAAlBA,mBAAA,CAAoBjB,KAAK,KAAIK,GAAG,CAACK,OAAO,IAAI;IACzD,CAAC,CAAC,CAAC;EACP;AACJ;AAEA,UAAUQ,aAAaA,CAAC;EAAE9B,IAAI;EAAEC;AAAQ,CAAC,EAAE;EACvC,IAAI;IACA,MAAMJ,GAAG,CAACH,cAAc,CAACQ,WAAW,CAACF,IAAI,CAAC,CAAC;IAC3C,MAAMH,GAAG,CAACH,cAAc,CAACS,YAAY,CAACH,IAAI,CAAC,CAAC;IAC5CI,OAAO,CAACC,IAAI,CAAC,iBAAiB,EAAEJ,OAAO,CAAC0B,EAAE,EAAE1B,OAAO,CAAC;IACpD,MAAM8B,OAAO,GAAG,MAAMnC,IAAI,CAACJ,cAAc,CAACsC,aAAa,EAAE7B,OAAO,CAAC0B,EAAE,EAAE1B,OAAO,CAAC;IAC7E;IACA,MAAM+B,KAAK,GAAG,MAAMD,OAAO,CAACrB,IAAI,CAAC,CAAC;IAClCN,OAAO,CAAC6B,GAAG,CAAC,yBAAyB,EAAED,KAAK,CAAC;IAC7C,MAAMzB,MAAM,GAAG,MAAMX,IAAI,CAACJ,cAAc,CAACsB,WAAW,CAAC;IACrD;IACAV,OAAO,CAAC6B,GAAG,CAAC,sBAAsB,EAAE1B,MAAM,CAAC;IAC3C,MAAMV,GAAG,CAACJ,cAAc,CAACsB,uBAAuB,CAACR,MAAM,CAAC,CAAC;IACzD,MAAMV,GAAG,CAACH,cAAc,CAACsB,WAAW,CAAChB,IAAI,CAAC,CAAC;EAC/C,CAAC,CAAC,OAAOiB,GAAG,EAAE;IAAA,IAAAiB,cAAA,EAAAC,mBAAA;IACV/B,OAAO,CAACQ,KAAK,CAAC,8BAA8B,EAAEK,GAAG,CAAC;IAClD,MAAMpB,GAAG,CAACH,cAAc,CAACsB,WAAW,CAAChB,IAAI,CAAC,CAAC;IAC3C,MAAMH,GAAG,CAACH,cAAc,CAAC0B,QAAQ,CAAC;MAC9BC,MAAM,EAAErB,IAAI;MACZsB,OAAO,EAAE,EAAAY,cAAA,GAAAjB,GAAG,CAACM,QAAQ,cAAAW,cAAA,wBAAAC,mBAAA,GAAZD,cAAA,CAAcV,IAAI,cAAAW,mBAAA,uBAAlBA,mBAAA,CAAoBvB,KAAK,KAAIK,GAAG,CAACK,OAAO,IAAI;IACzD,CAAC,CAAC,CAAC;EACP;AACJ;AAEA,UAAUc,aAAaA,CAAC;EAAEpC,IAAI;EAAEC;AAAQ,CAAC,EAAE;EACvC,IAAI;IACA,MAAMJ,GAAG,CAACH,cAAc,CAACQ,WAAW,CAACF,IAAI,CAAC,CAAC;IAC3C,MAAMH,GAAG,CAACH,cAAc,CAACS,YAAY,CAACH,IAAI,CAAC,CAAC;IAC5CI,OAAO,CAACC,IAAI,CAAC,yBAAyB,EAAEJ,OAAO,CAAC;IAChD,MAAMM,MAAM,GAAG,MAAMX,IAAI,CAACJ,cAAc,CAAC4C,aAAa,EAAEnC,OAAO,CAAC;IAChE;IACA,MAAMuB,IAAI,GAAG,MAAMjB,MAAM,CAACG,IAAI,CAAC,CAAC;IAChCN,OAAO,CAACC,IAAI,CAAC,8BAA8B,EAAEJ,OAAO,CAACoC,IAAI,CAAC;IAC1D,MAAMN,OAAO,GAAG,MAAMnC,IAAI,CAACJ,cAAc,CAACsB,WAAW,CAAC;IACtD;IACAV,OAAO,CAACC,IAAI,CAAC,6BAA6B,EAAE0B,OAAO,CAAC;IACpD,MAAMlC,GAAG,CAACJ,cAAc,CAACsB,uBAAuB,CAACgB,OAAO,CAAC,CAAC;IAC1D,MAAMlC,GAAG,CAACH,cAAc,CAACsB,WAAW,CAAChB,IAAI,CAAC,CAAC;EAC/C,CAAC,CAAC,OAAOiB,GAAG,EAAE;IAAA,IAAAqB,cAAA,EAAAC,mBAAA;IACVnC,OAAO,CAACQ,KAAK,CAAC,8BAA8B,EAAEK,GAAG,CAAC;IAClD,MAAMpB,GAAG,CAACH,cAAc,CAACsB,WAAW,CAAChB,IAAI,CAAC,CAAC;IAC3C,MAAMH,GAAG,CAACH,cAAc,CAAC0B,QAAQ,CAAC;MAC9BC,MAAM,EAAErB,IAAI;MACZsB,OAAO,EAAE,EAAAgB,cAAA,GAAArB,GAAG,CAACM,QAAQ,cAAAe,cAAA,wBAAAC,mBAAA,GAAZD,cAAA,CAAcd,IAAI,cAAAe,mBAAA,uBAAlBA,mBAAA,CAAoB3B,KAAK,KAAIK,GAAG,CAACK,OAAO,IAAI;IACzD,CAAC,CAAC,CAAC;EACP;AACJ;AAEA,UAAUR,WAAWA,CAAC;EAAEd,IAAI;EAAEC;AAAQ,CAAC,EAAE;EACrC,IAAI;IACA,MAAMJ,GAAG,CAACH,cAAc,CAACQ,WAAW,CAACF,IAAI,CAAC,CAAC;IAC3C,MAAMH,GAAG,CAACH,cAAc,CAACS,YAAY,CAACH,IAAI,CAAC,CAAC;IAC5CI,OAAO,CAACC,IAAI,CAAC,uBAAuB,EAAEJ,OAAO,CAAC;;IAE9C;IACA,MAAMK,MAAM,GAAGL,OAAO,IAAI,CAAC,CAAC;IAC5BG,OAAO,CAACC,IAAI,CAAC,+BAA+B,EAAEC,MAAM,CAAC;;IAErD;IACA,MAAMC,MAAM,GAAG,MAAMX,IAAI,CAACJ,cAAc,CAACsB,WAAW,EAAER,MAAM,CAAC;IAC7DF,OAAO,CAACC,IAAI,CAAC,sBAAsB,EAAEE,MAAM,CAAC;IAE5C,MAAMV,GAAG,CAACJ,cAAc,CAACsB,uBAAuB,CAACR,MAAM,CAAC,CAAC;IACzD,MAAMV,GAAG,CAACH,cAAc,CAACsB,WAAW,CAAChB,IAAI,CAAC,CAAC;EAC/C,CAAC,CAAC,OAAOiB,GAAG,EAAE;IAAA,IAAAuB,cAAA,EAAAC,mBAAA;IACVrC,OAAO,CAACQ,KAAK,CAAC,4BAA4B,EAAEK,GAAG,CAAC;IAChD,MAAMpB,GAAG,CAACH,cAAc,CAACsB,WAAW,CAAChB,IAAI,CAAC,CAAC;IAC3C,MAAMH,GAAG,CAACH,cAAc,CAAC0B,QAAQ,CAAC;MAC9BC,MAAM,EAAErB,IAAI;MACZsB,OAAO,EAAE,EAAAkB,cAAA,GAAAvB,GAAG,CAACM,QAAQ,cAAAiB,cAAA,wBAAAC,mBAAA,GAAZD,cAAA,CAAchB,IAAI,cAAAiB,mBAAA,uBAAlBA,mBAAA,CAAoB7B,KAAK,KAAIK,GAAG,CAACK,OAAO,IAAI;IACzD,CAAC,CAAC,CAAC;EACP;AACJ;AAEA,UAAUoB,wBAAwBA,CAAC;EAAE1C,IAAI;EAAEC;AAAQ,CAAC,EAAE;EAClD,IAAI;IACA,MAAMJ,GAAG,CAACH,cAAc,CAACQ,WAAW,CAACF,IAAI,CAAC,CAAC;IAC3C,MAAMH,GAAG,CAACH,cAAc,CAACS,YAAY,CAACH,IAAI,CAAC,CAAC;IAC5CI,OAAO,CAAC6B,GAAG,CAAC,4BAA4B,EAAEhC,OAAO,CAAC;;IAElD;IACA,IAAIA,OAAO,CAAC0C,QAAQ,EAAE;MAClBvC,OAAO,CAAC6B,GAAG,CAAC,uBAAuB,EAAEhC,OAAO,CAAC0C,QAAQ,CAAC;MACtDvC,OAAO,CAAC6B,GAAG,CAAC,mBAAmB,EAAEhC,OAAO,CAAC2C,WAAW,CAAC;IACzD;;IAEA;IACA,MAAMrC,MAAM,GAAG,MAAMX,IAAI,CAACJ,cAAc,CAACkC,kBAAkB,EAAEzB,OAAO,CAAC0B,EAAE,EAAE1B,OAAO,CAAC;IACjFG,OAAO,CAAC6B,GAAG,CAAC,uBAAuB,EAAE1B,MAAM,CAAC;IAE5C,MAAMM,SAAS,GAAG,MAAMjB,IAAI,CAACJ,cAAc,CAACsB,WAAW,CAAC;IACxDV,OAAO,CAACC,IAAI,CAAC,+BAA+B,EAAEQ,SAAS,CAAC;IAExD,MAAMhB,GAAG,CAACJ,cAAc,CAACsB,uBAAuB,CAACF,SAAS,CAAC,CAAC;IAC5D,MAAMhB,GAAG,CAACH,cAAc,CAACsB,WAAW,CAAChB,IAAI,CAAC,CAAC;EAC/C,CAAC,CAAC,OAAOiB,GAAG,EAAE;IAAA,IAAA4B,cAAA,EAAAC,mBAAA;IACV1C,OAAO,CAACQ,KAAK,CAAC,yCAAyC,EAAEK,GAAG,CAAC;IAC7D,MAAMpB,GAAG,CAACH,cAAc,CAACsB,WAAW,CAAChB,IAAI,CAAC,CAAC;IAC3C,MAAMH,GAAG,CAACH,cAAc,CAAC0B,QAAQ,CAAC;MAC9BC,MAAM,EAAErB,IAAI;MACZsB,OAAO,EAAE,EAAAuB,cAAA,GAAA5B,GAAG,CAACM,QAAQ,cAAAsB,cAAA,wBAAAC,mBAAA,GAAZD,cAAA,CAAcrB,IAAI,cAAAsB,mBAAA,uBAAlBA,mBAAA,CAAoBlC,KAAK,KAAIK,GAAG,CAACK,OAAO,IAAI;IACzD,CAAC,CAAC,CAAC;EACP;AACJ;AAEA,UAAUyB,iBAAiBA,CAAC;EAAE/C,IAAI;EAAEC;AAAQ,CAAC,EAAE;EAC3C,IAAI;IACA,MAAMJ,GAAG,CAACH,cAAc,CAACQ,WAAW,CAACF,IAAI,CAAC,CAAC;IAC3C,MAAMH,GAAG,CAACH,cAAc,CAACS,YAAY,CAACH,IAAI,CAAC,CAAC;IAC5CI,OAAO,CAACC,IAAI,CAAC,4BAA4B,EAAEJ,OAAO,CAAC0B,EAAE,CAAC;IACtD,MAAMpB,MAAM,GAAG,MAAMX,IAAI,CAACJ,cAAc,CAACuD,iBAAiB,EAAE9C,OAAO,CAAC0B,EAAE,CAAC;IACvE;IACA,MAAMH,IAAI,GAAG,MAAMjB,MAAM,CAACG,IAAI,CAAC,CAAC;IAChCN,OAAO,CAAC6B,GAAG,CAAC,8BAA8B,EAAET,IAAI,CAAC;IACjD,MAAM3B,GAAG,CAACJ,cAAc,CAACsB,uBAAuB,CAACS,IAAI,CAAC,CAAC;IACvD,MAAM3B,GAAG,CAACH,cAAc,CAACsB,WAAW,CAAChB,IAAI,CAAC,CAAC;EAC/C,CAAC,CAAC,OAAOiB,GAAG,EAAE;IAAA,IAAA+B,cAAA,EAAAC,mBAAA;IACV7C,OAAO,CAACQ,KAAK,CAAC,kCAAkC,EAAEK,GAAG,CAAC;IACtD,MAAMpB,GAAG,CAACH,cAAc,CAACsB,WAAW,CAAChB,IAAI,CAAC,CAAC;IAC3C,MAAMH,GAAG,CAACH,cAAc,CAAC0B,QAAQ,CAAC;MAC9BC,MAAM,EAAErB,IAAI;MACZsB,OAAO,EAAE,EAAA0B,cAAA,GAAA/B,GAAG,CAACM,QAAQ,cAAAyB,cAAA,wBAAAC,mBAAA,GAAZD,cAAA,CAAcxB,IAAI,cAAAyB,mBAAA,uBAAlBA,mBAAA,CAAoBrC,KAAK,KAAIK,GAAG,CAACK,OAAO,IAAI;IACzD,CAAC,CAAC,CAAC;EACP;AACJ;AAEA,UAAU4B,cAAcA,CAAC;EAAElD,IAAI;EAAEC;AAAQ,CAAC,EAAE;EACxC,IAAI;IACA,MAAMJ,GAAG,CAACH,cAAc,CAACQ,WAAW,CAACF,IAAI,CAAC,CAAC;IAC3C,MAAMH,GAAG,CAACH,cAAc,CAACS,YAAY,CAACH,IAAI,CAAC,CAAC;IAC5CI,OAAO,CAACC,IAAI,CAAC,8BAA8B,EAAEJ,OAAO,CAAC;IACrD,MAAMM,MAAM,GAAG,MAAMX,IAAI,CAACJ,cAAc,CAAC0D,cAAc,EAAEjD,OAAO,CAAC0B,EAAE,CAAC;IACpE;IACA,MAAMH,IAAI,GAAG,MAAMjB,MAAM,CAACG,IAAI,CAAC,CAAC;IAChCN,OAAO,CAAC6B,GAAG,CAAC,2BAA2B,EAAET,IAAI,CAAC;IAC9C,MAAM3B,GAAG,CAACJ,cAAc,CAAC0D,0BAA0B,CAAC3B,IAAI,CAAC,CAAC;IAC1D,MAAM3B,GAAG,CAACH,cAAc,CAACsB,WAAW,CAAChB,IAAI,CAAC,CAAC;EAC/C,CAAC,CAAC,OAAOiB,GAAG,EAAE;IAAA,IAAAmC,cAAA,EAAAC,mBAAA;IACVjD,OAAO,CAACQ,KAAK,CAAC,+BAA+B,EAAEK,GAAG,CAAC;IACnD,MAAMpB,GAAG,CAACH,cAAc,CAACsB,WAAW,CAAChB,IAAI,CAAC,CAAC;IAC3C,MAAMH,GAAG,CAACH,cAAc,CAAC0B,QAAQ,CAAC;MAC9BC,MAAM,EAAErB,IAAI;MACZsB,OAAO,EAAE,EAAA8B,cAAA,GAAAnC,GAAG,CAACM,QAAQ,cAAA6B,cAAA,wBAAAC,mBAAA,GAAZD,cAAA,CAAc5B,IAAI,cAAA6B,mBAAA,uBAAlBA,mBAAA,CAAoBzC,KAAK,KAAIK,GAAG,CAACK,OAAO,IAAI;IACzD,CAAC,CAAC,CAAC;EACP;AACJ;AACA,UAAUgC,UAAUA,CAAC;EAAEtD,IAAI;EAAEC;AAAQ,CAAC,EAAE;EACpC,IAAI;IACA,MAAMJ,GAAG,CAACH,cAAc,CAACQ,WAAW,CAACF,IAAI,CAAC,CAAC;IAC3C,MAAMH,GAAG,CAACH,cAAc,CAACS,YAAY,CAACH,IAAI,CAAC,CAAC;IAE5CI,OAAO,CAACC,IAAI,CAAC,gBAAgB,EAAEJ,OAAO,CAAC;;IAEvC;IACA,MAAMsD,SAAS,GAAGtD,OAAO,CAACsD,SAAS,IAAItD,OAAO,CAACuD,SAAS;IACxD,MAAMC,MAAM,GAAGxD,OAAO,CAACwD,MAAM,IAAIxD,OAAO,CAACyD,MAAM;IAC/C,MAAMC,IAAI,GAAG1D,OAAO,CAAC2D,WAAW,IAAI3D,OAAO,CAAC0D,IAAI;IAEhD,IAAI,CAACJ,SAAS,IAAI,CAACE,MAAM,EAAE;MACvB,MAAM,IAAI9C,KAAK,CAAC,oCAAoC,CAAC;IACzD;IAEAP,OAAO,CAAC6B,GAAG,CAAC,iBAAiBwB,MAAM,eAAeF,SAAS,aAAa,EAAEI,IAAI,CAAC;;IAE/E;IACA,MAAM5B,OAAO,GAAG,MAAMnC,IAAI,CAACJ,cAAc,CAAC8D,UAAU,EAAEC,SAAS,EAAEE,MAAM,EAAEE,IAAI,IAAI,CAAC,CAAC,CAAC;IACpF,MAAM3B,KAAK,GAAGD,OAAO;IACrB3B,OAAO,CAAC6B,GAAG,CAAC,qBAAqB,EAAED,KAAK,CAAC;;IAEzC;IACA,MAAMzB,MAAM,GAAG,MAAMX,IAAI,CAACJ,cAAc,CAACsB,WAAW,CAAC;IACrD,MAAMjB,GAAG,CAACJ,cAAc,CAACsB,uBAAuB,CAACR,MAAM,CAAC,CAAC;IACzD,MAAMV,GAAG,CAACH,cAAc,CAACsB,WAAW,CAAChB,IAAI,CAAC,CAAC;EAC/C,CAAC,CAAC,OAAOiB,GAAG,EAAE;IAAA,IAAA4C,cAAA,EAAAC,mBAAA;IACV1D,OAAO,CAACQ,KAAK,CAAC,2BAA2B,EAAEK,GAAG,CAAC;IAC/C,MAAMpB,GAAG,CAACH,cAAc,CAACsB,WAAW,CAAChB,IAAI,CAAC,CAAC;IAC3C,MAAMH,GAAG,CAACH,cAAc,CAAC0B,QAAQ,CAAC;MAC9BC,MAAM,EAAErB,IAAI;MACZsB,OAAO,EAAE,EAAAuC,cAAA,GAAA5C,GAAG,CAACM,QAAQ,cAAAsC,cAAA,wBAAAC,mBAAA,GAAZD,cAAA,CAAcrC,IAAI,cAAAsC,mBAAA,uBAAlBA,mBAAA,CAAoBlD,KAAK,KAAIK,GAAG,CAACK,OAAO,IAAI;IACzD,CAAC,CAAC,CAAC;EACP;AACJ;;AAGA;AACA,UAAUyC,SAASA,CAAC;EAAE/D,IAAI;EAAEC;AAAQ,CAAC,EAAE;EACnC,IAAI;IACA,MAAMJ,GAAG,CAACH,cAAc,CAACQ,WAAW,CAACF,IAAI,CAAC,CAAC;IAC3C,MAAMH,GAAG,CAACH,cAAc,CAACS,YAAY,CAACH,IAAI,CAAC,CAAC;IAC5CI,OAAO,CAACC,IAAI,CAAC,gBAAgB,EAAEJ,OAAO,CAACwD,MAAM,EAAExD,OAAO,CAAC+D,SAAS,CAAC;;IAEjE;IACA,MAAMzD,MAAM,GAAG,MAAMX,IAAI,CAACJ,cAAc,CAACuE,SAAS,EAAE9D,OAAO,CAAC;IAC5DG,OAAO,CAAC6B,GAAG,CAAC,sBAAsB,EAAE1B,MAAM,CAAC;;IAE3C;IACA,MAAM0D,cAAc,GAAG,MAAMrE,IAAI,CAACJ,cAAc,CAACsB,WAAW,CAAC;IAC7D,MAAMjB,GAAG,CAACJ,cAAc,CAACsB,uBAAuB,CAACkD,cAAc,CAAC,CAAC;IACjE,MAAMpE,GAAG,CAACH,cAAc,CAACsB,WAAW,CAAChB,IAAI,CAAC,CAAC;EAC/C,CAAC,CAAC,OAAOiB,GAAG,EAAE;IAAA,IAAAiD,cAAA,EAAAC,mBAAA;IACV/D,OAAO,CAACQ,KAAK,CAAC,0BAA0B,EAAEK,GAAG,CAAC;IAC9C,MAAMpB,GAAG,CAACH,cAAc,CAACsB,WAAW,CAAChB,IAAI,CAAC,CAAC;IAC3C,MAAMH,GAAG,CAACH,cAAc,CAAC0B,QAAQ,CAAC;MAC9BC,MAAM,EAAErB,IAAI;MACZsB,OAAO,EAAE,EAAA4C,cAAA,GAAAjD,GAAG,CAACM,QAAQ,cAAA2C,cAAA,wBAAAC,mBAAA,GAAZD,cAAA,CAAc1C,IAAI,cAAA2C,mBAAA,uBAAlBA,mBAAA,CAAoBvD,KAAK,KAAIK,GAAG,CAACK,OAAO,IAAI;IACzD,CAAC,CAAC,CAAC;EACP;AACJ;;AAEA;AACA,UAAU8C,QAAQA,CAAC;EAAEpE,IAAI;EAAEC;AAAQ,CAAC,EAAE;EAClC,IAAI;IACA,MAAMJ,GAAG,CAACH,cAAc,CAACQ,WAAW,CAACF,IAAI,CAAC,CAAC;IAC3C,MAAMH,GAAG,CAACH,cAAc,CAACS,YAAY,CAACH,IAAI,CAAC,CAAC;IAC5CI,OAAO,CAACC,IAAI,CAAC,gBAAgB,EAAEJ,OAAO,CAACwD,MAAM,EAAExD,OAAO,CAAC+D,SAAS,CAAC;;IAEjE;IACA,MAAMzD,MAAM,GAAG,MAAMX,IAAI,CAACJ,cAAc,CAAC4E,QAAQ,EAAEnE,OAAO,CAAC;IAC3DG,OAAO,CAAC6B,GAAG,CAAC,sBAAsB,EAAE1B,MAAM,CAAC;;IAE3C;IACA,MAAM0D,cAAc,GAAG,MAAMrE,IAAI,CAACJ,cAAc,CAACsB,WAAW,CAAC;IAC7D,MAAMjB,GAAG,CAACJ,cAAc,CAACsB,uBAAuB,CAACkD,cAAc,CAAC,CAAC;IACjE,MAAMpE,GAAG,CAACH,cAAc,CAACsB,WAAW,CAAChB,IAAI,CAAC,CAAC;EAC/C,CAAC,CAAC,OAAOiB,GAAG,EAAE;IAAA,IAAAoD,cAAA,EAAAC,mBAAA;IACVlE,OAAO,CAACQ,KAAK,CAAC,yBAAyB,EAAEK,GAAG,CAAC;IAC7C,MAAMpB,GAAG,CAACH,cAAc,CAACsB,WAAW,CAAChB,IAAI,CAAC,CAAC;IAC3C,MAAMH,GAAG,CAACH,cAAc,CAAC0B,QAAQ,CAAC;MAC9BC,MAAM,EAAErB,IAAI;MACZsB,OAAO,EAAE,EAAA+C,cAAA,GAAApD,GAAG,CAACM,QAAQ,cAAA8C,cAAA,wBAAAC,mBAAA,GAAZD,cAAA,CAAc7C,IAAI,cAAA8C,mBAAA,uBAAlBA,mBAAA,CAAoB1D,KAAK,KAAIK,GAAG,CAACK,OAAO,IAAI;IACzD,CAAC,CAAC,CAAC;EACP;AACJ;;AAEA;AACA,UAAUiD,SAASA,CAAC;EAAEvE,IAAI;EAAEC;AAAQ,CAAC,EAAE;EACnC,IAAI;IACA,MAAMJ,GAAG,CAACH,cAAc,CAACQ,WAAW,CAACF,IAAI,CAAC,CAAC;IAC3C,MAAMH,GAAG,CAACH,cAAc,CAACS,YAAY,CAACH,IAAI,CAAC,CAAC;IAC5CI,OAAO,CAACC,IAAI,CAAC,eAAe,EAAEJ,OAAO,CAACwD,MAAM,EAAExD,OAAO,CAAC+D,SAAS,CAAC;IAChE5D,OAAO,CAAC6B,GAAG,CAAC,gBAAgB,EAAEhC,OAAO,CAAC;;IAEtC;IACA,MAAMM,MAAM,GAAG,MAAMX,IAAI,CAACJ,cAAc,CAAC+E,SAAS,EAAEtE,OAAO,CAAC;IAC5DG,OAAO,CAAC6B,GAAG,CAAC,qBAAqB,EAAE1B,MAAM,CAAC;;IAE1C;IACA,MAAM0D,cAAc,GAAG,MAAMrE,IAAI,CAACJ,cAAc,CAACsB,WAAW,CAAC;IAC7D,MAAMjB,GAAG,CAACJ,cAAc,CAACsB,uBAAuB,CAACkD,cAAc,CAAC,CAAC;IACjE,MAAMpE,GAAG,CAACH,cAAc,CAACsB,WAAW,CAAChB,IAAI,CAAC,CAAC;EAC/C,CAAC,CAAC,OAAOiB,GAAG,EAAE;IAAA,IAAAuD,eAAA,EAAAC,oBAAA;IACVrE,OAAO,CAACQ,KAAK,CAAC,0BAA0B,EAAEK,GAAG,CAAC;IAC9C,MAAMpB,GAAG,CAACH,cAAc,CAACsB,WAAW,CAAChB,IAAI,CAAC,CAAC;IAC3C,MAAMH,GAAG,CAACH,cAAc,CAAC0B,QAAQ,CAAC;MAC9BC,MAAM,EAAErB,IAAI;MACZsB,OAAO,EAAE,EAAAkD,eAAA,GAAAvD,GAAG,CAACM,QAAQ,cAAAiD,eAAA,wBAAAC,oBAAA,GAAZD,eAAA,CAAchD,IAAI,cAAAiD,oBAAA,uBAAlBA,oBAAA,CAAoB7D,KAAK,KAAIK,GAAG,CAACK,OAAO,IAAI;IACzD,CAAC,CAAC,CAAC;EACP;AACJ;AAEA,UAAUoD,4BAA4BA,CAAC;EAAE1E,IAAI;EAAEC;AAAQ,CAAC,EAAE;EACtD,IAAI;IACA,MAAMJ,GAAG,CAACH,cAAc,CAACQ,WAAW,CAACF,IAAI,CAAC,CAAC;IAC3C,MAAMH,GAAG,CAACH,cAAc,CAACS,YAAY,CAACH,IAAI,CAAC,CAAC;IAC5CI,OAAO,CAACC,IAAI,CAAC,uBAAuB,CAAC;IACrC,MAAME,MAAM,GAAG,MAAMX,IAAI,CAACJ,cAAc,CAACkF,4BAA4B,CAAC;IACtE;IACA,MAAMlD,IAAI,GAAG,MAAMjB,MAAM,CAACG,IAAI,CAAC,CAAC;IAChCN,OAAO,CAAC6B,GAAG,CAAC,+BAA+B,EAAET,IAAI,CAAC;IAClD,MAAM3B,GAAG,CAACJ,cAAc,CAACkF,wCAAwC,CAACnD,IAAI,CAAC,CAAC;IACxE,MAAM3B,GAAG,CAACH,cAAc,CAACsB,WAAW,CAAChB,IAAI,CAAC,CAAC;EAC/C,CAAC,CAAC,OAAOiB,GAAG,EAAE;IAAA,IAAA2D,eAAA,EAAAC,oBAAA;IACVzE,OAAO,CAACQ,KAAK,CAAC,mCAAmC,EAAEK,GAAG,CAAC;IACvD,MAAMpB,GAAG,CAACH,cAAc,CAACsB,WAAW,CAAChB,IAAI,CAAC,CAAC;IAC3C,MAAMH,GAAG,CAACH,cAAc,CAAC0B,QAAQ,CAAC;MAC9BC,MAAM,EAAErB,IAAI;MACZsB,OAAO,EAAE,EAAAsD,eAAA,GAAA3D,GAAG,CAACM,QAAQ,cAAAqD,eAAA,wBAAAC,oBAAA,GAAZD,eAAA,CAAcpD,IAAI,cAAAqD,oBAAA,uBAAlBA,oBAAA,CAAoBjE,KAAK,KAAIK,GAAG,CAACK,OAAO,IAAI;IACzD,CAAC,CAAC,CAAC;EACP;AACJ;AAEA,OAAO,UAAUwD,cAAcA,CAAA,EAAG;EAC9B,MAAMnF,GAAG,CAAC,CACN,MAAMG,UAAU,CAACL,cAAc,CAACM,aAAa,CAACC,IAAI,EAAED,aAAa,CAAC,EAClE,MAAMD,UAAU,CAACL,cAAc,CAACqB,WAAW,CAACd,IAAI,EAAEc,WAAW,CAAC,EAC9D,MAAMhB,UAAU,CAACL,cAAc,CAACqC,aAAa,CAAC9B,IAAI,EAAE8B,aAAa,CAAC,EAClE,MAAMhC,UAAU,CAACL,cAAc,CAAC2C,aAAa,CAACpC,IAAI,EAAEoC,aAAa,CAAC,EAClE,MAAMtC,UAAU,CAACL,cAAc,CAACgC,iBAAiB,CAACzB,IAAI,EAAEyB,iBAAiB,CAAC,EAC1E,MAAM3B,UAAU,CAACL,cAAc,CAACiD,wBAAwB,CAAC1C,IAAI,EAAE0C,wBAAwB,CAAC,EACxF,MAAM5C,UAAU,CAACL,cAAc,CAACiF,4BAA4B,CAAC1E,IAAI,EAAE0E,4BAA4B,CAAC,EAChG,MAAM5E,UAAU,CAACL,cAAc,CAACyD,cAAc,CAAClD,IAAI,EAAEkD,cAAc,CAAC,EACpE,MAAMpD,UAAU,CAACL,cAAc,CAACsD,iBAAiB,CAAC/C,IAAI,EAAE+C,iBAAiB,CAAC,EAC1E,MAAMjD,UAAU,CAACL,cAAc,CAAC6D,UAAU,CAACtD,IAAI,EAAEsD,UAAU,CAAC,EAC5D,MAAMxD,UAAU,CAACL,cAAc,CAACsE,SAAS,CAAC/D,IAAI,EAAE+D,SAAS,CAAC;EAAE;EAC5D,MAAMjE,UAAU,CAACL,cAAc,CAAC2E,QAAQ,CAACpE,IAAI,EAAEoE,QAAQ,CAAC;EAAI;EAC5D,MAAMtE,UAAU,CAACL,cAAc,CAAC8E,SAAS,CAACvE,IAAI,EAAEuE,SAAS,CAAC,CAAE;EAAA,CAC/D,CAAC;AACN;AAACQ,EAAA,GAhBgBD,cAAc;AAAA,IAAAC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}