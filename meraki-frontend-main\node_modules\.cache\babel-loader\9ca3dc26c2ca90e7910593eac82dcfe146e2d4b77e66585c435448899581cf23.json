{"ast": null, "code": "var _jsxFileName = \"E:\\\\Suraj Patil\\\\Organization_Management_System\\\\meraki-frontend-main\\\\src\\\\screens\\\\User\\\\components\\\\Form\\\\BasicInformation.js\",\n  _s = $RefreshSig$();\nimport React, { useEffect, useState } from \"react\";\nimport { Box, Button, Card, FormControl, Grid, InputBase, MenuItem, Typography, useTheme } from \"@mui/material\";\nimport { Autocomplete } from \"@mui/lab\";\nimport COUNTRIES from \"constants/countries\";\nimport { useDispatch, useSelector } from \"react-redux\";\nimport { DepartmentSelector, DesignationSelector, GeneralSelector } from \"selectors\";\nimport { DepartmentActions, DesignationActions, GeneralActions, UserActions } from \"slices/actions\";\nimport { useFormik } from \"formik\";\nimport Input from \"components/Input\";\nimport SelectField from \"components/SelectField\";\nimport { toast } from \"react-toastify\";\nimport PropTypes from \"prop-types\";\nimport Can from \"../../../../utils/can\";\nimport { actions, features } from \"../../../../constants/permission\";\nimport { SCHEDULE_TEMPLATES, DEFAULT_WORK_SCHEDULE, TIME_OPTIONS } from \"../../../../constants/workSchedule\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nBasicInformation.propTypes = {\n  user: PropTypes.object,\n  form: PropTypes.object\n};\nexport default function BasicInformation(props) {\n  _s();\n  var _user$name, _user$country, _user$city, _user$address, _user$department$_id, _user$department, _user$designation$_id, _user$designation, _user$workHours, _user$workSchedule, _formik$values$workSc, _formik$values$workSc2;\n  const {\n    user,\n    form\n  } = props;\n  const dispatch = useDispatch();\n  const theme = useTheme();\n  const departments = useSelector(DepartmentSelector.getDepartments());\n  const designations = useSelector(DesignationSelector.getDesignations());\n  const success = useSelector(GeneralSelector.success(UserActions.updateUser.type));\n  const [hoursFormat, setHoursFormat] = useState(\"decimal\"); // \"decimal\" or \"hhmm\"\n\n  const countries = COUNTRIES.map(item => ({\n    id: item.id,\n    name: item.name,\n    phoneCode: item.phoneCode,\n    flag: item.flag\n  }));\n  useEffect(() => {\n    dispatch(DepartmentActions.getDepartments());\n    dispatch(DesignationActions.getDesignations());\n  }, []);\n  useEffect(() => {\n    if (success) {\n      var _success$message;\n      toast.success(`${(_success$message = success === null || success === void 0 ? void 0 : success.message) !== null && _success$message !== void 0 ? _success$message : \"Success\"}`, {\n        position: \"top-right\",\n        autoClose: 3000,\n        closeOnClick: true\n      });\n      dispatch(GeneralActions.removeSuccess(UserActions.updateUser.type));\n    }\n  }, [success]);\n\n  // Helper function to convert between decimal and HH:MM formats\n  const convertWorkHours = (value, toFormat) => {\n    if (!value) {\n      return \"\";\n    }\n    if (toFormat === \"decimal\") {\n      // Convert from HH:MM to decimal\n      if (value.includes(\":\")) {\n        const [hours, minutes] = value.split(\":\");\n        return Number(hours) + Number(minutes) / 60;\n      }\n      return value;\n    } else {\n      // Convert from decimal to HH:MM\n      const hours = Math.floor(Number(value));\n      const minutes = Math.round((Number(value) - hours) * 60);\n      return `${hours}:${minutes.toString().padStart(2, '0')}`;\n    }\n  };\n  const formik = useFormik({\n    initialValues: {\n      name: (_user$name = user === null || user === void 0 ? void 0 : user.name) !== null && _user$name !== void 0 ? _user$name : \"\",\n      phoneCode: '',\n      phoneNumber: \"\",\n      country: (_user$country = user === null || user === void 0 ? void 0 : user.country) !== null && _user$country !== void 0 ? _user$country : \"\",\n      city: (_user$city = user === null || user === void 0 ? void 0 : user.city) !== null && _user$city !== void 0 ? _user$city : \"\",\n      address: (_user$address = user === null || user === void 0 ? void 0 : user.address) !== null && _user$address !== void 0 ? _user$address : \"\",\n      department: (_user$department$_id = user === null || user === void 0 ? void 0 : (_user$department = user.department) === null || _user$department === void 0 ? void 0 : _user$department._id) !== null && _user$department$_id !== void 0 ? _user$department$_id : \"\",\n      designation: (_user$designation$_id = user === null || user === void 0 ? void 0 : (_user$designation = user.designation) === null || _user$designation === void 0 ? void 0 : _user$designation._id) !== null && _user$designation$_id !== void 0 ? _user$designation$_id : \"\",\n      workHours: (_user$workHours = user === null || user === void 0 ? void 0 : user.workHours) !== null && _user$workHours !== void 0 ? _user$workHours : \"8.5\",\n      // Default to 8.5 hours or use stored value\n      workSchedule: (_user$workSchedule = user === null || user === void 0 ? void 0 : user.workSchedule) !== null && _user$workSchedule !== void 0 ? _user$workSchedule : DEFAULT_WORK_SCHEDULE\n    },\n    enableReinitialize: true,\n    validateOnChange: true,\n    onSubmit: values => {\n      handleSubmit(values);\n    }\n  });\n  useEffect(() => {\n    var _formik$values$countr;\n    const code = (_formik$values$countr = formik.values.country) === null || _formik$values$countr === void 0 ? void 0 : _formik$values$countr.phoneCode;\n    const phone = formik.values.phone;\n    formik.setFieldValue('phoneCode', code !== null && code !== void 0 ? code : '');\n    formik.setFieldValue('phone', phone);\n  }, [formik.values.country]);\n  useEffect(() => {\n    const phone = user.phone;\n    const country = countries.find(e => e.name === user.country);\n    if (country) {\n      formik.setFieldValue('country', country);\n    }\n    if (phone && country) {\n      var _code$length;\n      const code = country.phoneCode;\n      formik.setFieldValue('phoneCode', code !== null && code !== void 0 ? code : '');\n      formik.setFieldValue('phoneNumber', phone.substring((_code$length = code.length) !== null && _code$length !== void 0 ? _code$length : 0));\n    }\n  }, [user]);\n  const handleSubmit = values => {\n    // Convert work hours to decimal format for storage\n    const workHoursDecimal = hoursFormat === \"decimal\" ? values.workHours : convertWorkHours(values.workHours, \"decimal\");\n\n    // Ensure workHours is a number, not a string\n    const workHoursNumber = parseFloat(workHoursDecimal);\n    const params = {\n      id: user._id,\n      ...values,\n      ...form,\n      phone: values.phoneCode + values.phoneNumber,\n      workHours: workHoursNumber,\n      workSchedule: values.workSchedule\n    };\n    dispatch(UserActions.updateUser(params));\n  };\n  const handleWorkHoursChange = e => {\n    const {\n      value\n    } = e.target;\n    formik.setFieldValue('workHours', value);\n  };\n  const toggleHoursFormat = () => {\n    const newFormat = hoursFormat === \"decimal\" ? \"hhmm\" : \"decimal\";\n    const convertedValue = convertWorkHours(formik.values.workHours, newFormat);\n    setHoursFormat(newFormat);\n    formik.setFieldValue('workHours', convertedValue);\n  };\n  const handleWorkScheduleChange = (field, value) => {\n    formik.setFieldValue(`workSchedule.${field}`, value);\n  };\n\n  // Helper function to format date for input field\n  const formatDateForInput = dateValue => {\n    if (!dateValue) {\n      return new Date().toISOString().split('T')[0]; // Current date\n    }\n    if (typeof dateValue === 'string') {\n      return dateValue.split('T')[0];\n    }\n    return new Date(dateValue).toISOString().split('T')[0];\n  };\n\n  // Helper functions to get work schedule values with defaults\n  const getWorkScheduleValue = (field, defaultValue) => {\n    const workSchedule = formik.values.workSchedule;\n    if (!workSchedule || workSchedule[field] === undefined) {\n      return defaultValue;\n    }\n    return workSchedule[field];\n  };\n  return /*#__PURE__*/_jsxDEV(Card, {\n    children: [/*#__PURE__*/_jsxDEV(Typography, {\n      variant: \"h5\",\n      sx: {\n        mb: 4\n      },\n      children: \"Basic Information\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 190,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n      onSubmit: formik.handleSubmit,\n      children: /*#__PURE__*/_jsxDEV(Grid, {\n        container: true,\n        spacing: 2,\n        children: [/*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          lg: 6,\n          xs: 12,\n          children: /*#__PURE__*/_jsxDEV(Input, {\n            label: \"Full Name\",\n            name: \"name\",\n            value: formik.values.name,\n            onChange: formik.handleChange,\n            error: formik.touched.name && Boolean(formik.errors.name),\n            helpertext: formik.touched.name ? formik.errors.name : \"\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 194,\n            columnNumber: 25\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 193,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          lg: 6,\n          xs: 12,\n          children: /*#__PURE__*/_jsxDEV(FormControl, {\n            fullWidth: true,\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"caption\",\n              children: \"Country\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 205,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(Autocomplete, {\n              disablePortal: true,\n              name: \"country\",\n              options: countries,\n              value: formik.values.country,\n              onChange: (e, val) => {\n                formik.setFieldValue('country', val);\n              },\n              error: formik.touched.country && Boolean(formik.errors.country),\n              helpertext: formik.touched.country ? formik.errors.country : \"\",\n              getOptionLabel: option => {\n                var _option$name;\n                return (_option$name = option.name) !== null && _option$name !== void 0 ? _option$name : '';\n              },\n              renderOption: (props, option) => /*#__PURE__*/_jsxDEV(Box, {\n                component: \"li\",\n                sx: {\n                  '& > img': {\n                    mr: 2,\n                    flexShrink: 0\n                  }\n                },\n                ...props,\n                children: [option.flag, \" \", option.name]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 219,\n                columnNumber: 37\n              }, this),\n              renderInput: params => /*#__PURE__*/_jsxDEV(InputBase, {\n                ...params.InputProps,\n                ...params\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 223,\n                columnNumber: 58\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 206,\n              columnNumber: 29\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 204,\n            columnNumber: 25\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 203,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          lg: 6,\n          xs: 12,\n          children: /*#__PURE__*/_jsxDEV(FormControl, {\n            fullWidth: true,\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"caption\",\n              children: \"Phone Number\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 229,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                display: 'flex',\n                gap: 1.5\n              },\n              children: [/*#__PURE__*/_jsxDEV(Box, {\n                sx: {\n                  width: 80\n                },\n                children: /*#__PURE__*/_jsxDEV(Input, {\n                  sx: {\n                    textAlign: 'center',\n                    '& .Mui-disabled': {\n                      fillColor: theme.palette.common.black\n                    }\n                  },\n                  autoComplete: \"tel-country-code\",\n                  name: \"phoneCode\",\n                  startAdornment: \"+\",\n                  type: \"number\",\n                  value: formik.values.phoneCode,\n                  onChange: formik.handleChange\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 235,\n                  columnNumber: 37\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 234,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(Input, {\n                name: \"phoneNumber\",\n                value: formik.values.phoneNumber,\n                onChange: formik.handleChange,\n                error: formik.touched.phoneNumber && Boolean(formik.errors.phoneNumber),\n                helpertext: formik.touched.phoneNumber ? formik.errors.phoneNumber : \"\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 250,\n                columnNumber: 33\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 230,\n              columnNumber: 29\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 228,\n            columnNumber: 25\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 227,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          lg: 6,\n          xs: 12,\n          children: /*#__PURE__*/_jsxDEV(Input, {\n            label: \"City\",\n            name: \"city\",\n            value: formik.values.city,\n            onChange: formik.handleChange,\n            error: formik.touched.city && Boolean(formik.errors.city),\n            helpertext: formik.touched.city ? formik.errors.city : \"\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 261,\n            columnNumber: 25\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 260,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          lg: 6,\n          xs: 12,\n          children: /*#__PURE__*/_jsxDEV(Input, {\n            label: \"Address\",\n            name: \"address\",\n            value: formik.values.address,\n            onChange: formik.handleChange,\n            error: formik.touched.address && Boolean(formik.errors.address),\n            helpertext: formik.touched.address ? formik.errors.address : \"\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 271,\n            columnNumber: 25\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 270,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          lg: 6,\n          xs: 12,\n          children: /*#__PURE__*/_jsxDEV(SelectField, {\n            label: \"Department\",\n            name: \"department\",\n            value: formik.values.department,\n            onChange: formik.handleChange,\n            error: formik.touched.department && Boolean(formik.errors.department),\n            helpertext: formik.touched.department ? formik.errors.department : \"\",\n            children: departments.map((item, index) => /*#__PURE__*/_jsxDEV(MenuItem, {\n              value: item._id,\n              children: item.name\n            }, index, false, {\n              fileName: _jsxFileName,\n              lineNumber: 290,\n              columnNumber: 33\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 281,\n            columnNumber: 25\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 280,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          lg: 6,\n          xs: 12,\n          children: /*#__PURE__*/_jsxDEV(SelectField, {\n            label: \"Designation\",\n            name: \"designation\",\n            value: formik.values.designation,\n            onChange: formik.handleChange,\n            error: formik.touched.designation && Boolean(formik.errors.designation),\n            helpertext: formik.touched.designation ? formik.errors.designation : \"\",\n            children: designations.map((item, index) => /*#__PURE__*/_jsxDEV(MenuItem, {\n              value: item._id,\n              children: item.name\n            }, index, false, {\n              fileName: _jsxFileName,\n              lineNumber: 306,\n              columnNumber: 33\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 297,\n            columnNumber: 25\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 296,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          lg: 6,\n          xs: 12,\n          children: /*#__PURE__*/_jsxDEV(FormControl, {\n            fullWidth: true,\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"caption\",\n              children: \"Daily Work Hours\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 316,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                display: 'flex',\n                gap: 1.5,\n                alignItems: 'center'\n              },\n              children: [/*#__PURE__*/_jsxDEV(Input, {\n                label: \"\",\n                name: \"workHours\",\n                value: formik.values.workHours,\n                onChange: handleWorkHoursChange,\n                placeholder: hoursFormat === \"decimal\" ? \"e.g. 8.5\" : \"e.g. 8:30\",\n                error: formik.touched.workHours && Boolean(formik.errors.workHours),\n                helpertext: formik.touched.workHours ? formik.errors.workHours : \"\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 322,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(Button, {\n                size: \"small\",\n                variant: \"outlined\",\n                onClick: toggleHoursFormat,\n                children: hoursFormat === \"decimal\" ? \"Use HH:MM\" : \"Use Decimal\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 332,\n                columnNumber: 33\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 317,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"caption\",\n              sx: {\n                mt: 1,\n                color: 'text.secondary'\n              },\n              children: hoursFormat === \"decimal\" ? \"Enter as decimal (e.g., 8.5 for 8 hours 30 minutes)\" : \"Enter as hours:minutes (e.g., 8:30)\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 340,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"caption\",\n              sx: {\n                mt: 1,\n                display: 'block',\n                color: 'info.main'\n              },\n              children: \"Note: This value determines your daily work requirement. Working less than half of this time will be marked as a half-day. Working more will count as overtime.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 343,\n              columnNumber: 29\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 315,\n            columnNumber: 25\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 314,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          lg: 6,\n          xs: 12,\n          children: /*#__PURE__*/_jsxDEV(SelectField, {\n            label: \"Schedule Template\",\n            name: \"workSchedule.scheduleTemplate\",\n            value: getWorkScheduleValue('scheduleTemplate', 'day_shift'),\n            onChange: e => handleWorkScheduleChange('scheduleTemplate', e.target.value),\n            children: SCHEDULE_TEMPLATES.map(template => /*#__PURE__*/_jsxDEV(MenuItem, {\n              value: template.value,\n              children: template.label\n            }, template.value, false, {\n              fileName: _jsxFileName,\n              lineNumber: 358,\n              columnNumber: 33\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 351,\n            columnNumber: 25\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 350,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          lg: 6,\n          xs: 12,\n          children: /*#__PURE__*/_jsxDEV(Input, {\n            label: \"Shift Start\",\n            name: \"workSchedule.shiftStart\",\n            type: \"date\",\n            value: formatDateForInput((_formik$values$workSc = formik.values.workSchedule) === null || _formik$values$workSc === void 0 ? void 0 : _formik$values$workSc.shiftStart),\n            onChange: e => handleWorkScheduleChange('shiftStart', e.target.value)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 366,\n            columnNumber: 25\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 365,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          lg: 6,\n          xs: 12,\n          children: /*#__PURE__*/_jsxDEV(Input, {\n            label: \"Shift End\",\n            name: \"workSchedule.shiftEnd\",\n            type: \"date\",\n            value: formatDateForInput((_formik$values$workSc2 = formik.values.workSchedule) === null || _formik$values$workSc2 === void 0 ? void 0 : _formik$values$workSc2.shiftEnd),\n            onChange: e => handleWorkScheduleChange('shiftEnd', e.target.value)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 376,\n            columnNumber: 25\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 375,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          lg: 6,\n          xs: 12,\n          children: /*#__PURE__*/_jsxDEV(SelectField, {\n            label: \"Start Time\",\n            name: \"workSchedule.startTime\",\n            value: getWorkScheduleValue('startTime', '09:00'),\n            onChange: e => handleWorkScheduleChange('startTime', e.target.value),\n            children: TIME_OPTIONS.map(option => /*#__PURE__*/_jsxDEV(MenuItem, {\n              value: option.value,\n              children: option.label\n            }, option.value, false, {\n              fileName: _jsxFileName,\n              lineNumber: 393,\n              columnNumber: 33\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 386,\n            columnNumber: 25\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 385,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          lg: 6,\n          xs: 12,\n          children: /*#__PURE__*/_jsxDEV(SelectField, {\n            label: \"End Time\",\n            name: \"workSchedule.endTime\",\n            value: getWorkScheduleValue('endTime', '17:00'),\n            onChange: e => handleWorkScheduleChange('endTime', e.target.value),\n            children: TIME_OPTIONS.map(option => /*#__PURE__*/_jsxDEV(MenuItem, {\n              value: option.value,\n              children: option.label\n            }, option.value, false, {\n              fileName: _jsxFileName,\n              lineNumber: 408,\n              columnNumber: 33\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 401,\n            columnNumber: 25\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 400,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          lg: 6,\n          xs: 12,\n          children: /*#__PURE__*/_jsxDEV(Input, {\n            label: \"Minimum Hours\",\n            name: \"workSchedule.minimumHours\",\n            type: \"number\",\n            step: \"0.5\",\n            value: getWorkScheduleValue('minimumHours', 8.0),\n            onChange: e => handleWorkScheduleChange('minimumHours', parseFloat(e.target.value))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 416,\n            columnNumber: 25\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 415,\n          columnNumber: 21\n        }, this), Can(actions.readAll, features.user) && /*#__PURE__*/_jsxDEV(Grid, {\n          sx: {\n            mt: 3\n          },\n          item: true,\n          container: true,\n          justifyContent: \"flex-end\",\n          children: /*#__PURE__*/_jsxDEV(Button, {\n            type: \"submit\",\n            color: \"primary\",\n            variant: \"contained\",\n            children: \"Submit\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 428,\n            columnNumber: 29\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 427,\n          columnNumber: 25\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 192,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 191,\n      columnNumber: 13\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 189,\n    columnNumber: 9\n  }, this);\n}\n_s(BasicInformation, \"nVcy5cbiLXR/E+141lGOoqWoQyE=\", false, function () {\n  return [useDispatch, useTheme, useSelector, useSelector, useSelector, useFormik];\n});\n_c = BasicInformation;\nvar _c;\n$RefreshReg$(_c, \"BasicInformation\");", "map": {"version": 3, "names": ["React", "useEffect", "useState", "Box", "<PERSON><PERSON>", "Card", "FormControl", "Grid", "InputBase", "MenuItem", "Typography", "useTheme", "Autocomplete", "COUNTRIES", "useDispatch", "useSelector", "DepartmentSelector", "DesignationSelector", "GeneralSelector", "DepartmentActions", "DesignationActions", "GeneralActions", "UserActions", "useFormik", "Input", "SelectField", "toast", "PropTypes", "Can", "actions", "features", "SCHEDULE_TEMPLATES", "DEFAULT_WORK_SCHEDULE", "TIME_OPTIONS", "jsxDEV", "_jsxDEV", "BasicInformation", "propTypes", "user", "object", "form", "props", "_s", "_user$name", "_user$country", "_user$city", "_user$address", "_user$department$_id", "_user$department", "_user$designation$_id", "_user$designation", "_user$workHours", "_user$workSchedule", "_formik$values$workSc", "_formik$values$workSc2", "dispatch", "theme", "departments", "getDepartments", "designations", "getDesignations", "success", "updateUser", "type", "hoursFormat", "setHoursFormat", "countries", "map", "item", "id", "name", "phoneCode", "flag", "_success$message", "message", "position", "autoClose", "closeOnClick", "removeSuccess", "convertWorkHours", "value", "toFormat", "includes", "hours", "minutes", "split", "Number", "Math", "floor", "round", "toString", "padStart", "formik", "initialValues", "phoneNumber", "country", "city", "address", "department", "_id", "designation", "workHours", "workSchedule", "enableReinitialize", "validateOnChange", "onSubmit", "values", "handleSubmit", "_formik$values$countr", "code", "phone", "setFieldValue", "find", "e", "_code$length", "substring", "length", "workHoursDecimal", "workHoursNumber", "parseFloat", "params", "handleWorkHoursChange", "target", "toggleHoursFormat", "newFormat", "convertedValue", "handleWorkScheduleChange", "field", "formatDateForInput", "dateValue", "Date", "toISOString", "getWorkScheduleValue", "defaultValue", "undefined", "children", "variant", "sx", "mb", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "container", "spacing", "lg", "xs", "label", "onChange", "handleChange", "error", "touched", "Boolean", "errors", "helpertext", "fullWidth", "disable<PERSON><PERSON><PERSON>", "options", "val", "getOptionLabel", "option", "_option$name", "renderOption", "component", "mr", "flexShrink", "renderInput", "InputProps", "display", "gap", "width", "textAlign", "fillColor", "palette", "common", "black", "autoComplete", "startAdornment", "index", "alignItems", "placeholder", "size", "onClick", "mt", "color", "template", "shiftStart", "shiftEnd", "step", "readAll", "justifyContent", "_c", "$RefreshReg$"], "sources": ["E:/Suraj Patil/Organization_Management_System/meraki-frontend-main/src/screens/User/components/Form/BasicInformation.js"], "sourcesContent": ["import React, {useEffect, useState} from \"react\";\r\nimport {\r\n    Box,\r\n    Button,\r\n    Card,\r\n    FormControl,\r\n    Grid,\r\n    InputBase,\r\n    MenuItem,\r\n    Typography,\r\n    useTheme\r\n} from \"@mui/material\";\r\nimport {Autocomplete} from \"@mui/lab\";\r\nimport COUNTRIES from \"constants/countries\";\r\nimport {useDispatch, useSelector} from \"react-redux\";\r\nimport {DepartmentSelector, DesignationSelector, GeneralSelector} from \"selectors\";\r\nimport {DepartmentActions, DesignationActions, GeneralActions, UserActions} from \"slices/actions\";\r\nimport {useFormik} from \"formik\";\r\nimport Input from \"components/Input\";\r\nimport SelectField from \"components/SelectField\";\r\nimport {toast} from \"react-toastify\";\r\nimport PropTypes from \"prop-types\";\r\nimport Can from \"../../../../utils/can\";\r\nimport {actions, features} from \"../../../../constants/permission\";\r\nimport {SCHEDULE_TEMPLATES, DEFAULT_WORK_SCHEDULE, TIME_OPTIONS} from \"../../../../constants/workSchedule\";\r\n\r\nBasicInformation.propTypes = {\r\n    user: PropTypes.object,\r\n    form: PropTypes.object\r\n};\r\n\r\nexport default function BasicInformation(props) {\r\n    const { user, form } = props;\r\n    const dispatch = useDispatch();\r\n    const theme = useTheme();\r\n    const departments = useSelector(DepartmentSelector.getDepartments());\r\n    const designations = useSelector(DesignationSelector.getDesignations());\r\n    const success = useSelector(GeneralSelector.success(UserActions.updateUser.type));\r\n\r\n    const [hoursFormat, setHoursFormat] = useState(\"decimal\"); // \"decimal\" or \"hhmm\"\r\n\r\n    const countries = COUNTRIES.map(item => ({\r\n        id: item.id,\r\n        name: item.name,\r\n        phoneCode: item.phoneCode,\r\n        flag: item.flag\r\n    }));\r\n\r\n    useEffect(() => {\r\n        dispatch(DepartmentActions.getDepartments());\r\n        dispatch(DesignationActions.getDesignations());\r\n    }, []);\r\n\r\n    useEffect(() => {\r\n        if (success) {\r\n            toast.success(`${success?.message ?? \"Success\"}`, {\r\n                    position: \"top-right\",\r\n                    autoClose: 3000,\r\n                    closeOnClick: true,\r\n                });\r\n\r\n            dispatch(GeneralActions.removeSuccess(UserActions.updateUser.type));\r\n        }\r\n    }, [success]);\r\n\r\n    // Helper function to convert between decimal and HH:MM formats\r\n    const convertWorkHours = (value, toFormat) => {\r\n        if (!value) {\r\n            return \"\";\r\n        }\r\n\r\n        if (toFormat === \"decimal\") {\r\n            // Convert from HH:MM to decimal\r\n            if (value.includes(\":\")) {\r\n                const [hours, minutes] = value.split(\":\");\r\n                return Number(hours) + (Number(minutes) / 60);\r\n            }\r\n            return value;\r\n        } else {\r\n            // Convert from decimal to HH:MM\r\n            const hours = Math.floor(Number(value));\r\n            const minutes = Math.round((Number(value) - hours) * 60);\r\n            return `${hours}:${minutes.toString().padStart(2, '0')}`;\r\n        }\r\n    };\r\n\r\n    const formik = useFormik({\r\n        initialValues: {\r\n            name: user?.name ?? \"\",\r\n            phoneCode: '',\r\n            phoneNumber: \"\",\r\n            country: user?.country ?? \"\",\r\n            city: user?.city ?? \"\",\r\n            address: user?.address ?? \"\",\r\n            department: user?.department?._id ?? \"\",\r\n            designation: user?.designation?._id ?? \"\",\r\n            workHours: user?.workHours ?? \"8.5\", // Default to 8.5 hours or use stored value\r\n            workSchedule: user?.workSchedule ?? DEFAULT_WORK_SCHEDULE,\r\n        },\r\n        enableReinitialize: true,\r\n        validateOnChange: true,\r\n        onSubmit: (values) => {\r\n            handleSubmit(values);\r\n        }\r\n    });\r\n\r\n    useEffect(() => {\r\n        const code = formik.values.country?.phoneCode;\r\n        const phone = formik.values.phone;\r\n\r\n        formik.setFieldValue('phoneCode', code ?? '');\r\n        formik.setFieldValue('phone', phone);\r\n    }, [formik.values.country]);\r\n\r\n    useEffect(() => {\r\n        const phone = user.phone;\r\n        const country = countries.find(e => e.name === user.country);\r\n\r\n        if (country) {\r\n            formik.setFieldValue('country', country);\r\n        }\r\n\r\n        if (phone && country) {\r\n            const code = country.phoneCode;\r\n\r\n            formik.setFieldValue('phoneCode', code ?? '');\r\n            formik.setFieldValue('phoneNumber', phone.substring(code.length ?? 0));\r\n        }\r\n    }, [user]);\r\n\r\n    const handleSubmit = (values) => {\r\n        // Convert work hours to decimal format for storage\r\n        const workHoursDecimal = hoursFormat === \"decimal\" ? values.workHours : convertWorkHours(values.workHours, \"decimal\");\r\n\r\n        // Ensure workHours is a number, not a string\r\n        const workHoursNumber = parseFloat(workHoursDecimal);\r\n\r\n        const params = {\r\n            id: user._id,\r\n            ...values,\r\n            ...form,\r\n            phone: values.phoneCode + values.phoneNumber,\r\n            workHours: workHoursNumber,\r\n            workSchedule: values.workSchedule\r\n        };\r\n\r\n        dispatch(UserActions.updateUser(params));\r\n    }\r\n\r\n    const handleWorkHoursChange = (e) => {\r\n        const { value } = e.target;\r\n        formik.setFieldValue('workHours', value);\r\n    };\r\n\r\n    const toggleHoursFormat = () => {\r\n        const newFormat = hoursFormat === \"decimal\" ? \"hhmm\" : \"decimal\";\r\n        const convertedValue = convertWorkHours(formik.values.workHours, newFormat);\r\n        setHoursFormat(newFormat);\r\n        formik.setFieldValue('workHours', convertedValue);\r\n    };\r\n\r\n    const handleWorkScheduleChange = (field, value) => {\r\n        formik.setFieldValue(`workSchedule.${field}`, value);\r\n    };\r\n\r\n    // Helper function to format date for input field\r\n    const formatDateForInput = (dateValue) => {\r\n        if (!dateValue) {\r\n            return new Date().toISOString().split('T')[0]; // Current date\r\n        }\r\n\r\n        if (typeof dateValue === 'string') {\r\n            return dateValue.split('T')[0];\r\n        }\r\n\r\n        return new Date(dateValue).toISOString().split('T')[0];\r\n    };\r\n\r\n    // Helper functions to get work schedule values with defaults\r\n    const getWorkScheduleValue = (field, defaultValue) => {\r\n        const workSchedule = formik.values.workSchedule;\r\n        if (!workSchedule || workSchedule[field] === undefined) {\r\n            return defaultValue;\r\n        }\r\n        return workSchedule[field];\r\n    };\r\n\r\n    return (\r\n        <Card>\r\n            <Typography variant='h5' sx={{ mb: 4 }}>Basic Information</Typography>\r\n            <form onSubmit={formik.handleSubmit}>\r\n                <Grid container spacing={2}>\r\n                    <Grid item lg={6} xs={12}>\r\n                        <Input\r\n\r\n                            label=\"Full Name\"\r\n                            name='name'\r\n                            value={formik.values.name}\r\n                            onChange={formik.handleChange}\r\n                            error={formik.touched.name && Boolean(formik.errors.name)}\r\n                           helpertext={formik.touched.name ? formik.errors.name : \"\"}/>\r\n                    </Grid>\r\n                    <Grid item lg={6} xs={12}>\r\n                        <FormControl fullWidth>\r\n                            <Typography variant='caption'>Country</Typography>\r\n                            <Autocomplete\r\n                                disablePortal\r\n\r\n                                name='country'\r\n                                options={countries}\r\n                                value={formik.values.country}\r\n                                onChange={(e, val) => {\r\n                                    formik.setFieldValue('country', val);\r\n                                }}\r\n                                error={formik.touched.country && Boolean(formik.errors.country)}\r\n                                helpertext={formik.touched.country ? formik.errors.country : \"\"}\r\n                                getOptionLabel={(option) => option.name ?? ''}\r\n                                renderOption={(props, option) => (\r\n                                    <Box component=\"li\" sx={{ '& > img': { mr: 2, flexShrink: 0 } }} {...props}>\r\n                                        {option.flag} {option.name}\r\n                                    </Box>\r\n                                )}\r\n                                renderInput={(params) => <InputBase {...params.InputProps} {...params} />}\r\n                            />\r\n                        </FormControl>\r\n                    </Grid>\r\n                    <Grid item lg={6} xs={12}>\r\n                        <FormControl fullWidth>\r\n                            <Typography variant='caption'>Phone Number</Typography>\r\n                            <Box sx={{\r\n                                display: 'flex',\r\n                                gap: 1.5\r\n                            }}>\r\n                                <Box sx={{ width: 80 }}>\r\n                                    <Input\r\n                                        sx={{\r\n                                            textAlign: 'center',\r\n                                            '& .Mui-disabled': {\r\n                                                fillColor: theme.palette.common.black\r\n                                            }\r\n                                        }}\r\n\r\n                                        autoComplete='tel-country-code'\r\n                                        name='phoneCode'\r\n                                        startAdornment='+'\r\n                                        type='number'\r\n                                        value={formik.values.phoneCode}\r\n                                        onChange={formik.handleChange}/>\r\n                                </Box>\r\n                                <Input\r\n\r\n                                    name='phoneNumber'\r\n                                    value={formik.values.phoneNumber}\r\n                                    onChange={formik.handleChange}\r\n                                    error={formik.touched.phoneNumber && Boolean(formik.errors.phoneNumber)}\r\n                                    helpertext={formik.touched.phoneNumber ? formik.errors.phoneNumber : \"\"}/>\r\n                            </Box>\r\n                        </FormControl>\r\n                    </Grid>\r\n                    <Grid item lg={6} xs={12}>\r\n                        <Input\r\n                            label=\"City\"\r\n                            name='city'\r\n                            value={formik.values.city}\r\n                            onChange={formik.handleChange}\r\n                            error={formik.touched.city && Boolean(formik.errors.city)}\r\n                            helpertext={formik.touched.city ? formik.errors.city : \"\"}\r\n                            />\r\n                    </Grid>\r\n                    <Grid item lg={6} xs={12}>\r\n                        <Input\r\n                            label=\"Address\"\r\n                            name='address'\r\n                            value={formik.values.address}\r\n                            onChange={formik.handleChange}\r\n                            error={formik.touched.address && Boolean(formik.errors.address)}\r\n                            helpertext={formik.touched.address ? formik.errors.address : \"\"}\r\n                            />\r\n                    </Grid>\r\n                    <Grid item lg={6} xs={12}>\r\n                        <SelectField\r\n                            label=\"Department\"\r\n                            name='department'\r\n                            value={formik.values.department}\r\n                            onChange={formik.handleChange}\r\n                            error={formik.touched.department && Boolean(formik.errors.department)}\r\n                            helpertext={formik.touched.department ? formik.errors.department : \"\"}\r\n                            >\r\n                            {departments.map((item, index) => (\r\n                                <MenuItem key={index} value={item._id}>\r\n                                    {item.name}\r\n                                </MenuItem>\r\n                            ))}\r\n                        </SelectField>\r\n                    </Grid>\r\n                    <Grid item lg={6} xs={12}>\r\n                        <SelectField\r\n                            label=\"Designation\"\r\n                            name='designation'\r\n                            value={formik.values.designation}\r\n                            onChange={formik.handleChange}\r\n                            error={formik.touched.designation && Boolean(formik.errors.designation)}\r\n                           helpertext={formik.touched.designation ? formik.errors.designation : \"\"}\r\n                            >\r\n                            {designations.map((item, index) => (\r\n                                <MenuItem key={index} value={item._id}>\r\n                                    {item.name}\r\n                                </MenuItem>\r\n                            ))}\r\n                        </SelectField>\r\n                    </Grid>\r\n\r\n                    {/* New Work Hours Field */}\r\n                    <Grid item lg={6} xs={12}>\r\n                        <FormControl fullWidth>\r\n                            <Typography variant='caption'>Daily Work Hours</Typography>\r\n                            <Box sx={{\r\n                                display: 'flex',\r\n                                gap: 1.5,\r\n                                alignItems: 'center'\r\n                            }}>\r\n                                <Input\r\n\r\n                                    label=\"\"\r\n                                    name='workHours'\r\n                                    value={formik.values.workHours}\r\n                                    onChange={handleWorkHoursChange}\r\n                                    placeholder={hoursFormat === \"decimal\" ? \"e.g. 8.5\" : \"e.g. 8:30\"}\r\n                                    error={formik.touched.workHours && Boolean(formik.errors.workHours)}\r\n                                    helpertext={formik.touched.workHours ? formik.errors.workHours : \"\"}/>\r\n\r\n                                <Button\r\n                                    size=\"small\"\r\n                                    variant=\"outlined\"\r\n                                    onClick={toggleHoursFormat}\r\n                                    >\r\n                                    {hoursFormat === \"decimal\" ? \"Use HH:MM\" : \"Use Decimal\"}\r\n                                </Button>\r\n                            </Box>\r\n                            <Typography variant='caption' sx={{ mt: 1, color: 'text.secondary' }}>\r\n                                {hoursFormat === \"decimal\" ? \"Enter as decimal (e.g., 8.5 for 8 hours 30 minutes)\" : \"Enter as hours:minutes (e.g., 8:30)\"}\r\n                            </Typography>\r\n                            <Typography variant='caption' sx={{ mt: 1, display: 'block', color: 'info.main' }}>\r\n                                Note: This value determines your daily work requirement. Working less than half of this time will be marked as a half-day. Working more will count as overtime.\r\n                            </Typography>\r\n                        </FormControl>\r\n                    </Grid>\r\n\r\n                    {/* Work Schedule Fields */}\r\n                    <Grid item lg={6} xs={12}>\r\n                        <SelectField\r\n                            label=\"Schedule Template\"\r\n                            name='workSchedule.scheduleTemplate'\r\n                            value={getWorkScheduleValue('scheduleTemplate', 'day_shift')}\r\n                            onChange={(e) => handleWorkScheduleChange('scheduleTemplate', e.target.value)}\r\n                            >\r\n                            {SCHEDULE_TEMPLATES.map((template) => (\r\n                                <MenuItem key={template.value} value={template.value}>\r\n                                    {template.label}\r\n                                </MenuItem>\r\n                            ))}\r\n                        </SelectField>\r\n                    </Grid>\r\n\r\n                    <Grid item lg={6} xs={12}>\r\n                        <Input\r\n                            label=\"Shift Start\"\r\n                            name='workSchedule.shiftStart'\r\n                            type=\"date\"\r\n                            value={formatDateForInput(formik.values.workSchedule?.shiftStart)}\r\n                            onChange={(e) => handleWorkScheduleChange('shiftStart', e.target.value)}\r\n                            />\r\n                    </Grid>\r\n\r\n                    <Grid item lg={6} xs={12}>\r\n                        <Input\r\n                            label=\"Shift End\"\r\n                            name='workSchedule.shiftEnd'\r\n                            type=\"date\"\r\n                            value={formatDateForInput(formik.values.workSchedule?.shiftEnd)}\r\n                            onChange={(e) => handleWorkScheduleChange('shiftEnd', e.target.value)}\r\n                            />\r\n                    </Grid>\r\n\r\n                    <Grid item lg={6} xs={12}>\r\n                        <SelectField\r\n                            label=\"Start Time\"\r\n                            name='workSchedule.startTime'\r\n                            value={getWorkScheduleValue('startTime', '09:00')}\r\n                            onChange={(e) => handleWorkScheduleChange('startTime', e.target.value)}\r\n                            >\r\n                            {TIME_OPTIONS.map((option) => (\r\n                                <MenuItem key={option.value} value={option.value}>\r\n                                    {option.label}\r\n                                </MenuItem>\r\n                            ))}\r\n                        </SelectField>\r\n                    </Grid>\r\n\r\n                    <Grid item lg={6} xs={12}>\r\n                        <SelectField\r\n                            label=\"End Time\"\r\n                            name='workSchedule.endTime'\r\n                            value={getWorkScheduleValue('endTime', '17:00')}\r\n                            onChange={(e) => handleWorkScheduleChange('endTime', e.target.value)}\r\n                            >\r\n                            {TIME_OPTIONS.map((option) => (\r\n                                <MenuItem key={option.value} value={option.value}>\r\n                                    {option.label}\r\n                                </MenuItem>\r\n                            ))}\r\n                        </SelectField>\r\n                    </Grid>\r\n\r\n                    <Grid item lg={6} xs={12}>\r\n                        <Input\r\n                            label=\"Minimum Hours\"\r\n                            name='workSchedule.minimumHours'\r\n                            type=\"number\"\r\n                            step=\"0.5\"\r\n                            value={getWorkScheduleValue('minimumHours', 8.0)}\r\n                            onChange={(e) => handleWorkScheduleChange('minimumHours', parseFloat(e.target.value))}\r\n                            />\r\n                    </Grid>\r\n\r\n                    {Can(actions.readAll, features.user) && (\r\n                        <Grid sx={{ mt: 3 }} item container justifyContent=\"flex-end\">\r\n                            <Button\r\n                                type=\"submit\"\r\n                                color=\"primary\"\r\n                                variant=\"contained\">\r\n                                Submit\r\n                            </Button>\r\n                        </Grid>\r\n                    )}\r\n                </Grid>\r\n            </form>\r\n        </Card>\r\n    )\r\n}"], "mappings": ";;AAAA,OAAOA,KAAK,IAAGC,SAAS,EAAEC,QAAQ,QAAO,OAAO;AAChD,SACIC,GAAG,EACHC,MAAM,EACNC,IAAI,EACJC,WAAW,EACXC,IAAI,EACJC,SAAS,EACTC,QAAQ,EACRC,UAAU,EACVC,QAAQ,QACL,eAAe;AACtB,SAAQC,YAAY,QAAO,UAAU;AACrC,OAAOC,SAAS,MAAM,qBAAqB;AAC3C,SAAQC,WAAW,EAAEC,WAAW,QAAO,aAAa;AACpD,SAAQC,kBAAkB,EAAEC,mBAAmB,EAAEC,eAAe,QAAO,WAAW;AAClF,SAAQC,iBAAiB,EAAEC,kBAAkB,EAAEC,cAAc,EAAEC,WAAW,QAAO,gBAAgB;AACjG,SAAQC,SAAS,QAAO,QAAQ;AAChC,OAAOC,KAAK,MAAM,kBAAkB;AACpC,OAAOC,WAAW,MAAM,wBAAwB;AAChD,SAAQC,KAAK,QAAO,gBAAgB;AACpC,OAAOC,SAAS,MAAM,YAAY;AAClC,OAAOC,GAAG,MAAM,uBAAuB;AACvC,SAAQC,OAAO,EAAEC,QAAQ,QAAO,kCAAkC;AAClE,SAAQC,kBAAkB,EAAEC,qBAAqB,EAAEC,YAAY,QAAO,oCAAoC;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE3GC,gBAAgB,CAACC,SAAS,GAAG;EACzBC,IAAI,EAAEX,SAAS,CAACY,MAAM;EACtBC,IAAI,EAAEb,SAAS,CAACY;AACpB,CAAC;AAED,eAAe,SAASH,gBAAgBA,CAACK,KAAK,EAAE;EAAAC,EAAA;EAAA,IAAAC,UAAA,EAAAC,aAAA,EAAAC,UAAA,EAAAC,aAAA,EAAAC,oBAAA,EAAAC,gBAAA,EAAAC,qBAAA,EAAAC,iBAAA,EAAAC,eAAA,EAAAC,kBAAA,EAAAC,qBAAA,EAAAC,sBAAA;EAC5C,MAAM;IAAEhB,IAAI;IAAEE;EAAK,CAAC,GAAGC,KAAK;EAC5B,MAAMc,QAAQ,GAAGzC,WAAW,CAAC,CAAC;EAC9B,MAAM0C,KAAK,GAAG7C,QAAQ,CAAC,CAAC;EACxB,MAAM8C,WAAW,GAAG1C,WAAW,CAACC,kBAAkB,CAAC0C,cAAc,CAAC,CAAC,CAAC;EACpE,MAAMC,YAAY,GAAG5C,WAAW,CAACE,mBAAmB,CAAC2C,eAAe,CAAC,CAAC,CAAC;EACvE,MAAMC,OAAO,GAAG9C,WAAW,CAACG,eAAe,CAAC2C,OAAO,CAACvC,WAAW,CAACwC,UAAU,CAACC,IAAI,CAAC,CAAC;EAEjF,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAG/D,QAAQ,CAAC,SAAS,CAAC,CAAC,CAAC;;EAE3D,MAAMgE,SAAS,GAAGrD,SAAS,CAACsD,GAAG,CAACC,IAAI,KAAK;IACrCC,EAAE,EAAED,IAAI,CAACC,EAAE;IACXC,IAAI,EAAEF,IAAI,CAACE,IAAI;IACfC,SAAS,EAAEH,IAAI,CAACG,SAAS;IACzBC,IAAI,EAAEJ,IAAI,CAACI;EACf,CAAC,CAAC,CAAC;EAEHvE,SAAS,CAAC,MAAM;IACZsD,QAAQ,CAACpC,iBAAiB,CAACuC,cAAc,CAAC,CAAC,CAAC;IAC5CH,QAAQ,CAACnC,kBAAkB,CAACwC,eAAe,CAAC,CAAC,CAAC;EAClD,CAAC,EAAE,EAAE,CAAC;EAEN3D,SAAS,CAAC,MAAM;IACZ,IAAI4D,OAAO,EAAE;MAAA,IAAAY,gBAAA;MACT/C,KAAK,CAACmC,OAAO,CAAC,IAAAY,gBAAA,GAAGZ,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEa,OAAO,cAAAD,gBAAA,cAAAA,gBAAA,GAAI,SAAS,EAAE,EAAE;QAC1CE,QAAQ,EAAE,WAAW;QACrBC,SAAS,EAAE,IAAI;QACfC,YAAY,EAAE;MAClB,CAAC,CAAC;MAENtB,QAAQ,CAAClC,cAAc,CAACyD,aAAa,CAACxD,WAAW,CAACwC,UAAU,CAACC,IAAI,CAAC,CAAC;IACvE;EACJ,CAAC,EAAE,CAACF,OAAO,CAAC,CAAC;;EAEb;EACA,MAAMkB,gBAAgB,GAAGA,CAACC,KAAK,EAAEC,QAAQ,KAAK;IAC1C,IAAI,CAACD,KAAK,EAAE;MACR,OAAO,EAAE;IACb;IAEA,IAAIC,QAAQ,KAAK,SAAS,EAAE;MACxB;MACA,IAAID,KAAK,CAACE,QAAQ,CAAC,GAAG,CAAC,EAAE;QACrB,MAAM,CAACC,KAAK,EAAEC,OAAO,CAAC,GAAGJ,KAAK,CAACK,KAAK,CAAC,GAAG,CAAC;QACzC,OAAOC,MAAM,CAACH,KAAK,CAAC,GAAIG,MAAM,CAACF,OAAO,CAAC,GAAG,EAAG;MACjD;MACA,OAAOJ,KAAK;IAChB,CAAC,MAAM;MACH;MACA,MAAMG,KAAK,GAAGI,IAAI,CAACC,KAAK,CAACF,MAAM,CAACN,KAAK,CAAC,CAAC;MACvC,MAAMI,OAAO,GAAGG,IAAI,CAACE,KAAK,CAAC,CAACH,MAAM,CAACN,KAAK,CAAC,GAAGG,KAAK,IAAI,EAAE,CAAC;MACxD,OAAO,GAAGA,KAAK,IAAIC,OAAO,CAACM,QAAQ,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,EAAE;IAC5D;EACJ,CAAC;EAED,MAAMC,MAAM,GAAGrE,SAAS,CAAC;IACrBsE,aAAa,EAAE;MACXvB,IAAI,GAAA3B,UAAA,GAAEL,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEgC,IAAI,cAAA3B,UAAA,cAAAA,UAAA,GAAI,EAAE;MACtB4B,SAAS,EAAE,EAAE;MACbuB,WAAW,EAAE,EAAE;MACfC,OAAO,GAAAnD,aAAA,GAAEN,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEyD,OAAO,cAAAnD,aAAA,cAAAA,aAAA,GAAI,EAAE;MAC5BoD,IAAI,GAAAnD,UAAA,GAAEP,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE0D,IAAI,cAAAnD,UAAA,cAAAA,UAAA,GAAI,EAAE;MACtBoD,OAAO,GAAAnD,aAAA,GAAER,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE2D,OAAO,cAAAnD,aAAA,cAAAA,aAAA,GAAI,EAAE;MAC5BoD,UAAU,GAAAnD,oBAAA,GAAET,IAAI,aAAJA,IAAI,wBAAAU,gBAAA,GAAJV,IAAI,CAAE4D,UAAU,cAAAlD,gBAAA,uBAAhBA,gBAAA,CAAkBmD,GAAG,cAAApD,oBAAA,cAAAA,oBAAA,GAAI,EAAE;MACvCqD,WAAW,GAAAnD,qBAAA,GAAEX,IAAI,aAAJA,IAAI,wBAAAY,iBAAA,GAAJZ,IAAI,CAAE8D,WAAW,cAAAlD,iBAAA,uBAAjBA,iBAAA,CAAmBiD,GAAG,cAAAlD,qBAAA,cAAAA,qBAAA,GAAI,EAAE;MACzCoD,SAAS,GAAAlD,eAAA,GAAEb,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE+D,SAAS,cAAAlD,eAAA,cAAAA,eAAA,GAAI,KAAK;MAAE;MACrCmD,YAAY,GAAAlD,kBAAA,GAAEd,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEgE,YAAY,cAAAlD,kBAAA,cAAAA,kBAAA,GAAIpB;IACxC,CAAC;IACDuE,kBAAkB,EAAE,IAAI;IACxBC,gBAAgB,EAAE,IAAI;IACtBC,QAAQ,EAAGC,MAAM,IAAK;MAClBC,YAAY,CAACD,MAAM,CAAC;IACxB;EACJ,CAAC,CAAC;EAEFzG,SAAS,CAAC,MAAM;IAAA,IAAA2G,qBAAA;IACZ,MAAMC,IAAI,IAAAD,qBAAA,GAAGhB,MAAM,CAACc,MAAM,CAACX,OAAO,cAAAa,qBAAA,uBAArBA,qBAAA,CAAuBrC,SAAS;IAC7C,MAAMuC,KAAK,GAAGlB,MAAM,CAACc,MAAM,CAACI,KAAK;IAEjClB,MAAM,CAACmB,aAAa,CAAC,WAAW,EAAEF,IAAI,aAAJA,IAAI,cAAJA,IAAI,GAAI,EAAE,CAAC;IAC7CjB,MAAM,CAACmB,aAAa,CAAC,OAAO,EAAED,KAAK,CAAC;EACxC,CAAC,EAAE,CAAClB,MAAM,CAACc,MAAM,CAACX,OAAO,CAAC,CAAC;EAE3B9F,SAAS,CAAC,MAAM;IACZ,MAAM6G,KAAK,GAAGxE,IAAI,CAACwE,KAAK;IACxB,MAAMf,OAAO,GAAG7B,SAAS,CAAC8C,IAAI,CAACC,CAAC,IAAIA,CAAC,CAAC3C,IAAI,KAAKhC,IAAI,CAACyD,OAAO,CAAC;IAE5D,IAAIA,OAAO,EAAE;MACTH,MAAM,CAACmB,aAAa,CAAC,SAAS,EAAEhB,OAAO,CAAC;IAC5C;IAEA,IAAIe,KAAK,IAAIf,OAAO,EAAE;MAAA,IAAAmB,YAAA;MAClB,MAAML,IAAI,GAAGd,OAAO,CAACxB,SAAS;MAE9BqB,MAAM,CAACmB,aAAa,CAAC,WAAW,EAAEF,IAAI,aAAJA,IAAI,cAAJA,IAAI,GAAI,EAAE,CAAC;MAC7CjB,MAAM,CAACmB,aAAa,CAAC,aAAa,EAAED,KAAK,CAACK,SAAS,EAAAD,YAAA,GAACL,IAAI,CAACO,MAAM,cAAAF,YAAA,cAAAA,YAAA,GAAI,CAAC,CAAC,CAAC;IAC1E;EACJ,CAAC,EAAE,CAAC5E,IAAI,CAAC,CAAC;EAEV,MAAMqE,YAAY,GAAID,MAAM,IAAK;IAC7B;IACA,MAAMW,gBAAgB,GAAGrD,WAAW,KAAK,SAAS,GAAG0C,MAAM,CAACL,SAAS,GAAGtB,gBAAgB,CAAC2B,MAAM,CAACL,SAAS,EAAE,SAAS,CAAC;;IAErH;IACA,MAAMiB,eAAe,GAAGC,UAAU,CAACF,gBAAgB,CAAC;IAEpD,MAAMG,MAAM,GAAG;MACXnD,EAAE,EAAE/B,IAAI,CAAC6D,GAAG;MACZ,GAAGO,MAAM;MACT,GAAGlE,IAAI;MACPsE,KAAK,EAAEJ,MAAM,CAACnC,SAAS,GAAGmC,MAAM,CAACZ,WAAW;MAC5CO,SAAS,EAAEiB,eAAe;MAC1BhB,YAAY,EAAEI,MAAM,CAACJ;IACzB,CAAC;IAED/C,QAAQ,CAACjC,WAAW,CAACwC,UAAU,CAAC0D,MAAM,CAAC,CAAC;EAC5C,CAAC;EAED,MAAMC,qBAAqB,GAAIR,CAAC,IAAK;IACjC,MAAM;MAAEjC;IAAM,CAAC,GAAGiC,CAAC,CAACS,MAAM;IAC1B9B,MAAM,CAACmB,aAAa,CAAC,WAAW,EAAE/B,KAAK,CAAC;EAC5C,CAAC;EAED,MAAM2C,iBAAiB,GAAGA,CAAA,KAAM;IAC5B,MAAMC,SAAS,GAAG5D,WAAW,KAAK,SAAS,GAAG,MAAM,GAAG,SAAS;IAChE,MAAM6D,cAAc,GAAG9C,gBAAgB,CAACa,MAAM,CAACc,MAAM,CAACL,SAAS,EAAEuB,SAAS,CAAC;IAC3E3D,cAAc,CAAC2D,SAAS,CAAC;IACzBhC,MAAM,CAACmB,aAAa,CAAC,WAAW,EAAEc,cAAc,CAAC;EACrD,CAAC;EAED,MAAMC,wBAAwB,GAAGA,CAACC,KAAK,EAAE/C,KAAK,KAAK;IAC/CY,MAAM,CAACmB,aAAa,CAAC,gBAAgBgB,KAAK,EAAE,EAAE/C,KAAK,CAAC;EACxD,CAAC;;EAED;EACA,MAAMgD,kBAAkB,GAAIC,SAAS,IAAK;IACtC,IAAI,CAACA,SAAS,EAAE;MACZ,OAAO,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,CAAC9C,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACnD;IAEA,IAAI,OAAO4C,SAAS,KAAK,QAAQ,EAAE;MAC/B,OAAOA,SAAS,CAAC5C,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;IAClC;IAEA,OAAO,IAAI6C,IAAI,CAACD,SAAS,CAAC,CAACE,WAAW,CAAC,CAAC,CAAC9C,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;EAC1D,CAAC;;EAED;EACA,MAAM+C,oBAAoB,GAAGA,CAACL,KAAK,EAAEM,YAAY,KAAK;IAClD,MAAM/B,YAAY,GAAGV,MAAM,CAACc,MAAM,CAACJ,YAAY;IAC/C,IAAI,CAACA,YAAY,IAAIA,YAAY,CAACyB,KAAK,CAAC,KAAKO,SAAS,EAAE;MACpD,OAAOD,YAAY;IACvB;IACA,OAAO/B,YAAY,CAACyB,KAAK,CAAC;EAC9B,CAAC;EAED,oBACI5F,OAAA,CAAC9B,IAAI;IAAAkI,QAAA,gBACDpG,OAAA,CAACzB,UAAU;MAAC8H,OAAO,EAAC,IAAI;MAACC,EAAE,EAAE;QAAEC,EAAE,EAAE;MAAE,CAAE;MAAAH,QAAA,EAAC;IAAiB;MAAAI,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAY,CAAC,eACtE3G,OAAA;MAAMsE,QAAQ,EAAEb,MAAM,CAACe,YAAa;MAAA4B,QAAA,eAChCpG,OAAA,CAAC5B,IAAI;QAACwI,SAAS;QAACC,OAAO,EAAE,CAAE;QAAAT,QAAA,gBACvBpG,OAAA,CAAC5B,IAAI;UAAC6D,IAAI;UAAC6E,EAAE,EAAE,CAAE;UAACC,EAAE,EAAE,EAAG;UAAAX,QAAA,eACrBpG,OAAA,CAACX,KAAK;YAEF2H,KAAK,EAAC,WAAW;YACjB7E,IAAI,EAAC,MAAM;YACXU,KAAK,EAAEY,MAAM,CAACc,MAAM,CAACpC,IAAK;YAC1B8E,QAAQ,EAAExD,MAAM,CAACyD,YAAa;YAC9BC,KAAK,EAAE1D,MAAM,CAAC2D,OAAO,CAACjF,IAAI,IAAIkF,OAAO,CAAC5D,MAAM,CAAC6D,MAAM,CAACnF,IAAI,CAAE;YAC3DoF,UAAU,EAAE9D,MAAM,CAAC2D,OAAO,CAACjF,IAAI,GAAGsB,MAAM,CAAC6D,MAAM,CAACnF,IAAI,GAAG;UAAG;YAAAqE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7D,CAAC,eACP3G,OAAA,CAAC5B,IAAI;UAAC6D,IAAI;UAAC6E,EAAE,EAAE,CAAE;UAACC,EAAE,EAAE,EAAG;UAAAX,QAAA,eACrBpG,OAAA,CAAC7B,WAAW;YAACqJ,SAAS;YAAApB,QAAA,gBAClBpG,OAAA,CAACzB,UAAU;cAAC8H,OAAO,EAAC,SAAS;cAAAD,QAAA,EAAC;YAAO;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eAClD3G,OAAA,CAACvB,YAAY;cACTgJ,aAAa;cAEbtF,IAAI,EAAC,SAAS;cACduF,OAAO,EAAE3F,SAAU;cACnBc,KAAK,EAAEY,MAAM,CAACc,MAAM,CAACX,OAAQ;cAC7BqD,QAAQ,EAAEA,CAACnC,CAAC,EAAE6C,GAAG,KAAK;gBAClBlE,MAAM,CAACmB,aAAa,CAAC,SAAS,EAAE+C,GAAG,CAAC;cACxC,CAAE;cACFR,KAAK,EAAE1D,MAAM,CAAC2D,OAAO,CAACxD,OAAO,IAAIyD,OAAO,CAAC5D,MAAM,CAAC6D,MAAM,CAAC1D,OAAO,CAAE;cAChE2D,UAAU,EAAE9D,MAAM,CAAC2D,OAAO,CAACxD,OAAO,GAAGH,MAAM,CAAC6D,MAAM,CAAC1D,OAAO,GAAG,EAAG;cAChEgE,cAAc,EAAGC,MAAM;gBAAA,IAAAC,YAAA;gBAAA,QAAAA,YAAA,GAAKD,MAAM,CAAC1F,IAAI,cAAA2F,YAAA,cAAAA,YAAA,GAAI,EAAE;cAAA,CAAC;cAC9CC,YAAY,EAAEA,CAACzH,KAAK,EAAEuH,MAAM,kBACxB7H,OAAA,CAAChC,GAAG;gBAACgK,SAAS,EAAC,IAAI;gBAAC1B,EAAE,EAAE;kBAAE,SAAS,EAAE;oBAAE2B,EAAE,EAAE,CAAC;oBAAEC,UAAU,EAAE;kBAAE;gBAAE,CAAE;gBAAA,GAAK5H,KAAK;gBAAA8F,QAAA,GACrEyB,MAAM,CAACxF,IAAI,EAAC,GAAC,EAACwF,MAAM,CAAC1F,IAAI;cAAA;gBAAAqE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACzB,CACP;cACFwB,WAAW,EAAG9C,MAAM,iBAAKrF,OAAA,CAAC3B,SAAS;gBAAA,GAAKgH,MAAM,CAAC+C,UAAU;gBAAA,GAAM/C;cAAM;gBAAAmB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG;YAAE;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC7E,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACO;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACZ,CAAC,eACP3G,OAAA,CAAC5B,IAAI;UAAC6D,IAAI;UAAC6E,EAAE,EAAE,CAAE;UAACC,EAAE,EAAE,EAAG;UAAAX,QAAA,eACrBpG,OAAA,CAAC7B,WAAW;YAACqJ,SAAS;YAAApB,QAAA,gBAClBpG,OAAA,CAACzB,UAAU;cAAC8H,OAAO,EAAC,SAAS;cAAAD,QAAA,EAAC;YAAY;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACvD3G,OAAA,CAAChC,GAAG;cAACsI,EAAE,EAAE;gBACL+B,OAAO,EAAE,MAAM;gBACfC,GAAG,EAAE;cACT,CAAE;cAAAlC,QAAA,gBACEpG,OAAA,CAAChC,GAAG;gBAACsI,EAAE,EAAE;kBAAEiC,KAAK,EAAE;gBAAG,CAAE;gBAAAnC,QAAA,eACnBpG,OAAA,CAACX,KAAK;kBACFiH,EAAE,EAAE;oBACAkC,SAAS,EAAE,QAAQ;oBACnB,iBAAiB,EAAE;sBACfC,SAAS,EAAEpH,KAAK,CAACqH,OAAO,CAACC,MAAM,CAACC;oBACpC;kBACJ,CAAE;kBAEFC,YAAY,EAAC,kBAAkB;kBAC/B1G,IAAI,EAAC,WAAW;kBAChB2G,cAAc,EAAC,GAAG;kBAClBlH,IAAI,EAAC,QAAQ;kBACbiB,KAAK,EAAEY,MAAM,CAACc,MAAM,CAACnC,SAAU;kBAC/B6E,QAAQ,EAAExD,MAAM,CAACyD;gBAAa;kBAAAV,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAC;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnC,CAAC,eACN3G,OAAA,CAACX,KAAK;gBAEF8C,IAAI,EAAC,aAAa;gBAClBU,KAAK,EAAEY,MAAM,CAACc,MAAM,CAACZ,WAAY;gBACjCsD,QAAQ,EAAExD,MAAM,CAACyD,YAAa;gBAC9BC,KAAK,EAAE1D,MAAM,CAAC2D,OAAO,CAACzD,WAAW,IAAI0D,OAAO,CAAC5D,MAAM,CAAC6D,MAAM,CAAC3D,WAAW,CAAE;gBACxE4D,UAAU,EAAE9D,MAAM,CAAC2D,OAAO,CAACzD,WAAW,GAAGF,MAAM,CAAC6D,MAAM,CAAC3D,WAAW,GAAG;cAAG;gBAAA6C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC7E,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACG;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACZ,CAAC,eACP3G,OAAA,CAAC5B,IAAI;UAAC6D,IAAI;UAAC6E,EAAE,EAAE,CAAE;UAACC,EAAE,EAAE,EAAG;UAAAX,QAAA,eACrBpG,OAAA,CAACX,KAAK;YACF2H,KAAK,EAAC,MAAM;YACZ7E,IAAI,EAAC,MAAM;YACXU,KAAK,EAAEY,MAAM,CAACc,MAAM,CAACV,IAAK;YAC1BoD,QAAQ,EAAExD,MAAM,CAACyD,YAAa;YAC9BC,KAAK,EAAE1D,MAAM,CAAC2D,OAAO,CAACvD,IAAI,IAAIwD,OAAO,CAAC5D,MAAM,CAAC6D,MAAM,CAACzD,IAAI,CAAE;YAC1D0D,UAAU,EAAE9D,MAAM,CAAC2D,OAAO,CAACvD,IAAI,GAAGJ,MAAM,CAAC6D,MAAM,CAACzD,IAAI,GAAG;UAAG;YAAA2C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzD;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC,eACP3G,OAAA,CAAC5B,IAAI;UAAC6D,IAAI;UAAC6E,EAAE,EAAE,CAAE;UAACC,EAAE,EAAE,EAAG;UAAAX,QAAA,eACrBpG,OAAA,CAACX,KAAK;YACF2H,KAAK,EAAC,SAAS;YACf7E,IAAI,EAAC,SAAS;YACdU,KAAK,EAAEY,MAAM,CAACc,MAAM,CAACT,OAAQ;YAC7BmD,QAAQ,EAAExD,MAAM,CAACyD,YAAa;YAC9BC,KAAK,EAAE1D,MAAM,CAAC2D,OAAO,CAACtD,OAAO,IAAIuD,OAAO,CAAC5D,MAAM,CAAC6D,MAAM,CAACxD,OAAO,CAAE;YAChEyD,UAAU,EAAE9D,MAAM,CAAC2D,OAAO,CAACtD,OAAO,GAAGL,MAAM,CAAC6D,MAAM,CAACxD,OAAO,GAAG;UAAG;YAAA0C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC/D;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC,eACP3G,OAAA,CAAC5B,IAAI;UAAC6D,IAAI;UAAC6E,EAAE,EAAE,CAAE;UAACC,EAAE,EAAE,EAAG;UAAAX,QAAA,eACrBpG,OAAA,CAACV,WAAW;YACR0H,KAAK,EAAC,YAAY;YAClB7E,IAAI,EAAC,YAAY;YACjBU,KAAK,EAAEY,MAAM,CAACc,MAAM,CAACR,UAAW;YAChCkD,QAAQ,EAAExD,MAAM,CAACyD,YAAa;YAC9BC,KAAK,EAAE1D,MAAM,CAAC2D,OAAO,CAACrD,UAAU,IAAIsD,OAAO,CAAC5D,MAAM,CAAC6D,MAAM,CAACvD,UAAU,CAAE;YACtEwD,UAAU,EAAE9D,MAAM,CAAC2D,OAAO,CAACrD,UAAU,GAAGN,MAAM,CAAC6D,MAAM,CAACvD,UAAU,GAAG,EAAG;YAAAqC,QAAA,EAErE9E,WAAW,CAACU,GAAG,CAAC,CAACC,IAAI,EAAE8G,KAAK,kBACzB/I,OAAA,CAAC1B,QAAQ;cAAauE,KAAK,EAAEZ,IAAI,CAAC+B,GAAI;cAAAoC,QAAA,EACjCnE,IAAI,CAACE;YAAI,GADC4G,KAAK;cAAAvC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAEV,CACb;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACO;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACZ,CAAC,eACP3G,OAAA,CAAC5B,IAAI;UAAC6D,IAAI;UAAC6E,EAAE,EAAE,CAAE;UAACC,EAAE,EAAE,EAAG;UAAAX,QAAA,eACrBpG,OAAA,CAACV,WAAW;YACR0H,KAAK,EAAC,aAAa;YACnB7E,IAAI,EAAC,aAAa;YAClBU,KAAK,EAAEY,MAAM,CAACc,MAAM,CAACN,WAAY;YACjCgD,QAAQ,EAAExD,MAAM,CAACyD,YAAa;YAC9BC,KAAK,EAAE1D,MAAM,CAAC2D,OAAO,CAACnD,WAAW,IAAIoD,OAAO,CAAC5D,MAAM,CAAC6D,MAAM,CAACrD,WAAW,CAAE;YACzEsD,UAAU,EAAE9D,MAAM,CAAC2D,OAAO,CAACnD,WAAW,GAAGR,MAAM,CAAC6D,MAAM,CAACrD,WAAW,GAAG,EAAG;YAAAmC,QAAA,EAEtE5E,YAAY,CAACQ,GAAG,CAAC,CAACC,IAAI,EAAE8G,KAAK,kBAC1B/I,OAAA,CAAC1B,QAAQ;cAAauE,KAAK,EAAEZ,IAAI,CAAC+B,GAAI;cAAAoC,QAAA,EACjCnE,IAAI,CAACE;YAAI,GADC4G,KAAK;cAAAvC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAEV,CACb;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACO;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACZ,CAAC,eAGP3G,OAAA,CAAC5B,IAAI;UAAC6D,IAAI;UAAC6E,EAAE,EAAE,CAAE;UAACC,EAAE,EAAE,EAAG;UAAAX,QAAA,eACrBpG,OAAA,CAAC7B,WAAW;YAACqJ,SAAS;YAAApB,QAAA,gBAClBpG,OAAA,CAACzB,UAAU;cAAC8H,OAAO,EAAC,SAAS;cAAAD,QAAA,EAAC;YAAgB;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eAC3D3G,OAAA,CAAChC,GAAG;cAACsI,EAAE,EAAE;gBACL+B,OAAO,EAAE,MAAM;gBACfC,GAAG,EAAE,GAAG;gBACRU,UAAU,EAAE;cAChB,CAAE;cAAA5C,QAAA,gBACEpG,OAAA,CAACX,KAAK;gBAEF2H,KAAK,EAAC,EAAE;gBACR7E,IAAI,EAAC,WAAW;gBAChBU,KAAK,EAAEY,MAAM,CAACc,MAAM,CAACL,SAAU;gBAC/B+C,QAAQ,EAAE3B,qBAAsB;gBAChC2D,WAAW,EAAEpH,WAAW,KAAK,SAAS,GAAG,UAAU,GAAG,WAAY;gBAClEsF,KAAK,EAAE1D,MAAM,CAAC2D,OAAO,CAAClD,SAAS,IAAImD,OAAO,CAAC5D,MAAM,CAAC6D,MAAM,CAACpD,SAAS,CAAE;gBACpEqD,UAAU,EAAE9D,MAAM,CAAC2D,OAAO,CAAClD,SAAS,GAAGT,MAAM,CAAC6D,MAAM,CAACpD,SAAS,GAAG;cAAG;gBAAAsC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAC,CAAC,eAE1E3G,OAAA,CAAC/B,MAAM;gBACHiL,IAAI,EAAC,OAAO;gBACZ7C,OAAO,EAAC,UAAU;gBAClB8C,OAAO,EAAE3D,iBAAkB;gBAAAY,QAAA,EAE1BvE,WAAW,KAAK,SAAS,GAAG,WAAW,GAAG;cAAa;gBAAA2E,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpD,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACR,CAAC,eACN3G,OAAA,CAACzB,UAAU;cAAC8H,OAAO,EAAC,SAAS;cAACC,EAAE,EAAE;gBAAE8C,EAAE,EAAE,CAAC;gBAAEC,KAAK,EAAE;cAAiB,CAAE;cAAAjD,QAAA,EAChEvE,WAAW,KAAK,SAAS,GAAG,qDAAqD,GAAG;YAAqC;cAAA2E,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClH,CAAC,eACb3G,OAAA,CAACzB,UAAU;cAAC8H,OAAO,EAAC,SAAS;cAACC,EAAE,EAAE;gBAAE8C,EAAE,EAAE,CAAC;gBAAEf,OAAO,EAAE,OAAO;gBAAEgB,KAAK,EAAE;cAAY,CAAE;cAAAjD,QAAA,EAAC;YAEnF;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACZ,CAAC,eAGP3G,OAAA,CAAC5B,IAAI;UAAC6D,IAAI;UAAC6E,EAAE,EAAE,CAAE;UAACC,EAAE,EAAE,EAAG;UAAAX,QAAA,eACrBpG,OAAA,CAACV,WAAW;YACR0H,KAAK,EAAC,mBAAmB;YACzB7E,IAAI,EAAC,+BAA+B;YACpCU,KAAK,EAAEoD,oBAAoB,CAAC,kBAAkB,EAAE,WAAW,CAAE;YAC7DgB,QAAQ,EAAGnC,CAAC,IAAKa,wBAAwB,CAAC,kBAAkB,EAAEb,CAAC,CAACS,MAAM,CAAC1C,KAAK,CAAE;YAAAuD,QAAA,EAE7ExG,kBAAkB,CAACoC,GAAG,CAAEsH,QAAQ,iBAC7BtJ,OAAA,CAAC1B,QAAQ;cAAsBuE,KAAK,EAAEyG,QAAQ,CAACzG,KAAM;cAAAuD,QAAA,EAChDkD,QAAQ,CAACtC;YAAK,GADJsC,QAAQ,CAACzG,KAAK;cAAA2D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAEnB,CACb;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACO;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACZ,CAAC,eAEP3G,OAAA,CAAC5B,IAAI;UAAC6D,IAAI;UAAC6E,EAAE,EAAE,CAAE;UAACC,EAAE,EAAE,EAAG;UAAAX,QAAA,eACrBpG,OAAA,CAACX,KAAK;YACF2H,KAAK,EAAC,aAAa;YACnB7E,IAAI,EAAC,yBAAyB;YAC9BP,IAAI,EAAC,MAAM;YACXiB,KAAK,EAAEgD,kBAAkB,EAAA3E,qBAAA,GAACuC,MAAM,CAACc,MAAM,CAACJ,YAAY,cAAAjD,qBAAA,uBAA1BA,qBAAA,CAA4BqI,UAAU,CAAE;YAClEtC,QAAQ,EAAGnC,CAAC,IAAKa,wBAAwB,CAAC,YAAY,EAAEb,CAAC,CAACS,MAAM,CAAC1C,KAAK;UAAE;YAAA2D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC,eAEP3G,OAAA,CAAC5B,IAAI;UAAC6D,IAAI;UAAC6E,EAAE,EAAE,CAAE;UAACC,EAAE,EAAE,EAAG;UAAAX,QAAA,eACrBpG,OAAA,CAACX,KAAK;YACF2H,KAAK,EAAC,WAAW;YACjB7E,IAAI,EAAC,uBAAuB;YAC5BP,IAAI,EAAC,MAAM;YACXiB,KAAK,EAAEgD,kBAAkB,EAAA1E,sBAAA,GAACsC,MAAM,CAACc,MAAM,CAACJ,YAAY,cAAAhD,sBAAA,uBAA1BA,sBAAA,CAA4BqI,QAAQ,CAAE;YAChEvC,QAAQ,EAAGnC,CAAC,IAAKa,wBAAwB,CAAC,UAAU,EAAEb,CAAC,CAACS,MAAM,CAAC1C,KAAK;UAAE;YAAA2D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC,eAEP3G,OAAA,CAAC5B,IAAI;UAAC6D,IAAI;UAAC6E,EAAE,EAAE,CAAE;UAACC,EAAE,EAAE,EAAG;UAAAX,QAAA,eACrBpG,OAAA,CAACV,WAAW;YACR0H,KAAK,EAAC,YAAY;YAClB7E,IAAI,EAAC,wBAAwB;YAC7BU,KAAK,EAAEoD,oBAAoB,CAAC,WAAW,EAAE,OAAO,CAAE;YAClDgB,QAAQ,EAAGnC,CAAC,IAAKa,wBAAwB,CAAC,WAAW,EAAEb,CAAC,CAACS,MAAM,CAAC1C,KAAK,CAAE;YAAAuD,QAAA,EAEtEtG,YAAY,CAACkC,GAAG,CAAE6F,MAAM,iBACrB7H,OAAA,CAAC1B,QAAQ;cAAoBuE,KAAK,EAAEgF,MAAM,CAAChF,KAAM;cAAAuD,QAAA,EAC5CyB,MAAM,CAACb;YAAK,GADFa,MAAM,CAAChF,KAAK;cAAA2D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAEjB,CACb;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACO;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACZ,CAAC,eAEP3G,OAAA,CAAC5B,IAAI;UAAC6D,IAAI;UAAC6E,EAAE,EAAE,CAAE;UAACC,EAAE,EAAE,EAAG;UAAAX,QAAA,eACrBpG,OAAA,CAACV,WAAW;YACR0H,KAAK,EAAC,UAAU;YAChB7E,IAAI,EAAC,sBAAsB;YAC3BU,KAAK,EAAEoD,oBAAoB,CAAC,SAAS,EAAE,OAAO,CAAE;YAChDgB,QAAQ,EAAGnC,CAAC,IAAKa,wBAAwB,CAAC,SAAS,EAAEb,CAAC,CAACS,MAAM,CAAC1C,KAAK,CAAE;YAAAuD,QAAA,EAEpEtG,YAAY,CAACkC,GAAG,CAAE6F,MAAM,iBACrB7H,OAAA,CAAC1B,QAAQ;cAAoBuE,KAAK,EAAEgF,MAAM,CAAChF,KAAM;cAAAuD,QAAA,EAC5CyB,MAAM,CAACb;YAAK,GADFa,MAAM,CAAChF,KAAK;cAAA2D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAEjB,CACb;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACO;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACZ,CAAC,eAEP3G,OAAA,CAAC5B,IAAI;UAAC6D,IAAI;UAAC6E,EAAE,EAAE,CAAE;UAACC,EAAE,EAAE,EAAG;UAAAX,QAAA,eACrBpG,OAAA,CAACX,KAAK;YACF2H,KAAK,EAAC,eAAe;YACrB7E,IAAI,EAAC,2BAA2B;YAChCP,IAAI,EAAC,QAAQ;YACb6H,IAAI,EAAC,KAAK;YACV5G,KAAK,EAAEoD,oBAAoB,CAAC,cAAc,EAAE,GAAG,CAAE;YACjDgB,QAAQ,EAAGnC,CAAC,IAAKa,wBAAwB,CAAC,cAAc,EAAEP,UAAU,CAACN,CAAC,CAACS,MAAM,CAAC1C,KAAK,CAAC;UAAE;YAAA2D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrF;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC,EAENlH,GAAG,CAACC,OAAO,CAACgK,OAAO,EAAE/J,QAAQ,CAACQ,IAAI,CAAC,iBAChCH,OAAA,CAAC5B,IAAI;UAACkI,EAAE,EAAE;YAAE8C,EAAE,EAAE;UAAE,CAAE;UAACnH,IAAI;UAAC2E,SAAS;UAAC+C,cAAc,EAAC,UAAU;UAAAvD,QAAA,eACzDpG,OAAA,CAAC/B,MAAM;YACH2D,IAAI,EAAC,QAAQ;YACbyH,KAAK,EAAC,SAAS;YACfhD,OAAO,EAAC,WAAW;YAAAD,QAAA,EAAC;UAExB;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACP,CACT;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACL,CAAC;AAEf;AAACpG,EAAA,CAxZuBN,gBAAgB;EAAA,QAEnBtB,WAAW,EACdH,QAAQ,EACFI,WAAW,EACVA,WAAW,EAChBA,WAAW,EAiDZQ,SAAS;AAAA;AAAAwK,EAAA,GAvDJ3J,gBAAgB;AAAA,IAAA2J,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}