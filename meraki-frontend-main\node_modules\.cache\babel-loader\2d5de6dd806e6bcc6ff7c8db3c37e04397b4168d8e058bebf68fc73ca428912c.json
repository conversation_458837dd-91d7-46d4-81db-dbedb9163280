{"ast": null, "code": "import { get, post, patch, del } from \"../utils/api\";\n\n// Use the department endpoint directly without the full URL\n// The api.js utility will handle adding the base URL\nconst GetDepartments = async params => get('department', params);\n_c = GetDepartments;\nconst GetDepartmentById = async id => get(`department/${id}`);\n_c2 = GetDepartmentById;\nconst CreateDepartment = async params => post('department', params);\n_c3 = CreateDepartment;\nconst UpdateDepartment = async (id, params) => patch(`department/${id}`, params);\n_c4 = UpdateDepartment;\nconst DeleteDepartment = async id => del(`department/${id}`);\n_c5 = DeleteDepartment;\nexport const DepartmentService = {\n  GetDepartments,\n  GetDepartmentById,\n  CreateDepartment,\n  UpdateDepartment,\n  DeleteDepartment\n};\nvar _c, _c2, _c3, _c4, _c5;\n$RefreshReg$(_c, \"GetDepartments\");\n$RefreshReg$(_c2, \"GetDepartmentById\");\n$RefreshReg$(_c3, \"CreateDepartment\");\n$RefreshReg$(_c4, \"UpdateDepartment\");\n$RefreshReg$(_c5, \"DeleteDepartment\");", "map": {"version": 3, "names": ["get", "post", "patch", "del", "GetDepartments", "params", "_c", "GetDepartmentById", "id", "_c2", "CreateDepartment", "_c3", "UpdateDepartment", "_c4", "DeleteDepartment", "_c5", "DepartmentService", "$RefreshReg$"], "sources": ["E:/Suraj Patil/Organization_Management_System/meraki-frontend-main/src/services/DepartmentService.js"], "sourcesContent": ["import {get, post, patch, del} from \"../utils/api\";\r\n\r\n\r\n// Use the department endpoint directly without the full URL\r\n// The api.js utility will handle adding the base URL\r\nconst GetDepartments = async (params) => get('department', params);\r\n\r\nconst GetDepartmentById = async (id) => get(`department/${id}`);\r\n\r\nconst CreateDepartment = async (params) => post('department', params);\r\n\r\nconst UpdateDepartment = async (id, params) => patch(`department/${id}`, params);\r\n\r\nconst DeleteDepartment = async (id) => del(`department/${id}`);\r\n\r\nexport const DepartmentService = {\r\n    GetDepartments,\r\n    GetDepartmentById,\r\n    CreateDepartment,\r\n    UpdateDepartment,\r\n    DeleteDepartment\r\n};\r\n"], "mappings": "AAAA,SAAQA,GAAG,EAAEC,IAAI,EAAEC,KAAK,EAAEC,GAAG,QAAO,cAAc;;AAGlD;AACA;AACA,MAAMC,cAAc,GAAG,MAAOC,MAAM,IAAKL,GAAG,CAAC,YAAY,EAAEK,MAAM,CAAC;AAACC,EAAA,GAA7DF,cAAc;AAEpB,MAAMG,iBAAiB,GAAG,MAAOC,EAAE,IAAKR,GAAG,CAAC,cAAcQ,EAAE,EAAE,CAAC;AAACC,GAAA,GAA1DF,iBAAiB;AAEvB,MAAMG,gBAAgB,GAAG,MAAOL,MAAM,IAAKJ,IAAI,CAAC,YAAY,EAAEI,MAAM,CAAC;AAACM,GAAA,GAAhED,gBAAgB;AAEtB,MAAME,gBAAgB,GAAG,MAAAA,CAAOJ,EAAE,EAAEH,MAAM,KAAKH,KAAK,CAAC,cAAcM,EAAE,EAAE,EAAEH,MAAM,CAAC;AAACQ,GAAA,GAA3ED,gBAAgB;AAEtB,MAAME,gBAAgB,GAAG,MAAON,EAAE,IAAKL,GAAG,CAAC,cAAcK,EAAE,EAAE,CAAC;AAACO,GAAA,GAAzDD,gBAAgB;AAEtB,OAAO,MAAME,iBAAiB,GAAG;EAC7BZ,cAAc;EACdG,iBAAiB;EACjBG,gBAAgB;EAChBE,gBAAgB;EAChBE;AACJ,CAAC;AAAC,IAAAR,EAAA,EAAAG,GAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAE,GAAA;AAAAE,YAAA,CAAAX,EAAA;AAAAW,YAAA,CAAAR,GAAA;AAAAQ,YAAA,CAAAN,GAAA;AAAAM,YAAA,CAAAJ,GAAA;AAAAI,YAAA,CAAAF,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}