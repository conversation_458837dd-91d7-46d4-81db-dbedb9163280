{"ast": null, "code": "var _jsxFileName = \"E:\\\\Suraj Patil\\\\Organization_Management_System\\\\meraki-frontend-main\\\\src\\\\screens\\\\User\\\\components\\\\Create\\\\AccountSetting.js\",\n  _s = $RefreshSig$();\nimport React, { useState } from \"react\";\nimport { Card, Chip, FormControl, Grid, IconButton, InputAdornment, MenuItem, Typography } from \"@mui/material\";\nimport ROLES from \"constants/role\";\nimport Box from \"@mui/material/Box\";\nimport Input from \"components/Input\";\nimport SelectField from \"components/SelectField\";\nimport { Visibility, VisibilityOff } from \"@mui/icons-material\";\nimport PropTypes from \"prop-types\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nAccountSetting.propTypes = {\n  formik: PropTypes.object\n};\nexport default function AccountSetting(props) {\n  _s();\n  const {\n    formik\n  } = props;\n  const [showPassword, setShowPassword] = useState(false);\n  const handleClickShowPassword = () => {\n    setShowPassword(!showPassword);\n  };\n  const handleMouseDownPassword = event => {\n    event.preventDefault();\n  };\n  const handleRole = ({\n    target\n  }) => {\n    formik.setFieldValue('role', target.value);\n  };\n  return /*#__PURE__*/_jsxDEV(Card, {\n    sx: {\n      mb: 3\n    },\n    children: [/*#__PURE__*/_jsxDEV(Typography, {\n      variant: \"h5\",\n      sx: {\n        mb: 4\n      },\n      children: \"Account Setting\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 39,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(Grid, {\n      container: true,\n      spacing: 3,\n      children: [/*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        lg: 6,\n        xs: 12,\n        children: /*#__PURE__*/_jsxDEV(Input, {\n          label: \"Email\",\n          type: \"email\",\n          name: \"email\",\n          value: formik.values.email,\n          onChange: formik.handleChange,\n          error: Boolean(formik.touched.email) && Boolean(formik.errors.email),\n          helpertext: formik.touched.email ? formik.errors.email : \"\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 42,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 41,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        lg: 6,\n        xs: 12,\n        children: /*#__PURE__*/_jsxDEV(Input, {\n          label: \"Password\",\n          inputProps: {\n            autoComplete: \"new-password\"\n          },\n          placeholder: \"\\u25CF\\u25CF\\u25CF\\u25CF\\u25CF\\u25CF\\u25CF\\u25CF\\u25CF\\u25CF\",\n          type: \"password\",\n          name: \"password\",\n          endAdornment: /*#__PURE__*/_jsxDEV(InputAdornment, {\n            position: \"end\",\n            children: /*#__PURE__*/_jsxDEV(IconButton, {\n              \"aria-label\": \"toggle password visibility\",\n              onClick: handleClickShowPassword,\n              onMouseDown: handleMouseDownPassword,\n              edge: \"end\",\n              children: !showPassword ? /*#__PURE__*/_jsxDEV(VisibilityOff, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 68,\n                columnNumber: 54\n              }, this) : /*#__PURE__*/_jsxDEV(Visibility, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 68,\n                columnNumber: 74\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 62,\n              columnNumber: 33\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 61,\n            columnNumber: 29\n          }, this),\n          value: formik.values.password,\n          onChange: formik.handleChange,\n          error: formik.touched.password && Boolean(formik.errors.password),\n          helpertext: formik.touched.password ? formik.errors.password : \"\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 52,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 51,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        lg: 12,\n        xs: 12,\n        children: /*#__PURE__*/_jsxDEV(FormControl, {\n          fullWidth: true,\n          children: /*#__PURE__*/_jsxDEV(SelectField, {\n            multiple: true,\n            value: formik.values.role,\n            onChange: handleRole,\n            input: /*#__PURE__*/_jsxDEV(Input, {\n              sx: {\n                '& .MuiInputBase-root': {\n                  height: 'auto'\n                }\n              },\n              label: \"Role\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 83,\n              columnNumber: 36\n            }, this),\n            renderValue: selected => /*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                display: 'flex',\n                flexWrap: 'wrap',\n                gap: 0.5\n              },\n              children: selected.map(value => /*#__PURE__*/_jsxDEV(Chip, {\n                label: value\n              }, value, false, {\n                fileName: _jsxFileName,\n                lineNumber: 91,\n                columnNumber: 41\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 89,\n              columnNumber: 33\n            }, this),\n            children: Object.keys(ROLES).map(key => /*#__PURE__*/_jsxDEV(MenuItem, {\n              value: key,\n              children: ROLES[key].name\n            }, key, false, {\n              fileName: _jsxFileName,\n              lineNumber: 96,\n              columnNumber: 33\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 79,\n            columnNumber: 25\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 78,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 77,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 40,\n      columnNumber: 13\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 38,\n    columnNumber: 9\n  }, this);\n}\n_s(AccountSetting, \"daguiRHWMFkqPgCh/ppD7CF5VuQ=\");\n_c = AccountSetting;\nvar _c;\n$RefreshReg$(_c, \"AccountSetting\");", "map": {"version": 3, "names": ["React", "useState", "Card", "Chip", "FormControl", "Grid", "IconButton", "InputAdornment", "MenuItem", "Typography", "ROLES", "Box", "Input", "SelectField", "Visibility", "VisibilityOff", "PropTypes", "jsxDEV", "_jsxDEV", "AccountSetting", "propTypes", "formik", "object", "props", "_s", "showPassword", "setShowPassword", "handleClickShowPassword", "handleMouseDownPassword", "event", "preventDefault", "handleRole", "target", "setFieldValue", "value", "sx", "mb", "children", "variant", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "container", "spacing", "item", "lg", "xs", "label", "type", "name", "values", "email", "onChange", "handleChange", "error", "Boolean", "touched", "errors", "helpertext", "inputProps", "autoComplete", "placeholder", "endAdornment", "position", "onClick", "onMouseDown", "edge", "password", "fullWidth", "multiple", "role", "input", "height", "renderValue", "selected", "display", "flexWrap", "gap", "map", "Object", "keys", "key", "_c", "$RefreshReg$"], "sources": ["E:/Suraj Patil/Organization_Management_System/meraki-frontend-main/src/screens/User/components/Create/AccountSetting.js"], "sourcesContent": ["import React, {useState} from \"react\";\r\nimport {\r\n    Card,\r\n    Chip, FormControl,\r\n    Grid, IconButton, InputAdornment,\r\n    MenuItem,\r\n    Typography\r\n} from \"@mui/material\";\r\nimport ROLES from \"constants/role\";\r\nimport Box from \"@mui/material/Box\";\r\nimport Input from \"components/Input\";\r\nimport <PERSON>Field from \"components/SelectField\";\r\nimport {Visibility, VisibilityOff} from \"@mui/icons-material\";\r\nimport PropTypes from \"prop-types\";\r\n\r\nAccountSetting.propTypes = {\r\n    formik: PropTypes.object\r\n};\r\n\r\nexport default function AccountSetting(props) {\r\n    const { formik } = props;\r\n\r\n    const [showPassword, setShowPassword] = useState(false);\r\n\r\n    const handleClickShowPassword = () => {\r\n        setShowPassword(!showPassword);\r\n    };\r\n\r\n    const handleMouseDownPassword = (event) => {\r\n        event.preventDefault();\r\n    };\r\n\r\n    const handleRole = ({ target }) => {\r\n        formik.setFieldValue('role', target.value);\r\n    }\r\n\r\n    return (\r\n        <Card sx={{ mb: 3 }}>\r\n            <Typography variant='h5' sx={{ mb: 4 }}>Account Setting</Typography>\r\n            <Grid container spacing={3}>\r\n                <Grid item lg={6} xs={12}>\r\n                    <Input\r\n                        label=\"Email\"\r\n                        type=\"email\"\r\n                        name='email'\r\n                        value={formik.values.email}\r\n                        onChange={formik.handleChange}\r\n                        error={Boolean(formik.touched.email) && Boolean(formik.errors.email)}\r\n                        helpertext={formik.touched.email ? formik.errors.email : \"\"}/>\r\n                </Grid>\r\n                <Grid item lg={6} xs={12}>\r\n                    <Input\r\n                        label=\"Password\"\r\n                        inputProps={{\r\n                            autoComplete: \"new-password\"\r\n                        }}\r\n                        placeholder='●●●●●●●●●●'\r\n                        type=\"password\"\r\n                        name='password'\r\n                        endAdornment={\r\n                            <InputAdornment position=\"end\">\r\n                                <IconButton\r\n                                    aria-label=\"toggle password visibility\"\r\n                                    onClick={handleClickShowPassword}\r\n                                    onMouseDown={handleMouseDownPassword}\r\n                                    edge=\"end\"\r\n                                >\r\n                                    {!showPassword ? <VisibilityOff /> : <Visibility />}\r\n                                </IconButton>\r\n                            </InputAdornment>\r\n                        }\r\n                        value={formik.values.password}\r\n                        onChange={formik.handleChange}\r\n                        error={formik.touched.password && Boolean(formik.errors.password)}\r\n                        helpertext={formik.touched.password ? formik.errors.password : \"\"}/>\r\n                </Grid>\r\n                <Grid item lg={12} xs={12}>\r\n                    <FormControl fullWidth>\r\n                        <SelectField\r\n                            multiple\r\n                            value={formik.values.role}\r\n                            onChange={handleRole}\r\n                            input={<Input sx={{\r\n                                '& .MuiInputBase-root': {\r\n                                    height: 'auto'\r\n                                }\r\n                            }} label=\"Role\" />}\r\n                            renderValue={(selected) => (\r\n                                <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>\r\n                                    {selected.map((value) => (\r\n                                        <Chip key={value} label={value} />\r\n                                    ))}\r\n                                </Box>\r\n                            )}>\r\n                            {Object.keys(ROLES).map(key => (\r\n                                <MenuItem key={key} value={key}>\r\n                                    {ROLES[key].name}\r\n                                </MenuItem>\r\n                            ))}\r\n                        </SelectField>\r\n                    </FormControl>\r\n                </Grid>\r\n            </Grid>\r\n        </Card>\r\n    )\r\n}"], "mappings": ";;AAAA,OAAOA,KAAK,IAAGC,QAAQ,QAAO,OAAO;AACrC,SACIC,IAAI,EACJC,IAAI,EAAEC,WAAW,EACjBC,IAAI,EAAEC,UAAU,EAAEC,cAAc,EAChCC,QAAQ,EACRC,UAAU,QACP,eAAe;AACtB,OAAOC,KAAK,MAAM,gBAAgB;AAClC,OAAOC,GAAG,MAAM,mBAAmB;AACnC,OAAOC,KAAK,MAAM,kBAAkB;AACpC,OAAOC,WAAW,MAAM,wBAAwB;AAChD,SAAQC,UAAU,EAAEC,aAAa,QAAO,qBAAqB;AAC7D,OAAOC,SAAS,MAAM,YAAY;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEnCC,cAAc,CAACC,SAAS,GAAG;EACvBC,MAAM,EAAEL,SAAS,CAACM;AACtB,CAAC;AAED,eAAe,SAASH,cAAcA,CAACI,KAAK,EAAE;EAAAC,EAAA;EAC1C,MAAM;IAAEH;EAAO,CAAC,GAAGE,KAAK;EAExB,MAAM,CAACE,YAAY,EAAEC,eAAe,CAAC,GAAGzB,QAAQ,CAAC,KAAK,CAAC;EAEvD,MAAM0B,uBAAuB,GAAGA,CAAA,KAAM;IAClCD,eAAe,CAAC,CAACD,YAAY,CAAC;EAClC,CAAC;EAED,MAAMG,uBAAuB,GAAIC,KAAK,IAAK;IACvCA,KAAK,CAACC,cAAc,CAAC,CAAC;EAC1B,CAAC;EAED,MAAMC,UAAU,GAAGA,CAAC;IAAEC;EAAO,CAAC,KAAK;IAC/BX,MAAM,CAACY,aAAa,CAAC,MAAM,EAAED,MAAM,CAACE,KAAK,CAAC;EAC9C,CAAC;EAED,oBACIhB,OAAA,CAAChB,IAAI;IAACiC,EAAE,EAAE;MAAEC,EAAE,EAAE;IAAE,CAAE;IAAAC,QAAA,gBAChBnB,OAAA,CAACT,UAAU;MAAC6B,OAAO,EAAC,IAAI;MAACH,EAAE,EAAE;QAAEC,EAAE,EAAE;MAAE,CAAE;MAAAC,QAAA,EAAC;IAAe;MAAAE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAY,CAAC,eACpExB,OAAA,CAACb,IAAI;MAACsC,SAAS;MAACC,OAAO,EAAE,CAAE;MAAAP,QAAA,gBACvBnB,OAAA,CAACb,IAAI;QAACwC,IAAI;QAACC,EAAE,EAAE,CAAE;QAACC,EAAE,EAAE,EAAG;QAAAV,QAAA,eACrBnB,OAAA,CAACN,KAAK;UACFoC,KAAK,EAAC,OAAO;UACbC,IAAI,EAAC,OAAO;UACZC,IAAI,EAAC,OAAO;UACZhB,KAAK,EAAEb,MAAM,CAAC8B,MAAM,CAACC,KAAM;UAC3BC,QAAQ,EAAEhC,MAAM,CAACiC,YAAa;UAC9BC,KAAK,EAAEC,OAAO,CAACnC,MAAM,CAACoC,OAAO,CAACL,KAAK,CAAC,IAAII,OAAO,CAACnC,MAAM,CAACqC,MAAM,CAACN,KAAK,CAAE;UACrEO,UAAU,EAAEtC,MAAM,CAACoC,OAAO,CAACL,KAAK,GAAG/B,MAAM,CAACqC,MAAM,CAACN,KAAK,GAAG;QAAG;UAAAb,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAChE,CAAC,eACPxB,OAAA,CAACb,IAAI;QAACwC,IAAI;QAACC,EAAE,EAAE,CAAE;QAACC,EAAE,EAAE,EAAG;QAAAV,QAAA,eACrBnB,OAAA,CAACN,KAAK;UACFoC,KAAK,EAAC,UAAU;UAChBY,UAAU,EAAE;YACRC,YAAY,EAAE;UAClB,CAAE;UACFC,WAAW,EAAC,8DAAY;UACxBb,IAAI,EAAC,UAAU;UACfC,IAAI,EAAC,UAAU;UACfa,YAAY,eACR7C,OAAA,CAACX,cAAc;YAACyD,QAAQ,EAAC,KAAK;YAAA3B,QAAA,eAC1BnB,OAAA,CAACZ,UAAU;cACP,cAAW,4BAA4B;cACvC2D,OAAO,EAAEtC,uBAAwB;cACjCuC,WAAW,EAAEtC,uBAAwB;cACrCuC,IAAI,EAAC,KAAK;cAAA9B,QAAA,EAET,CAACZ,YAAY,gBAAGP,OAAA,CAACH,aAAa;gBAAAwB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,gBAAGxB,OAAA,CAACJ,UAAU;gBAAAyB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3C;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CACnB;UACDR,KAAK,EAAEb,MAAM,CAAC8B,MAAM,CAACiB,QAAS;UAC9Bf,QAAQ,EAAEhC,MAAM,CAACiC,YAAa;UAC9BC,KAAK,EAAElC,MAAM,CAACoC,OAAO,CAACW,QAAQ,IAAIZ,OAAO,CAACnC,MAAM,CAACqC,MAAM,CAACU,QAAQ,CAAE;UAClET,UAAU,EAAEtC,MAAM,CAACoC,OAAO,CAACW,QAAQ,GAAG/C,MAAM,CAACqC,MAAM,CAACU,QAAQ,GAAG;QAAG;UAAA7B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACtE,CAAC,eACPxB,OAAA,CAACb,IAAI;QAACwC,IAAI;QAACC,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,EAAG;QAAAV,QAAA,eACtBnB,OAAA,CAACd,WAAW;UAACiE,SAAS;UAAAhC,QAAA,eAClBnB,OAAA,CAACL,WAAW;YACRyD,QAAQ;YACRpC,KAAK,EAAEb,MAAM,CAAC8B,MAAM,CAACoB,IAAK;YAC1BlB,QAAQ,EAAEtB,UAAW;YACrByC,KAAK,eAAEtD,OAAA,CAACN,KAAK;cAACuB,EAAE,EAAE;gBACd,sBAAsB,EAAE;kBACpBsC,MAAM,EAAE;gBACZ;cACJ,CAAE;cAACzB,KAAK,EAAC;YAAM;cAAAT,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YACnBgC,WAAW,EAAGC,QAAQ,iBAClBzD,OAAA,CAACP,GAAG;cAACwB,EAAE,EAAE;gBAAEyC,OAAO,EAAE,MAAM;gBAAEC,QAAQ,EAAE,MAAM;gBAAEC,GAAG,EAAE;cAAI,CAAE;cAAAzC,QAAA,EACpDsC,QAAQ,CAACI,GAAG,CAAE7C,KAAK,iBAChBhB,OAAA,CAACf,IAAI;gBAAa6C,KAAK,EAAEd;cAAM,GAApBA,KAAK;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAiB,CACpC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CACP;YAAAL,QAAA,EACD2C,MAAM,CAACC,IAAI,CAACvE,KAAK,CAAC,CAACqE,GAAG,CAACG,GAAG,iBACvBhE,OAAA,CAACV,QAAQ;cAAW0B,KAAK,EAAEgD,GAAI;cAAA7C,QAAA,EAC1B3B,KAAK,CAACwE,GAAG,CAAC,CAAChC;YAAI,GADLgC,GAAG;cAAA3C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAER,CACb;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACO;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACZ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACL,CAAC;AAEf;AAAClB,EAAA,CAtFuBL,cAAc;AAAAgE,EAAA,GAAdhE,cAAc;AAAA,IAAAgE,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}