{"ast": null, "code": "const API_URL = \"https://meraki-backend-main.onrender.com/api\";\nasync function createClient(body) {\n  console.log(\"Create Clientt \", body);\n  const token = localStorage.getItem(\"merakihr-token\");\n  const result = await fetch(`${API_URL}/client/create`, {\n    method: 'POST',\n    body: JSON.stringify(body),\n    headers: {\n      Authorization: `Bearer ${token}`,\n      'Content-Type': 'application/json'\n    }\n  });\n  if (result) {\n    return result;\n  }\n}\nasync function updateClient(id, body) {\n  const token = localStorage.getItem(\"merakihr-token\");\n  const result = await fetch(`${API_URL}/client/update/${id}`, {\n    method: 'PATCH',\n    body: JSON.stringify(body),\n    headers: {\n      Authorization: `Bearer ${token}`,\n      'Content-Type': 'application/json'\n    }\n  });\n  if (result) {\n    return result;\n  }\n}\nasync function deleteClient(id, body) {\n  const token = localStorage.getItem(\"merakihr-token\");\n  const result = await fetch(`${API_URL}/client/delete/${id}`, {\n    method: 'DELETE',\n    body: JSON.stringify(body),\n    headers: {\n      Authorization: `Bearer ${token}`,\n      'Content-Type': 'application/json'\n    }\n  });\n  if (result) {\n    return result;\n  }\n}\nasync function getClient(params = {}) {\n  const token = localStorage.getItem(\"merakihr-token\");\n\n  // Convert params object into query string\n  const queryString = new URLSearchParams(params).toString();\n  try {\n    console.log(\"Get CLIENT API called\");\n    const response = await fetch(`${API_URL}/client?${queryString}`, {\n      method: 'GET',\n      headers: {\n        Authorization: `Bearer ${token}`,\n        'Content-Type': 'application/json'\n      }\n    });\n    if (!response.ok) {\n      throw new Error(`HTTP Error! Status: ${response.status}`);\n    }\n    return await response.json(); // Convert response to JSON\n  } catch (error) {\n    console.error(\"Error fetching client:\", error);\n    return null; // Handle error properly\n  }\n}\nexport const ClientService = {\n  createClient,\n  updateClient,\n  getClient,\n  deleteClient\n};", "map": {"version": 3, "names": ["API_URL", "createClient", "body", "console", "log", "token", "localStorage", "getItem", "result", "fetch", "method", "JSON", "stringify", "headers", "Authorization", "updateClient", "id", "deleteClient", "getClient", "params", "queryString", "URLSearchParams", "toString", "response", "ok", "Error", "status", "json", "error", "ClientService"], "sources": ["E:/Suraj Patil/Organization_Management_System/meraki-frontend-main/src/services/ClientService.js"], "sourcesContent": ["\r\n\r\nconst API_URL = \"https://meraki-backend-main.onrender.com/api\";\r\n\r\nasync function createClient (body) {\r\n\r\n    console.log(\"Create Clientt \",body)\r\n    const token = localStorage.getItem(\"merakihr-token\");\r\n    const result = await fetch(`${API_URL}/client/create`, {\r\n        method: 'POST',\r\n         body: JSON.stringify(body),\r\n         headers: {\r\n            Authorization: `Bearer ${token}`,\r\n            'Content-Type': 'application/json'\r\n        }\r\n    })\r\n    if(result) {\r\n         return result\r\n    }\r\n}\r\n\r\nasync function updateClient (id,body) {\r\n\r\n    const token = localStorage.getItem(\"merakihr-token\");\r\n    const result = await fetch(`${API_URL}/client/update/${id}`, {\r\n        method: 'PATCH',\r\n         body: JSON.stringify(body),\r\n         headers: {\r\n            Authorization: `Bearer ${token}`,\r\n            'Content-Type': 'application/json'\r\n        }\r\n    })\r\n    if(result) {\r\n         return result\r\n    }\r\n}\r\n\r\nasync function deleteClient (id,body) {\r\n\r\n    const token = localStorage.getItem(\"merakihr-token\");\r\n    const result = await fetch(`${API_URL}/client/delete/${id}`, {\r\n        method: 'DELETE',\r\n         body: JSON.stringify(body),\r\n         headers: {\r\n            Authorization: `Bearer ${token}`,\r\n            'Content-Type': 'application/json'\r\n        }\r\n    })\r\n    if(result) {\r\n         return result\r\n    }\r\n}\r\n\r\nasync function getClient(params = {}) {\r\n    const token = localStorage.getItem(\"merakihr-token\");\r\n\r\n    // Convert params object into query string\r\n    const queryString = new URLSearchParams(params).toString();\r\n\r\n    try {\r\n        console.log(\"Get CLIENT API called\");\r\n\r\n        const response = await fetch(`${API_URL}/client?${queryString}`, {\r\n            method: 'GET',\r\n            headers: {\r\n                Authorization: `Bearer ${token}`,\r\n                'Content-Type': 'application/json',\r\n            },\r\n        });\r\n\r\n        if (!response.ok) {\r\n            throw new Error(`HTTP Error! Status: ${response.status}`);\r\n        }\r\n\r\n        return await response.json(); // Convert response to JSON\r\n    } catch (error) {\r\n        console.error(\"Error fetching client:\", error);\r\n        return null; // Handle error properly\r\n    }\r\n}\r\n\r\n\r\n\r\nexport const ClientService = {\r\n    createClient,\r\n    updateClient,\r\n    getClient,\r\n    deleteClient\r\n}\r\n"], "mappings": "AAEA,MAAMA,OAAO,GAAG,8CAA8C;AAE9D,eAAeC,YAAYA,CAAEC,IAAI,EAAE;EAE/BC,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAACF,IAAI,CAAC;EACnC,MAAMG,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,gBAAgB,CAAC;EACpD,MAAMC,MAAM,GAAG,MAAMC,KAAK,CAAC,GAAGT,OAAO,gBAAgB,EAAE;IACnDU,MAAM,EAAE,MAAM;IACbR,IAAI,EAAES,IAAI,CAACC,SAAS,CAACV,IAAI,CAAC;IAC1BW,OAAO,EAAE;MACNC,aAAa,EAAE,UAAUT,KAAK,EAAE;MAChC,cAAc,EAAE;IACpB;EACJ,CAAC,CAAC;EACF,IAAGG,MAAM,EAAE;IACN,OAAOA,MAAM;EAClB;AACJ;AAEA,eAAeO,YAAYA,CAAEC,EAAE,EAACd,IAAI,EAAE;EAElC,MAAMG,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,gBAAgB,CAAC;EACpD,MAAMC,MAAM,GAAG,MAAMC,KAAK,CAAC,GAAGT,OAAO,kBAAkBgB,EAAE,EAAE,EAAE;IACzDN,MAAM,EAAE,OAAO;IACdR,IAAI,EAAES,IAAI,CAACC,SAAS,CAACV,IAAI,CAAC;IAC1BW,OAAO,EAAE;MACNC,aAAa,EAAE,UAAUT,KAAK,EAAE;MAChC,cAAc,EAAE;IACpB;EACJ,CAAC,CAAC;EACF,IAAGG,MAAM,EAAE;IACN,OAAOA,MAAM;EAClB;AACJ;AAEA,eAAeS,YAAYA,CAAED,EAAE,EAACd,IAAI,EAAE;EAElC,MAAMG,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,gBAAgB,CAAC;EACpD,MAAMC,MAAM,GAAG,MAAMC,KAAK,CAAC,GAAGT,OAAO,kBAAkBgB,EAAE,EAAE,EAAE;IACzDN,MAAM,EAAE,QAAQ;IACfR,IAAI,EAAES,IAAI,CAACC,SAAS,CAACV,IAAI,CAAC;IAC1BW,OAAO,EAAE;MACNC,aAAa,EAAE,UAAUT,KAAK,EAAE;MAChC,cAAc,EAAE;IACpB;EACJ,CAAC,CAAC;EACF,IAAGG,MAAM,EAAE;IACN,OAAOA,MAAM;EAClB;AACJ;AAEA,eAAeU,SAASA,CAACC,MAAM,GAAG,CAAC,CAAC,EAAE;EAClC,MAAMd,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,gBAAgB,CAAC;;EAEpD;EACA,MAAMa,WAAW,GAAG,IAAIC,eAAe,CAACF,MAAM,CAAC,CAACG,QAAQ,CAAC,CAAC;EAE1D,IAAI;IACAnB,OAAO,CAACC,GAAG,CAAC,uBAAuB,CAAC;IAEpC,MAAMmB,QAAQ,GAAG,MAAMd,KAAK,CAAC,GAAGT,OAAO,WAAWoB,WAAW,EAAE,EAAE;MAC7DV,MAAM,EAAE,KAAK;MACbG,OAAO,EAAE;QACLC,aAAa,EAAE,UAAUT,KAAK,EAAE;QAChC,cAAc,EAAE;MACpB;IACJ,CAAC,CAAC;IAEF,IAAI,CAACkB,QAAQ,CAACC,EAAE,EAAE;MACd,MAAM,IAAIC,KAAK,CAAC,uBAAuBF,QAAQ,CAACG,MAAM,EAAE,CAAC;IAC7D;IAEA,OAAO,MAAMH,QAAQ,CAACI,IAAI,CAAC,CAAC,CAAC,CAAC;EAClC,CAAC,CAAC,OAAOC,KAAK,EAAE;IACZzB,OAAO,CAACyB,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;IAC9C,OAAO,IAAI,CAAC,CAAC;EACjB;AACJ;AAIA,OAAO,MAAMC,aAAa,GAAG;EACzB5B,YAAY;EACZc,YAAY;EACZG,SAAS;EACTD;AACJ,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}