{"ast": null, "code": "import { get, post, patch, del } from \"../utils/api\";\nconst API_URL = \"http://localhost:10000/api\";\nconst GetAttendances = async params => {\n  const result = await get(`${API_URL}/attendance`, params);\n  return result;\n};\n_c = GetAttendances;\nconst GetAttendanceById = async id => get(`${API_URL}/attendance/${id}`);\n_c2 = GetAttendanceById;\nconst GetAttendanceByMonth = async (startDate, endDate) => get(`${API_URL}/attendance/${startDate}/${endDate}`);\n_c3 = GetAttendanceByMonth;\nconst GetAttendanceUserToday = async () => get(`${API_URL}/attendance/today`);\n_c4 = GetAttendanceUserToday;\nconst CreateAttendance = async params => {\n  const token = localStorage.getItem(\"merakihr-token\");\n  const result = await fetch(`${API_URL}/attendance`, {\n    method: 'POST',\n    body: JSON.stringify(params),\n    headers: {\n      Authorization: `Bearer ${token}`,\n      'Content-Type': 'application/json'\n    }\n  });\n  if (!result.ok) {\n    throw new Error(`HTTP error! status: ${result.status}`);\n  }\n  const data = await result.json();\n  return {\n    data\n  };\n};\n_c5 = CreateAttendance;\nconst UpdateAttendance = async (id, params) => {\n  console.log(\"Update Attendenc \", params);\n  return patch(`${API_URL}/attendance/${id}`, params);\n};\n_c6 = UpdateAttendance;\nconst DeleteAttendance = async id => del(`${API_URL}/attendance/${id}`);\n_c7 = DeleteAttendance;\nconst CreateLunch = async (id, params) => patch(`${API_URL}/attendance/lunchcreate/${id}`, params);\n_c8 = CreateLunch;\nconst UpdateLunch = async (id, params) => patch(`${API_URL}/attendance/lunchupdate/${id}`, params);\n_c9 = UpdateLunch;\nexport const AttendanceService = {\n  GetAttendances,\n  GetAttendanceById,\n  GetAttendanceUserToday,\n  CreateAttendance,\n  UpdateAttendance,\n  DeleteAttendance,\n  CreateLunch,\n  UpdateLunch,\n  GetAttendanceByMonth\n};\nvar _c, _c2, _c3, _c4, _c5, _c6, _c7, _c8, _c9;\n$RefreshReg$(_c, \"GetAttendances\");\n$RefreshReg$(_c2, \"GetAttendanceById\");\n$RefreshReg$(_c3, \"GetAttendanceByMonth\");\n$RefreshReg$(_c4, \"GetAttendanceUserToday\");\n$RefreshReg$(_c5, \"CreateAttendance\");\n$RefreshReg$(_c6, \"UpdateAttendance\");\n$RefreshReg$(_c7, \"DeleteAttendance\");\n$RefreshReg$(_c8, \"CreateLunch\");\n$RefreshReg$(_c9, \"UpdateLunch\");", "map": {"version": 3, "names": ["get", "post", "patch", "del", "API_URL", "GetAttendances", "params", "result", "_c", "GetAttendanceById", "id", "_c2", "GetAttendanceByMonth", "startDate", "endDate", "_c3", "GetAttendanceUserToday", "_c4", "CreateAttendance", "token", "localStorage", "getItem", "fetch", "method", "body", "JSON", "stringify", "headers", "Authorization", "ok", "Error", "status", "data", "json", "_c5", "UpdateAttendance", "console", "log", "_c6", "DeleteAttendance", "_c7", "CreateLunch", "_c8", "UpdateLunch", "_c9", "AttendanceService", "$RefreshReg$"], "sources": ["E:/Suraj Patil/Organization_Management_System/meraki-frontend-main/src/services/AttendanceService.js"], "sourcesContent": ["import {get, post, patch, del} from \"../utils/api\";\r\n\r\nconst API_URL = \"http://localhost:10000/api\";\r\n\r\nconst GetAttendances = async (params) => { \r\n    const result = await get(`${API_URL}/attendance`, params)\r\n        return result\r\n\r\n};\r\n\r\nconst GetAttendanceById = async (id) => get(`${API_URL}/attendance/${id}`);\r\n\r\nconst GetAttendanceByMonth = async (startDate,endDate) => get(`${API_URL}/attendance/${startDate}/${endDate}`)\r\n\r\nconst GetAttendanceUserToday = async () => get(`${API_URL}/attendance/today`);\r\n\r\nconst CreateAttendance = async (params) => {\r\n    const token = localStorage.getItem(\"merakihr-token\");\r\n    const result = await fetch(`${API_URL}/attendance`, {\r\n        method: 'POST',\r\n        body: JSON.stringify(params),\r\n        headers: {\r\n            Authorization: `Bearer ${token}`,\r\n            'Content-Type': 'application/json'\r\n        }\r\n    });\r\n\r\n    if (!result.ok) {\r\n        throw new Error(`HTTP error! status: ${result.status}`);\r\n    }\r\n\r\n    const data = await result.json();\r\n    return { data };\r\n};\r\n\r\nconst UpdateAttendance = async (id, params) => {\r\n    console.log(\"Update Attendenc \",params)\r\n    return patch(`${API_URL}/attendance/${id}`, params)\r\n};\r\n\r\nconst DeleteAttendance = async (id) => del(`${API_URL}/attendance/${id}`);\r\n\r\nconst CreateLunch = async (id,params) => patch(`${API_URL}/attendance/lunchcreate/${id}`, params)\r\n \r\n\r\nconst UpdateLunch = async (id,params) => patch(`${API_URL}/attendance/lunchupdate/${id}`,params)\r\n\r\nexport const AttendanceService = {\r\n    GetAttendances,\r\n    GetAttendanceById,\r\n    GetAttendanceUserToday,\r\n    CreateAttendance,\r\n    UpdateAttendance,\r\n    DeleteAttendance,\r\n    CreateLunch,\r\n    UpdateLunch,\r\n    GetAttendanceByMonth\r\n};\r\n"], "mappings": "AAAA,SAAQA,GAAG,EAAEC,IAAI,EAAEC,KAAK,EAAEC,GAAG,QAAO,cAAc;AAElD,MAAMC,OAAO,GAAG,4BAA4B;AAE5C,MAAMC,cAAc,GAAG,MAAOC,MAAM,IAAK;EACrC,MAAMC,MAAM,GAAG,MAAMP,GAAG,CAAC,GAAGI,OAAO,aAAa,EAAEE,MAAM,CAAC;EACrD,OAAOC,MAAM;AAErB,CAAC;AAACC,EAAA,GAJIH,cAAc;AAMpB,MAAMI,iBAAiB,GAAG,MAAOC,EAAE,IAAKV,GAAG,CAAC,GAAGI,OAAO,eAAeM,EAAE,EAAE,CAAC;AAACC,GAAA,GAArEF,iBAAiB;AAEvB,MAAMG,oBAAoB,GAAG,MAAAA,CAAOC,SAAS,EAACC,OAAO,KAAKd,GAAG,CAAC,GAAGI,OAAO,eAAeS,SAAS,IAAIC,OAAO,EAAE,CAAC;AAAAC,GAAA,GAAxGH,oBAAoB;AAE1B,MAAMI,sBAAsB,GAAG,MAAAA,CAAA,KAAYhB,GAAG,CAAC,GAAGI,OAAO,mBAAmB,CAAC;AAACa,GAAA,GAAxED,sBAAsB;AAE5B,MAAME,gBAAgB,GAAG,MAAOZ,MAAM,IAAK;EACvC,MAAMa,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,gBAAgB,CAAC;EACpD,MAAMd,MAAM,GAAG,MAAMe,KAAK,CAAC,GAAGlB,OAAO,aAAa,EAAE;IAChDmB,MAAM,EAAE,MAAM;IACdC,IAAI,EAAEC,IAAI,CAACC,SAAS,CAACpB,MAAM,CAAC;IAC5BqB,OAAO,EAAE;MACLC,aAAa,EAAE,UAAUT,KAAK,EAAE;MAChC,cAAc,EAAE;IACpB;EACJ,CAAC,CAAC;EAEF,IAAI,CAACZ,MAAM,CAACsB,EAAE,EAAE;IACZ,MAAM,IAAIC,KAAK,CAAC,uBAAuBvB,MAAM,CAACwB,MAAM,EAAE,CAAC;EAC3D;EAEA,MAAMC,IAAI,GAAG,MAAMzB,MAAM,CAAC0B,IAAI,CAAC,CAAC;EAChC,OAAO;IAAED;EAAK,CAAC;AACnB,CAAC;AAACE,GAAA,GAjBIhB,gBAAgB;AAmBtB,MAAMiB,gBAAgB,GAAG,MAAAA,CAAOzB,EAAE,EAAEJ,MAAM,KAAK;EAC3C8B,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAC/B,MAAM,CAAC;EACvC,OAAOJ,KAAK,CAAC,GAAGE,OAAO,eAAeM,EAAE,EAAE,EAAEJ,MAAM,CAAC;AACvD,CAAC;AAACgC,GAAA,GAHIH,gBAAgB;AAKtB,MAAMI,gBAAgB,GAAG,MAAO7B,EAAE,IAAKP,GAAG,CAAC,GAAGC,OAAO,eAAeM,EAAE,EAAE,CAAC;AAAC8B,GAAA,GAApED,gBAAgB;AAEtB,MAAME,WAAW,GAAG,MAAAA,CAAO/B,EAAE,EAACJ,MAAM,KAAKJ,KAAK,CAAC,GAAGE,OAAO,2BAA2BM,EAAE,EAAE,EAAEJ,MAAM,CAAC;AAAAoC,GAAA,GAA3FD,WAAW;AAGjB,MAAME,WAAW,GAAG,MAAAA,CAAOjC,EAAE,EAACJ,MAAM,KAAKJ,KAAK,CAAC,GAAGE,OAAO,2BAA2BM,EAAE,EAAE,EAACJ,MAAM,CAAC;AAAAsC,GAAA,GAA1FD,WAAW;AAEjB,OAAO,MAAME,iBAAiB,GAAG;EAC7BxC,cAAc;EACdI,iBAAiB;EACjBO,sBAAsB;EACtBE,gBAAgB;EAChBiB,gBAAgB;EAChBI,gBAAgB;EAChBE,WAAW;EACXE,WAAW;EACX/B;AACJ,CAAC;AAAC,IAAAJ,EAAA,EAAAG,GAAA,EAAAI,GAAA,EAAAE,GAAA,EAAAiB,GAAA,EAAAI,GAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAE,GAAA;AAAAE,YAAA,CAAAtC,EAAA;AAAAsC,YAAA,CAAAnC,GAAA;AAAAmC,YAAA,CAAA/B,GAAA;AAAA+B,YAAA,CAAA7B,GAAA;AAAA6B,YAAA,CAAAZ,GAAA;AAAAY,YAAA,CAAAR,GAAA;AAAAQ,YAAA,CAAAN,GAAA;AAAAM,YAAA,CAAAJ,GAAA;AAAAI,YAAA,CAAAF,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}