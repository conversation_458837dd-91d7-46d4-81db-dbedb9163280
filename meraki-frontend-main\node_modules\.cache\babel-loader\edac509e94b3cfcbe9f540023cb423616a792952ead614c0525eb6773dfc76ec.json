{"ast": null, "code": "var _jsxFileName = \"E:\\\\Suraj Patil\\\\Organization_Management_System\\\\meraki-frontend-main\\\\src\\\\screens\\\\Dashboard\\\\components\\\\ProductivityChart.jsx\",\n  _s = $RefreshSig$();\nimport PropTypes from \"prop-types\";\nimport React, { useState, useEffect } from \"react\";\nimport \"../../../App.css\";\nimport { useDispatch, useSelector } from \"react-redux\";\nimport { ActivityActions } from \"../../../slices/actions\";\nimport { UserSelector } from \"selectors\";\nimport { Tooltip } from '@mui/material';\nimport TimelineRequest from \"./TimelineRequest\";\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst ProductivityChart = ({\n  todayActivities\n}) => {\n  _s();\n  const profile = useSelector(UserSelector.profile());\n  let minArr = [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59];\n  let minArrRev = [...minArr].reverse();\n  const hours = Array.from({\n    length: 24\n  }, (_, i) => `${i} AM`);\n  hours[12] = \"12 PM\";\n  for (let i = 13; i < 24; i++) {\n    hours[i] = `${i - 12} PM`;\n  }\n  const dispatch = useDispatch();\n  const [startTime, setStartTime] = useState(null);\n  const [endTime, setEndTime] = useState(null);\n  const [startTimeline, setStartTimeline] = useState(null);\n  const [endTimeline, setEndTimeline] = useState(null);\n  const [currentTime, setCurrentTime] = useState(new Date());\n  const [chartController, setChartUpdate] = useState(true);\n  const [toolTipTitle, setTooltipTitle] = useState(\"\");\n  const [toolTipController, setToolTipController] = useState(false);\n  const [addRequest, setAddRequest] = useState(false);\n  const [fromRequest, setFromRequest] = useState(\"Idel\");\n  const handleClose = () => setAddRequest(false);\n  useEffect(() => {\n    console.log(\"Product Today Activities \", todayActivities);\n    if (todayActivities.length > 0) {\n      setStartTime(new Date(todayActivities[0].checkInTime));\n      if (Object.keys(todayActivities[0]).includes(\"checkOutTime\")) {\n        setChartUpdate(false);\n        setEndTime(new Date(todayActivities[0].checkOutTime));\n      }\n    }\n    const interval = setInterval(() => {\n      if (chartController) {\n        // it is necessary to get user latest activity\n        console.log(\"Ensure Profile \", profile);\n        dispatch(ActivityActions.getUserActivity({\n          id: profile._id\n        }));\n        if (todayActivities.length > 0) {\n          if (Object.keys(todayActivities[0]).includes(\"checkOutTime\")) {\n            setChartUpdate(false);\n            setEndTime(new Date(todayActivities[0].checkOutTime));\n          } else {\n            setCurrentTime(new Date());\n          }\n        }\n      }\n    }, 30000); // Update every half of minute\n\n    return () => clearInterval(interval);\n  }, [todayActivities, profile._id, dispatch, chartController]);\n  const getSlotColor = (hour, minute) => {\n    var _todayActivities$, _todayActivities$4, _todayActivities$5;\n    const slotTime = new Date(new Date().setHours(hour, minute, 0));\n    if (((_todayActivities$ = todayActivities[0]) === null || _todayActivities$ === void 0 ? void 0 : _todayActivities$.breaksHistory.length) > 0) {\n      for (let i = 0; i < todayActivities[0].breaksHistory.length; i++) {\n        if (!(\"breakEndedTime\" in todayActivities[0].breaksHistory[i])) {\n          var _todayActivities$2, _todayActivities$3;\n          // this is for idel activity checking\n          if (((_todayActivities$2 = todayActivities[0]) === null || _todayActivities$2 === void 0 ? void 0 : _todayActivities$2.idelHistory.length) > 0) {\n            for (let i = 0; i < todayActivities[0].idelHistory.length; i++) {\n              if (!(\"idelEndedTime\" in todayActivities[0].idelHistory[i])) {\n                if (slotTime >= new Date(todayActivities[0].idelHistory[i].idelStartedTime) && slotTime <= currentTime) {\n                  return \"yellow\";\n                } else {\n                  if (slotTime >= startTime && startTime !== null && slotTime <= currentTime) {\n                    return \"#32CD32\";\n                  } else {\n                    return \"lightgrey\";\n                  }\n                }\n              } else {\n                if (slotTime >= new Date(todayActivities[0].idelHistory[i].idelStartedTime) && slotTime <= new Date(todayActivities[0].idelHistory[i].idelEndedTime)) {\n                  return \"yellow\";\n                }\n              }\n            }\n          }\n\n          // this is for timeline request checking\n          if (((_todayActivities$3 = todayActivities[0]) === null || _todayActivities$3 === void 0 ? void 0 : _todayActivities$3.timelineRequestHistory.length) > 0) {\n            for (let i = 0; i < todayActivities[0].timelineRequestHistory.length; i++) {\n              if (!(\"endTimeline\" in todayActivities[0].timelineRequestHistory[i])) {\n                if (slotTime >= new Date(todayActivities[0].timelineRequestHistory[i].startTimeline) && slotTime <= currentTime) {\n                  return \"blue\";\n                } else {\n                  if (slotTime >= startTime && startTime !== null && slotTime <= currentTime) {\n                    return \"#32CD32\";\n                  } else {\n                    return \"lightgrey\";\n                  }\n                }\n              } else {\n                if (slotTime >= new Date(todayActivities[0].timelineRequestHistory[i].startTimeline) && slotTime <= new Date(todayActivities[0].timelineRequestHistory[i].endTimeline)) {\n                  return \"blue\";\n                }\n              }\n            }\n          }\n\n          // this is for break activity checking \n          if (slotTime >= new Date(todayActivities[0].breaksHistory[i].breakStartedTime) && slotTime <= currentTime) {\n            return \"red\";\n          } else {\n            if (slotTime >= startTime && startTime !== null && slotTime <= currentTime) {\n              return \"#32CD32\";\n            } else {\n              return \"lightgrey\";\n            }\n          }\n        } else {\n          if (slotTime >= new Date(todayActivities[0].breaksHistory[i].breakStartedTime) && slotTime <= new Date(todayActivities[0].breaksHistory[i].breakEndedTime)) {\n            return \"red\";\n          }\n        }\n      }\n    }\n    if (((_todayActivities$4 = todayActivities[0]) === null || _todayActivities$4 === void 0 ? void 0 : _todayActivities$4.idelHistory.length) > 0) {\n      for (let i = 0; i < todayActivities[0].idelHistory.length; i++) {\n        if (!(\"idelEndedTime\" in todayActivities[0].idelHistory[i])) {\n          if (slotTime >= new Date(todayActivities[0].idelHistory[i].idelStartedTime) && slotTime <= currentTime) {\n            return \"yellow\";\n          } else {\n            if (slotTime >= startTime && startTime !== null && slotTime <= currentTime) {\n              return \"#32CD32\";\n            } else {\n              return \"lightgrey\";\n            }\n          }\n        } else {\n          if (slotTime >= new Date(todayActivities[0].idelHistory[i].idelStartedTime) && slotTime <= new Date(todayActivities[0].idelHistory[i].idelEndedTime)) {\n            return \"yellow\";\n          }\n        }\n      }\n    }\n    if (((_todayActivities$5 = todayActivities[0]) === null || _todayActivities$5 === void 0 ? void 0 : _todayActivities$5.timelineRequestHistory.length) > 0) {\n      for (let i = 0; i < todayActivities[0].timelineRequestHistory.length; i++) {\n        // if (!(\"endTimeline\" in todayActivities[0].timelineRequestHistory[i])) {\n        //   if (\n        //     slotTime >=\n        //       new Date(todayActivities[0].timelineRequestHistory[i].startTimeline) &&\n        //     slotTime <= currentTime\n        //   ) {\n        //     return \"blue\";\n        //   } else {\n        //     if (\n        //       slotTime >= startTime &&\n        //       startTime !== null &&\n        //       slotTime <= currentTime\n        //     ) {\n        //       return \"#32CD32\";\n        //     } else {\n        //       return \"lightgrey\";\n        //     }\n        //   }\n        // } else {\n        if (slotTime >= new Date(todayActivities[0].timelineRequestHistory[i].startTimeline) && slotTime <= new Date(todayActivities[0].timelineRequestHistory[i].endTimeline)) {\n          return \"blue\";\n        }\n        // }\n      }\n    }\n    if (slotTime >= startTime && startTime !== null && slotTime <= currentTime && endTime === null) {\n      return \"#32CD32\";\n    } else if (slotTime >= startTime && slotTime <= endTime) {\n      return \"#32CD32\";\n    } else {\n      return \"lightgrey\";\n    }\n  };\n  const getVerticalSlotColour = (hour, minute) => {\n    if (startTime !== null) {\n      if (hour >= startTime.getHours() && hour <= currentTime.getHours()) {\n        var _todayActivities$6;\n        if (todayActivities && ((_todayActivities$6 = todayActivities[0]) === null || _todayActivities$6 === void 0 ? void 0 : _todayActivities$6.productivityHistory.length) > 0) {\n          for (let i = 0; i < todayActivities[0].productivityHistory.length; i++) {\n            let hourStart = todayActivities[0].productivityHistory[i].slotHours.split(\"-\");\n            if (Number(hourStart[0]) === hour) {\n              var _todayActivities$0$pr, _todayActivities$0$pr2;\n              if (minute <= ((_todayActivities$0$pr = todayActivities[0].productivityHistory[i]) === null || _todayActivities$0$pr === void 0 ? void 0 : _todayActivities$0$pr.productivityFilled)) {\n                return \"#32CD32\";\n              } else if (minute <= ((_todayActivities$0$pr2 = todayActivities[0].productivityHistory[i]) === null || _todayActivities$0$pr2 === void 0 ? void 0 : _todayActivities$0$pr2.totalSlotFilled)) {\n                return \"#90EE90\";\n              } else {\n                return \"lightgrey\";\n              }\n            }\n          }\n          return \"ligtgrey\";\n        } else {\n          return \"lightgrey\";\n        }\n      } else {\n        return \"lightgrey\";\n      }\n    } else {\n      return \"lightgrey\";\n    }\n  };\n  const normalizeRGB = rgb => {\n    const result = rgb.match(/\\d+/g);\n    return result ? `rgb(${result[0]},${result[1]},${result[2]})` : rgb;\n  };\n  const dateFormat = (startTime, endTime) => {\n    const startTimeStr = new Date(startTime);\n    const endTimeStr = new Date(endTime);\n    let result = (endTimeStr - startTimeStr) / 60000;\n    return result < 60 ? `${Math.floor(result)}m` : `${Math.floor(result / 60)}h ${Math.floor(result % 60)}m `;\n  };\n  const handleMouseEnter = (event, hour, minute) => {\n    const divColor = getComputedStyle(event.currentTarget).backgroundColor;\n    console.log(\"Handle Mouse Eneter \", divColor);\n    switch (normalizeRGB(divColor)) {\n      case \"rgb(255,255,0)\":\n        {\n          setToolTipController(true);\n          const activityDate = new Date(new Date().setHours(hour, minute - 1, 0, 0));\n          let idleFound = false;\n          for (let i = 0; i < todayActivities[0].idelHistory.length; i++) {\n            const start = new Date(todayActivities[0].idelHistory[i].idelStartedTime);\n            const end = new Date(todayActivities[0].idelHistory[i].idelEndedTime);\n            if (start <= activityDate && end >= activityDate) {\n              const str = dateFormat(start, end);\n              setTooltipTitle(`Idle ${str} ${start.getHours()}:${start.getMinutes()}:${start.getSeconds()} - ${end.getHours()}:${end.getMinutes()}:${end.getSeconds()}`);\n              console.log(\" ADD request result \", addRequest);\n              idleFound = true;\n              break;\n            }\n          }\n          if (!idleFound) {\n            console.log(\"Not Idel FOund\");\n            setTooltipTitle(\"Idle\");\n          }\n          break;\n        }\n      case \"rgb(50,205,50)\":\n        {\n          setToolTipController(true);\n          const activityDate = new Date(new Date().setHours(hour, minute - 1, 0, 0));\n          let workFound = false;\n          for (let i = 0; i < todayActivities[0].productivityHoverHistory.length; i++) {\n            const start = new Date(todayActivities[0].productivityHoverHistory[i].startWorkTime);\n            let end = null;\n            if (todayActivities[0].productivityHoverHistory[i].endWorkTime) {\n              end = new Date(todayActivities[0].productivityHoverHistory[i].endWorkTime);\n            } else {\n              end = new Date();\n            }\n            if (start <= activityDate && end >= activityDate) {\n              // console.log(`The color of this div is work ${activityDate} ${start} ${end}`);\n              const str = dateFormat(start, end);\n              setTooltipTitle(`Work ${str} ${start.getHours()}:${start.getMinutes()}:${start.getSeconds()} - ${end.getHours()}:${end.getMinutes()}:${end.getSeconds()}`);\n              workFound = true;\n              break;\n            }\n          }\n          if (!workFound) {\n            setTooltipTitle(\"Work\");\n          }\n          break;\n        }\n      case \"rgb(255,0,0)\":\n        {\n          setToolTipController(true);\n          const activityDate = new Date(new Date().setHours(hour, minute - 1, 0, 0));\n          let breakFound = false;\n          for (let i = 0; i < todayActivities[0].breaksHistory.length; i++) {\n            const start = new Date(todayActivities[0].breaksHistory[i].breakStartedTime);\n            let end = null;\n            if (todayActivities[0].breaksHistory[i].breakEndedTime) {\n              end = new Date(todayActivities[0].breaksHistory[i].breakEndedTime);\n            } else {\n              end = new Date();\n            }\n            if (start <= activityDate && end >= activityDate) {\n              const str = dateFormat(start, end);\n              setTooltipTitle(`Break ${str} ${start.getHours()}:${start.getMinutes()}:${start.getSeconds()} - ${end.getHours()}:${end.getMinutes()}:${end.getSeconds()}`);\n              // setAddRequest(true)\n              console.log(\" ADD request result \", addRequest);\n              // setRequestStartTime(start)\n              // setRequestEndTime(end)\n              breakFound = true;\n              break;\n            }\n          }\n          if (!breakFound) {\n            console.log(\"Breka N\");\n            setTooltipTitle(\"Break\");\n          }\n          break;\n        }\n      case \"rgb(0,0,255)\":\n        {\n          console.log(\"Blue\");\n          setToolTipController(true);\n          const activityDate = new Date(new Date().setHours(hour, minute - 1, 0, 0));\n          let timelineFound = false;\n          let status = \"Approved\";\n          for (let i = 0; i < todayActivities[0].timelineRequestHistory.length; i++) {\n            const start = new Date(todayActivities[0].timelineRequestHistory[i].startTimeline);\n            let end = null;\n            if (todayActivities[0].timelineRequestHistory[i].endTimeline) {\n              end = new Date(todayActivities[0].timelineRequestHistory[i].endTimeline);\n            } else {\n              end = new Date();\n            }\n            if (start <= activityDate && end >= activityDate) {\n              const str = dateFormat(start, end);\n              setTooltipTitle(`Request: ${str} ${start.getHours()}:${start.getMinutes()}:${start.getSeconds()} - ${end.getHours()}:${end.getMinutes()}:${end.getSeconds()}\\nStatus:${status}`);\n              console.log(\" ADD request result \", addRequest);\n              timelineFound = true;\n              break;\n            }\n          }\n          if (!timelineFound) {\n            console.log(\"Timeline N\");\n            setTooltipTitle(\"Request\");\n          }\n          break;\n        }\n      default:\n        {\n          // console.log(\"Blue\")\n          setToolTipController(false);\n          break;\n        }\n    }\n  };\n  const handleMouseClick = (event, hour, minute) => {\n    const divColor = getComputedStyle(event.currentTarget).backgroundColor;\n    switch (normalizeRGB(divColor)) {\n      case \"rgb(255,255,0)\":\n        {\n          const activityDate = new Date(new Date().setHours(hour, minute - 1, 0, 0));\n          for (let i = 0; i < ((_todayActivities$7 = todayActivities[0]) === null || _todayActivities$7 === void 0 ? void 0 : _todayActivities$7.idelHistory.length); i++) {\n            var _todayActivities$7, _todayActivities$8, _todayActivities$9;\n            // console.log(\"Add request set to true \",todayActivities[0].idelHistory[i]);\n            const start = new Date((_todayActivities$8 = todayActivities[0]) === null || _todayActivities$8 === void 0 ? void 0 : _todayActivities$8.idelHistory[i].idelStartedTime);\n            const end = new Date((_todayActivities$9 = todayActivities[0]) === null || _todayActivities$9 === void 0 ? void 0 : _todayActivities$9.idelHistory[i].idelEndedTime);\n            if (start <= activityDate && end >= activityDate) {\n              console.log(\"HOUR HOUR \", start.getHours());\n              setFromRequest(\"Idel\");\n              setStartTimeline(start);\n              setEndTimeline(end);\n              setAddRequest(true);\n              break;\n            }\n          }\n          break;\n        }\n      case \"rgb(255,0,0)\":\n        {\n          const activityDate = new Date(new Date().setHours(hour, minute - 1, 0, 0));\n          for (let i = 0; i < todayActivities[0].breaksHistory.length; i++) {\n            const start = new Date(todayActivities[0].breaksHistory[i].breakStartedTime);\n            let end = null;\n            if (todayActivities[0].breaksHistory[i].breakEndedTime) {\n              end = new Date(todayActivities[0].breaksHistory[i].breakEndedTime);\n            } else {\n              end = new Date();\n            }\n            if (start <= activityDate && end >= activityDate) {\n              setFromRequest(\"Private\");\n              console.log(\" Start Break \", todayActivities[0].breaksHistory[i].breakStartedTime);\n              console.log(\" End Break \", todayActivities[0].breaksHistory[i].breakEndedTime);\n              setStartTimeline(start);\n              setEndTimeline(end);\n              setAddRequest(true);\n              break;\n            }\n          }\n          break;\n        }\n      default:\n        setAddRequest(false);\n        break;\n    }\n  };\n  const renderProgressBars = () => {\n    const progressBars = [];\n    let currentActivity = null;\n    let currentActivityStart = 0;\n    let currentActivityWidth = 0;\n    hours.forEach((hour, hourIndex) => {\n      minArr.forEach(minute => {\n        const activity = getSlotColor(hourIndex, minute);\n        if (activity !== currentActivity) {\n          if (currentActivity !== null) {\n            // Push the current accumulated div\n            progressBars.push(/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"progress-bar\",\n              role: \"progressbar\",\n              style: {\n                width: `${currentActivityWidth}%`,\n                backgroundColor: currentActivity\n              },\n              onMouseEnter: event => handleMouseEnter(event, hourIndex, minute),\n              onClick: event => handleMouseClick(event, hourIndex, minute),\n              children: toolTipController ? /*#__PURE__*/_jsxDEV(Tooltip, {\n                title: toolTipTitle,\n                arrow: true,\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    padding: \"20px\",\n                    display: \"inline-block\"\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 533,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 532,\n                columnNumber: 37\n              }, this) : null\n            }, `${hourIndex}-${minute}`, false, {\n              fileName: _jsxFileName,\n              lineNumber: 521,\n              columnNumber: 15\n            }, this));\n          }\n          // Start a new activity block\n          currentActivity = activity;\n          currentActivityStart = minute;\n          currentActivityWidth = 1.04;\n        } else {\n          // Accumulate width for the same activity\n          currentActivityWidth += 1.04;\n        }\n      });\n    });\n\n    // Push the last accumulated div\n\n    if (currentActivity !== null) {\n      // console.log(\"Accumulated Cell\")\n      progressBars.push(/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"progress-bar\",\n        role: \"progressbar\",\n        style: {\n          width: `${currentActivityWidth}%`,\n          backgroundColor: currentActivity\n        },\n        onMouseEnter: event => handleMouseEnter(event, hours.length - 1, minArr.length - 1)\n        // onMouseLeave={handleMouseLeave}\n        ,\n        children: toolTipController ? /*#__PURE__*/_jsxDEV(Tooltip, {\n          title: toolTipTitle,\n          arrow: true,\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              padding: \"20px\",\n              display: \"inline-block\"\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 568,\n            columnNumber: 19\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 567,\n          columnNumber: 33\n        }, this) : null\n      }, `last-${currentActivityStart}`, false, {\n        fileName: _jsxFileName,\n        lineNumber: 556,\n        columnNumber: 9\n      }, this));\n    }\n    return progressBars;\n  };\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: [addRequest ? /*#__PURE__*/_jsxDEV(_Fragment, {\n      children: /*#__PURE__*/_jsxDEV(TimelineRequest, {\n        startTime: startTimeline,\n        endTime: endTimeline,\n        addRequest: addRequest,\n        setAddRequest: handleClose,\n        fromRequest: fromRequest\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 583,\n        columnNumber: 9\n      }, this)\n    }, void 0, false) : null, /*#__PURE__*/_jsxDEV(\"h2\", {\n      className: \"text-center\",\n      children: \"User Activity Progress\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 586,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"d-flex justify-content-between\",\n      children: hours.map((hour, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bar\",\n        style: {\n          width: \"3.9%\",\n          textAlign: \"center\",\n          height: \"60px\"\n        },\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"slot\",\n          style: {\n            display: \"flex\",\n            flexDirection: \"column\"\n          },\n          children: minArrRev.map((minute, minIndex) => /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              width: \"100%\",\n              height: \"1px\",\n              backgroundColor: getVerticalSlotColour(index, minute)\n            }\n          }, minute, false, {\n            fileName: _jsxFileName,\n            lineNumber: 599,\n            columnNumber: 17\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 594,\n          columnNumber: 13\n        }, this)\n      }, index, false, {\n        fileName: _jsxFileName,\n        lineNumber: 589,\n        columnNumber: 11\n      }, this))\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 587,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        marginBottom: \"10px\"\n      },\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"progress\",\n        style: {\n          height: \"10px\"\n        },\n        children: renderProgressBars()\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 613,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 612,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true);\n};\n_s(ProductivityChart, \"7jdXc32gC9SmvDyhWo6mxQVndWo=\", false, function () {\n  return [useSelector, useDispatch];\n});\n_c = ProductivityChart;\nProductivityChart.propTypes = {\n  todayActivities: PropTypes.array\n};\nexport default ProductivityChart;\nvar _c;\n$RefreshReg$(_c, \"ProductivityChart\");", "map": {"version": 3, "names": ["PropTypes", "React", "useState", "useEffect", "useDispatch", "useSelector", "ActivityActions", "UserSelector", "<PERSON><PERSON><PERSON>", "TimelineRequest", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "ProductivityChart", "todayActivities", "_s", "profile", "minArr", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "reverse", "hours", "Array", "from", "length", "_", "i", "dispatch", "startTime", "setStartTime", "endTime", "setEndTime", "startTimeline", "setStartTimeline", "endTimeline", "setEndTimeline", "currentTime", "setCurrentTime", "Date", "chartController", "setChartUpdate", "toolTipTitle", "setTooltipTitle", "toolTipController", "setToolTipController", "addRequest", "setAddRequest", "fromRequest", "setFromRequest", "handleClose", "console", "log", "checkInTime", "Object", "keys", "includes", "checkOutTime", "interval", "setInterval", "getUserActivity", "id", "_id", "clearInterval", "getSlotColor", "hour", "minute", "_todayActivities$", "_todayActivities$4", "_todayActivities$5", "slotTime", "setHours", "breaksHistory", "_todayActivities$2", "_todayActivities$3", "idelHistory", "idelStartedTime", "idelEndedTime", "timelineRequestHistory", "breakStartedTime", "breakEndedTime", "getVerticalSlotColour", "getHours", "_todayActivities$6", "productivityHistory", "hourStart", "slotHours", "split", "Number", "_todayActivities$0$pr", "_todayActivities$0$pr2", "productivityFilled", "totalSlotFilled", "normalizeRGB", "rgb", "result", "match", "dateFormat", "startTimeStr", "endTimeStr", "Math", "floor", "handleMouseEnter", "event", "divColor", "getComputedStyle", "currentTarget", "backgroundColor", "activityDate", "idleFound", "start", "end", "str", "getMinutes", "getSeconds", "workFound", "productivityHoverHistory", "startWorkTime", "endWorkTime", "breakFound", "timelineFound", "status", "handleMouseClick", "_todayActivities$7", "_todayActivities$8", "_todayActivities$9", "renderProgressBars", "progressBars", "currentActivity", "currentActivityStart", "currentActivityWidth", "for<PERSON>ach", "hourIndex", "activity", "push", "className", "role", "style", "width", "onMouseEnter", "onClick", "children", "title", "arrow", "padding", "display", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "map", "index", "textAlign", "height", "flexDirection", "minIndex", "marginBottom", "_c", "propTypes", "array", "$RefreshReg$"], "sources": ["E:/Suraj Patil/Organization_Management_System/meraki-frontend-main/src/screens/Dashboard/components/ProductivityChart.jsx"], "sourcesContent": ["import PropTypes from \"prop-types\";\r\nimport React, { useState, useEffect } from \"react\";\r\nimport \"../../../App.css\";\r\nimport { useDispatch, useSelector } from \"react-redux\";\r\nimport { ActivityActions } from \"../../../slices/actions\";\r\nimport { UserSelector } from \"selectors\";\r\nimport {\r\n  Tooltip\r\n} from '@mui/material';\r\nimport TimelineRequest from \"./TimelineRequest\";\r\n\r\n\r\nconst ProductivityChart = ({ todayActivities }) => {\r\n\r\n  const profile = useSelector(UserSelector.profile());\r\n  let minArr = [\r\n    0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20,\r\n    21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39,\r\n    40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58,\r\n    59,\r\n  ];\r\n  let minArrRev = [...minArr].reverse();\r\n\r\n  const hours = Array.from({ length: 24 }, (_, i) => `${i} AM`);\r\n  hours[12] = \"12 PM\";\r\n  for (let i = 13; i < 24; i++) {\r\n    hours[i] = `${i - 12} PM`;\r\n  }\r\n  const dispatch = useDispatch();\r\n  const [startTime, setStartTime] = useState(null);\r\n  const [endTime, setEndTime] = useState(null);\r\n  const [startTimeline,setStartTimeline] = useState(null)\r\n  const [endTimeline,setEndTimeline] = useState(null)\r\n  const [currentTime, setCurrentTime] = useState(new Date());\r\n  const [chartController, setChartUpdate] = useState(true);\r\n  const [toolTipTitle, setTooltipTitle] = useState(\"\");\r\n  const [toolTipController,setToolTipController] = useState(false)\r\n  const [addRequest,setAddRequest] = useState(false)\r\n  const [fromRequest,setFromRequest] = useState(\"Idel\")\r\n\r\n  const handleClose = () => setAddRequest(false);\r\n  useEffect(() => {\r\n    console.log(\"Product Today Activities \", todayActivities);\r\n    if (todayActivities.length > 0) {\r\n      setStartTime(new Date(todayActivities[0].checkInTime));\r\n      if (Object.keys(todayActivities[0]).includes(\"checkOutTime\")) {\r\n        setChartUpdate(false);\r\n        setEndTime(new Date(todayActivities[0].checkOutTime));\r\n      }\r\n    }\r\n\r\n    const interval = setInterval(() => {\r\n      if (chartController) {\r\n        // it is necessary to get user latest activity\r\n        console.log(\"Ensure Profile \",profile)\r\n        dispatch(ActivityActions.getUserActivity({\r\n          id:profile._id,\r\n      }));\r\n        if (todayActivities.length > 0) {\r\n          if (Object.keys(todayActivities[0]).includes(\"checkOutTime\")) {\r\n            setChartUpdate(false);\r\n            setEndTime(new Date(todayActivities[0].checkOutTime));\r\n          } else {\r\n            setCurrentTime(new Date());\r\n          }\r\n        }\r\n      }\r\n    }, 30000); // Update every half of minute\r\n\r\n    return () => clearInterval(interval);\r\n  }, [todayActivities, profile._id, dispatch, chartController]);\r\n\r\n  const getSlotColor = (hour, minute) => {\r\n    const slotTime = new Date(new Date().setHours(hour, minute, 0));\r\n\r\n    if (todayActivities[0]?.breaksHistory.length > 0) {\r\n      for (let i = 0; i < todayActivities[0].breaksHistory.length; i++) {\r\n        if (!(\"breakEndedTime\" in todayActivities[0].breaksHistory[i])) {\r\n\r\n          // this is for idel activity checking\r\n          if (todayActivities[0]?.idelHistory.length > 0) {\r\n            for (let i = 0; i < todayActivities[0].idelHistory.length; i++) {\r\n              if (!(\"idelEndedTime\" in todayActivities[0].idelHistory[i])) {\r\n                if (\r\n                  slotTime >=\r\n                    new Date(\r\n                      todayActivities[0].idelHistory[i].idelStartedTime\r\n                    ) &&\r\n                  slotTime <= currentTime\r\n                ) {\r\n                  return \"yellow\";\r\n                } else {\r\n                  if (\r\n                    slotTime >= startTime &&\r\n                    startTime !== null &&\r\n                    slotTime <= currentTime\r\n                  ) {\r\n                    return \"#32CD32\";\r\n                  } else {\r\n                    return \"lightgrey\";\r\n                  }\r\n                }\r\n              } else {\r\n                if (\r\n                  slotTime >=\r\n                    new Date(\r\n                      todayActivities[0].idelHistory[i].idelStartedTime\r\n                    ) &&\r\n                  slotTime <=\r\n                    new Date(todayActivities[0].idelHistory[i].idelEndedTime)\r\n                ) {\r\n                  return \"yellow\";\r\n                }\r\n              }\r\n            }\r\n          }\r\n\r\n          // this is for timeline request checking\r\n          if (todayActivities[0]?.timelineRequestHistory.length > 0) {\r\n            for (let i = 0; i < todayActivities[0].timelineRequestHistory.length; i++) {\r\n              if (!(\"endTimeline\" in todayActivities[0].timelineRequestHistory[i])) {\r\n                if (\r\n                  slotTime >=\r\n                    new Date(\r\n                      todayActivities[0].timelineRequestHistory[i].startTimeline\r\n                    ) &&\r\n                  slotTime <= currentTime\r\n                ) {\r\n                  return \"blue\";\r\n                } else {\r\n                  if (\r\n                    slotTime >= startTime &&\r\n                    startTime !== null &&\r\n                    slotTime <= currentTime\r\n                  ) {\r\n                    return \"#32CD32\";\r\n                  } else {\r\n                    return \"lightgrey\";\r\n                  }\r\n                }\r\n              } else {\r\n                if (\r\n                  slotTime >=\r\n                    new Date(\r\n                      todayActivities[0].timelineRequestHistory[i].startTimeline\r\n                    ) &&\r\n                  slotTime <=\r\n                    new Date(todayActivities[0].timelineRequestHistory[i].endTimeline)\r\n                ) {\r\n                  return \"blue\";\r\n                }\r\n              }\r\n            }\r\n          }\r\n\r\n          // this is for break activity checking \r\n          if (\r\n            slotTime >=\r\n              new Date(todayActivities[0].breaksHistory[i].breakStartedTime) &&\r\n            slotTime <= currentTime\r\n          ) {\r\n            return \"red\";\r\n          } else {\r\n            if (\r\n              slotTime >= startTime &&\r\n              startTime !== null &&\r\n              slotTime <= currentTime\r\n            ) {\r\n              return \"#32CD32\";\r\n            } else {\r\n              return \"lightgrey\";\r\n            }\r\n          }\r\n        } else {\r\n          if (\r\n            slotTime >=\r\n              new Date(todayActivities[0].breaksHistory[i].breakStartedTime) &&\r\n            slotTime <=\r\n              new Date(todayActivities[0].breaksHistory[i].breakEndedTime)\r\n          ) {\r\n            return \"red\";\r\n          }\r\n        }\r\n      }\r\n    }\r\n\r\n    if (todayActivities[0]?.idelHistory.length > 0) {\r\n      for (let i = 0; i < todayActivities[0].idelHistory.length; i++) {\r\n        if (!(\"idelEndedTime\" in todayActivities[0].idelHistory[i])) {\r\n          if (\r\n            slotTime >=\r\n              new Date(todayActivities[0].idelHistory[i].idelStartedTime) &&\r\n            slotTime <= currentTime\r\n          ) {\r\n            return \"yellow\";\r\n          } else {\r\n            if (\r\n              slotTime >= startTime &&\r\n              startTime !== null &&\r\n              slotTime <= currentTime\r\n            ) {\r\n              return \"#32CD32\";\r\n            } else {\r\n              return \"lightgrey\";\r\n            }\r\n          }\r\n        } else {\r\n          if (\r\n            slotTime >=\r\n              new Date(todayActivities[0].idelHistory[i].idelStartedTime) &&\r\n            slotTime <=\r\n              new Date(todayActivities[0].idelHistory[i].idelEndedTime)\r\n          ) {\r\n            return \"yellow\";\r\n          }\r\n        }\r\n      }\r\n    }\r\n\r\n    if(todayActivities[0]?.timelineRequestHistory.length > 0) {\r\n      for (let i = 0; i < todayActivities[0].timelineRequestHistory.length; i++) {\r\n        // if (!(\"endTimeline\" in todayActivities[0].timelineRequestHistory[i])) {\r\n        //   if (\r\n        //     slotTime >=\r\n        //       new Date(todayActivities[0].timelineRequestHistory[i].startTimeline) &&\r\n        //     slotTime <= currentTime\r\n        //   ) {\r\n        //     return \"blue\";\r\n        //   } else {\r\n        //     if (\r\n        //       slotTime >= startTime &&\r\n        //       startTime !== null &&\r\n        //       slotTime <= currentTime\r\n        //     ) {\r\n        //       return \"#32CD32\";\r\n        //     } else {\r\n        //       return \"lightgrey\";\r\n        //     }\r\n        //   }\r\n        // } else {\r\n          if (\r\n            slotTime >=\r\n              new Date(todayActivities[0].timelineRequestHistory[i].startTimeline) &&\r\n            slotTime <=\r\n              new Date(todayActivities[0].timelineRequestHistory[i].endTimeline)\r\n          ) {\r\n            return \"blue\";\r\n          }\r\n        // }\r\n      }\r\n    }\r\n\r\n    if (\r\n      slotTime >= startTime &&\r\n      startTime !== null &&\r\n      slotTime <= currentTime &&\r\n      endTime === null\r\n    ) {\r\n      return \"#32CD32\";\r\n    } else if (slotTime >= startTime && slotTime <= endTime) {\r\n      return \"#32CD32\";\r\n    } else {\r\n      return \"lightgrey\";\r\n    }\r\n  };\r\n\r\n  const getVerticalSlotColour = (hour, minute) => {\r\n\r\n    if (startTime !== null) {\r\n      if (hour >= startTime.getHours() && hour <= currentTime.getHours()) {\r\n   \r\n        if (todayActivities && todayActivities[0]?.productivityHistory.length > 0) {\r\n          for (\r\n            let i = 0;\r\n            i < todayActivities[0].productivityHistory.length;\r\n            i++\r\n          ) {\r\n            let hourStart =\r\n              todayActivities[0].productivityHistory[i].slotHours.split(\"-\");\r\n\r\n            if (Number(hourStart[0]) === hour) {\r\n              if (\r\n                minute <=\r\n                todayActivities[0].productivityHistory[i]?.productivityFilled\r\n              ) {\r\n                return \"#32CD32\";\r\n              } else if (\r\n                minute <=\r\n                todayActivities[0].productivityHistory[i]?.totalSlotFilled\r\n              ) {\r\n                return \"#90EE90\";\r\n              } else {\r\n                return \"lightgrey\";\r\n              }\r\n            }\r\n          }\r\n\r\n          return \"ligtgrey\";\r\n        } else {\r\n          return \"lightgrey\";\r\n        }\r\n      } else {\r\n        return \"lightgrey\";\r\n      }\r\n    } else {\r\n      return \"lightgrey\";\r\n    }\r\n  };\r\n\r\n  const normalizeRGB = (rgb) => {\r\n    const result = rgb.match(/\\d+/g);\r\n    return result ? `rgb(${result[0]},${result[1]},${result[2]})` : rgb;\r\n  };\r\n\r\n  const dateFormat = (startTime, endTime) => {\r\n    const startTimeStr = new Date(startTime);\r\n    const endTimeStr = new Date(endTime);\r\n    let result = (endTimeStr - startTimeStr) / 60000;\r\n    return result < 60 ? `${Math.floor(result)}m` : `${Math.floor(result / 60)}h ${Math.floor(result % 60)}m `;\r\n  };\r\n\r\n  const handleMouseEnter = (event, hour, minute) => {\r\n    const divColor = getComputedStyle(event.currentTarget).backgroundColor;\r\n    console.log(\"Handle Mouse Eneter \",divColor)\r\n\r\n    switch (normalizeRGB(divColor)) {\r\n      case \"rgb(255,255,0)\": {\r\n        setToolTipController(true)\r\n        const activityDate = new Date(new Date().setHours(hour, minute-1, 0, 0));\r\n        let idleFound = false;\r\n        for (let i = 0; i < todayActivities[0].idelHistory.length; i++) {\r\n          const start = new Date(todayActivities[0].idelHistory[i].idelStartedTime);\r\n          const end = new Date(todayActivities[0].idelHistory[i].idelEndedTime);\r\n          if (start <= activityDate && end >= activityDate) {\r\n            const str = dateFormat(start, end);\r\n            setTooltipTitle(`Idle ${str} ${start.getHours()}:${start.getMinutes()}:${start.getSeconds()} - ${end.getHours()}:${end.getMinutes()}:${end.getSeconds()}`);\r\n            console.log(\" ADD request result \",addRequest)\r\n\r\n            idleFound = true;\r\n            break;\r\n          }\r\n        }\r\n        if (!idleFound) {\r\n          console.log(\"Not Idel FOund\")\r\n          setTooltipTitle(\"Idle\");\r\n        }\r\n        break;\r\n      }\r\n      case \"rgb(50,205,50)\": {\r\n        setToolTipController(true)\r\n        const activityDate = new Date(\r\n          new Date().setHours(hour, minute-1, 0, 0)\r\n        );\r\n        let workFound = false;\r\n        for (let i=0; i<todayActivities[0].productivityHoverHistory.length; i++) {\r\n          const start = new Date(\r\n            todayActivities[0].productivityHoverHistory[i].startWorkTime\r\n          );\r\n          let end = null;\r\n          if (todayActivities[0].productivityHoverHistory[i].endWorkTime) {\r\n            end = new Date(todayActivities[0].productivityHoverHistory[i].endWorkTime);\r\n          } else {\r\n            end = new Date();\r\n          }\r\n\r\n          if (start <= activityDate && end >= activityDate) {\r\n            // console.log(`The color of this div is work ${activityDate} ${start} ${end}`);\r\n            const str = dateFormat(start, end);\r\n            setTooltipTitle(`Work ${str} ${start.getHours()}:${start.getMinutes()}:${start.getSeconds()} - ${end.getHours()}:${end.getMinutes()}:${end.getSeconds()}`);\r\n            workFound = true;\r\n            break;\r\n          }\r\n        }\r\n        if (!workFound) {\r\n          setTooltipTitle(\"Work\");\r\n        }\r\n        break;\r\n      }\r\n      case \"rgb(255,0,0)\": {\r\n        setToolTipController(true)\r\n        const activityDate = new Date(new Date().setHours(hour, minute-1, 0, 0));\r\n        let breakFound = false;\r\n        for (let i = 0; i < todayActivities[0].breaksHistory.length; i++) {\r\n          const start = new Date(todayActivities[0].breaksHistory[i].breakStartedTime);\r\n          let end = null;\r\n          if (todayActivities[0].breaksHistory[i].breakEndedTime) {\r\n            end = new Date(todayActivities[0].breaksHistory[i].breakEndedTime);\r\n          } else {\r\n            end = new Date();\r\n          }\r\n\r\n          if (start <= activityDate && end >= activityDate) {\r\n         \r\n            const str = dateFormat(start, end);\r\n            setTooltipTitle(`Break ${str} ${start.getHours()}:${start.getMinutes()}:${start.getSeconds()} - ${end.getHours()}:${end.getMinutes()}:${end.getSeconds()}`);\r\n            // setAddRequest(true)\r\n            console.log(\" ADD request result \",addRequest)\r\n            // setRequestStartTime(start)\r\n            // setRequestEndTime(end)\r\n            breakFound = true;\r\n            break;\r\n          }\r\n        }\r\n        if (!breakFound) {\r\n          console.log(\"Breka N\")\r\n          setTooltipTitle(\"Break\");\r\n        }\r\n        break;\r\n      }\r\n\r\n      case \"rgb(0,0,255)\": {\r\n        console.log(\"Blue\")\r\n        setToolTipController(true)\r\n        const activityDate = new Date(new Date().setHours(hour, minute-1, 0, 0));\r\n        let timelineFound = false;\r\n        let status = \"Approved\"\r\n        for (let i = 0; i < todayActivities[0].timelineRequestHistory.length; i++) {\r\n          const start = new Date(todayActivities[0].timelineRequestHistory[i].startTimeline);\r\n          let end = null;\r\n          if (todayActivities[0].timelineRequestHistory[i].endTimeline) {\r\n            end = new Date(todayActivities[0].timelineRequestHistory[i].endTimeline);\r\n          } else {\r\n            end = new Date();\r\n          }\r\n\r\n          if (start <= activityDate && end >= activityDate) {\r\n         \r\n            const str = dateFormat(start, end);\r\n            setTooltipTitle(`Request: ${str} ${start.getHours()}:${start.getMinutes()}:${start.getSeconds()} - ${end.getHours()}:${end.getMinutes()}:${end.getSeconds()}\\nStatus:${status}`);\r\n\r\n            console.log(\" ADD request result \",addRequest)\r\n            timelineFound = true;\r\n            break;\r\n          }\r\n        }\r\n        if (!timelineFound) {\r\n          console.log(\"Timeline N\")\r\n          setTooltipTitle(\"Request\");\r\n        }\r\n        break;\r\n      }\r\n                                \r\n      default: {\r\n        // console.log(\"Blue\")\r\n        setToolTipController(false)\r\n      \r\n        break;\r\n      }\r\n    }\r\n  };\r\n\r\n  const handleMouseClick = (event, hour, minute) => {\r\n    const divColor = getComputedStyle(event.currentTarget).backgroundColor;\r\n  \r\n    switch (normalizeRGB(divColor)) {\r\n      case \"rgb(255,255,0)\": {\r\n        const activityDate = new Date(new Date().setHours(hour, minute - 1, 0, 0));\r\n          for (let i = 0; i < todayActivities[0]?.idelHistory.length; i++) {\r\n            // console.log(\"Add request set to true \",todayActivities[0].idelHistory[i]);\r\n            const start = new Date(todayActivities[0]?.idelHistory[i].idelStartedTime);\r\n            const end = new Date(todayActivities[0]?.idelHistory[i].idelEndedTime);\r\n    \r\n            if (start <= activityDate && end >= activityDate) {\r\n              console.log(\"HOUR HOUR \",start.getHours())\r\n              setFromRequest(\"Idel\")\r\n              setStartTimeline(start)\r\n              setEndTimeline(end)\r\n              setAddRequest(true);\r\n              break;\r\n            }\r\n          }\r\n     \r\n        break;\r\n      }\r\n  \r\n      case \"rgb(255,0,0)\": {\r\n        const activityDate = new Date(new Date().setHours(hour, minute - 1, 0, 0));\r\n        for (let i = 0; i < todayActivities[0].breaksHistory.length; i++) {\r\n          const start = new Date(todayActivities[0].breaksHistory[i].breakStartedTime);\r\n          let end = null;\r\n  \r\n          if (todayActivities[0].breaksHistory[i].breakEndedTime) {\r\n            end = new Date(todayActivities[0].breaksHistory[i].breakEndedTime);\r\n          } else {\r\n            end = new Date();\r\n          }\r\n  \r\n          if (start <= activityDate && end >= activityDate) {\r\n            setFromRequest(\"Private\")\r\n            console.log(\" Start Break \",todayActivities[0].breaksHistory[i].breakStartedTime)\r\n            console.log(\" End Break \",todayActivities[0].breaksHistory[i].breakEndedTime)\r\n            setStartTimeline(start)\r\n            setEndTimeline(end)\r\n            setAddRequest(true);\r\n            break;\r\n          }\r\n        }\r\n        break;\r\n      }\r\n      \r\n      default:  setAddRequest(false);\r\n        break;\r\n    }\r\n   \r\n  }\r\n  \r\n\r\n  const renderProgressBars = () => {\r\n    const progressBars = [];\r\n    let currentActivity = null;\r\n    let currentActivityStart = 0;\r\n    let currentActivityWidth = 0;\r\n\r\n    hours.forEach((hour, hourIndex) => {\r\n      minArr.forEach((minute) => {\r\n        const activity = getSlotColor(hourIndex, minute);\r\n        if (activity !== currentActivity) {\r\n          if (currentActivity !== null) {\r\n            // Push the current accumulated div\r\n            progressBars.push(\r\n              <div\r\n                key={`${hourIndex}-${minute}`}\r\n                className=\"progress-bar\"\r\n                role=\"progressbar\"\r\n                style={{\r\n                  width: `${currentActivityWidth}%`,\r\n                  backgroundColor: currentActivity,\r\n                }}\r\n                onMouseEnter={(event) => handleMouseEnter(event, hourIndex, minute)}\r\n                onClick={(event) => handleMouseClick(event, hourIndex, minute)}\r\n              >\r\n               {toolTipController ? <Tooltip title={toolTipTitle} arrow>\r\n                  <div\r\n                    style={{ padding: \"20px\", display: \"inline-block\" }}\r\n                  ></div>\r\n                </Tooltip> : null }\r\n              </div>\r\n            );\r\n          }\r\n          // Start a new activity block\r\n          currentActivity = activity;\r\n          currentActivityStart = minute;\r\n          currentActivityWidth = 1.04;\r\n        } else {\r\n          // Accumulate width for the same activity\r\n          currentActivityWidth += 1.04;\r\n        }\r\n      });\r\n    });\r\n\r\n    // Push the last accumulated div\r\n\r\n    if (currentActivity !== null) {\r\n      // console.log(\"Accumulated Cell\")\r\n      progressBars.push(\r\n        <div\r\n          key={`last-${currentActivityStart}`}\r\n          className=\"progress-bar\"\r\n          role=\"progressbar\"\r\n          style={{\r\n            width: `${currentActivityWidth}%`,\r\n            backgroundColor: currentActivity,\r\n          }}\r\n          onMouseEnter={(event) => handleMouseEnter(event, hours.length - 1, minArr.length - 1) }\r\n          // onMouseLeave={handleMouseLeave}\r\n        >\r\n           {toolTipController ? <Tooltip title={toolTipTitle} arrow >\r\n                  <div \r\n                    style={{ padding: \"20px\", display: \"inline-block\" }}\r\n                    ></div>\r\n                </Tooltip> : null }\r\n        </div>\r\n      );\r\n    }\r\n\r\n    return progressBars;\r\n  };\r\n\r\n  return (\r\n    <>\r\n    {addRequest ? (\r\n    <>\r\n        <TimelineRequest startTime={startTimeline} endTime={endTimeline} addRequest={addRequest} setAddRequest={handleClose} fromRequest={fromRequest} ></TimelineRequest>\r\n    </>\r\n  ) : null}\r\n      <h2 className=\"text-center\">User Activity Progress</h2>\r\n      <div className=\"d-flex justify-content-between\">\r\n        {hours.map((hour, index) => (\r\n          <div\r\n            key={index}\r\n            className=\"bar\"\r\n            style={{ width: \"3.9%\", textAlign: \"center\", height: \"60px\" }}\r\n          >\r\n            <div\r\n              className=\"slot\"\r\n              style={{ display: \"flex\", flexDirection: \"column\" }}\r\n            >\r\n              {minArrRev.map((minute, minIndex) => (\r\n                <div\r\n                  key={minute}\r\n                  style={{\r\n                    width: \"100%\",\r\n                    height: \"1px\",\r\n                    backgroundColor: getVerticalSlotColour(index, minute),\r\n                  }}\r\n                ></div>\r\n              ))}\r\n            </div>\r\n          </div>\r\n        ))}\r\n      </div>\r\n      <div style={{ marginBottom: \"10px\" }}>\r\n        <div className=\"progress\" style={{ height: \"10px\" }}>\r\n          {renderProgressBars()}\r\n        </div>\r\n      </div>\r\n      \r\n    </>\r\n  );\r\n};\r\n\r\nProductivityChart.propTypes = {\r\n  todayActivities: PropTypes.array,\r\n};\r\n\r\nexport default ProductivityChart;\r\n"], "mappings": ";;AAAA,OAAOA,SAAS,MAAM,YAAY;AAClC,OAAOC,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,OAAO,kBAAkB;AACzB,SAASC,WAAW,EAAEC,WAAW,QAAQ,aAAa;AACtD,SAASC,eAAe,QAAQ,yBAAyB;AACzD,SAASC,YAAY,QAAQ,WAAW;AACxC,SACEC,OAAO,QACF,eAAe;AACtB,OAAOC,eAAe,MAAM,mBAAmB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAGhD,MAAMC,iBAAiB,GAAGA,CAAC;EAAEC;AAAgB,CAAC,KAAK;EAAAC,EAAA;EAEjD,MAAMC,OAAO,GAAGZ,WAAW,CAACE,YAAY,CAACU,OAAO,CAAC,CAAC,CAAC;EACnD,IAAIC,MAAM,GAAG,CACX,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EACxE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAC1E,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAC1E,EAAE,CACH;EACD,IAAIC,SAAS,GAAG,CAAC,GAAGD,MAAM,CAAC,CAACE,OAAO,CAAC,CAAC;EAErC,MAAMC,KAAK,GAAGC,KAAK,CAACC,IAAI,CAAC;IAAEC,MAAM,EAAE;EAAG,CAAC,EAAE,CAACC,CAAC,EAAEC,CAAC,KAAK,GAAGA,CAAC,KAAK,CAAC;EAC7DL,KAAK,CAAC,EAAE,CAAC,GAAG,OAAO;EACnB,KAAK,IAAIK,CAAC,GAAG,EAAE,EAAEA,CAAC,GAAG,EAAE,EAAEA,CAAC,EAAE,EAAE;IAC5BL,KAAK,CAACK,CAAC,CAAC,GAAG,GAAGA,CAAC,GAAG,EAAE,KAAK;EAC3B;EACA,MAAMC,QAAQ,GAAGvB,WAAW,CAAC,CAAC;EAC9B,MAAM,CAACwB,SAAS,EAAEC,YAAY,CAAC,GAAG3B,QAAQ,CAAC,IAAI,CAAC;EAChD,MAAM,CAAC4B,OAAO,EAAEC,UAAU,CAAC,GAAG7B,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAAC8B,aAAa,EAACC,gBAAgB,CAAC,GAAG/B,QAAQ,CAAC,IAAI,CAAC;EACvD,MAAM,CAACgC,WAAW,EAACC,cAAc,CAAC,GAAGjC,QAAQ,CAAC,IAAI,CAAC;EACnD,MAAM,CAACkC,WAAW,EAAEC,cAAc,CAAC,GAAGnC,QAAQ,CAAC,IAAIoC,IAAI,CAAC,CAAC,CAAC;EAC1D,MAAM,CAACC,eAAe,EAAEC,cAAc,CAAC,GAAGtC,QAAQ,CAAC,IAAI,CAAC;EACxD,MAAM,CAACuC,YAAY,EAAEC,eAAe,CAAC,GAAGxC,QAAQ,CAAC,EAAE,CAAC;EACpD,MAAM,CAACyC,iBAAiB,EAACC,oBAAoB,CAAC,GAAG1C,QAAQ,CAAC,KAAK,CAAC;EAChE,MAAM,CAAC2C,UAAU,EAACC,aAAa,CAAC,GAAG5C,QAAQ,CAAC,KAAK,CAAC;EAClD,MAAM,CAAC6C,WAAW,EAACC,cAAc,CAAC,GAAG9C,QAAQ,CAAC,MAAM,CAAC;EAErD,MAAM+C,WAAW,GAAGA,CAAA,KAAMH,aAAa,CAAC,KAAK,CAAC;EAC9C3C,SAAS,CAAC,MAAM;IACd+C,OAAO,CAACC,GAAG,CAAC,2BAA2B,EAAEpC,eAAe,CAAC;IACzD,IAAIA,eAAe,CAACS,MAAM,GAAG,CAAC,EAAE;MAC9BK,YAAY,CAAC,IAAIS,IAAI,CAACvB,eAAe,CAAC,CAAC,CAAC,CAACqC,WAAW,CAAC,CAAC;MACtD,IAAIC,MAAM,CAACC,IAAI,CAACvC,eAAe,CAAC,CAAC,CAAC,CAAC,CAACwC,QAAQ,CAAC,cAAc,CAAC,EAAE;QAC5Df,cAAc,CAAC,KAAK,CAAC;QACrBT,UAAU,CAAC,IAAIO,IAAI,CAACvB,eAAe,CAAC,CAAC,CAAC,CAACyC,YAAY,CAAC,CAAC;MACvD;IACF;IAEA,MAAMC,QAAQ,GAAGC,WAAW,CAAC,MAAM;MACjC,IAAInB,eAAe,EAAE;QACnB;QACAW,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAClC,OAAO,CAAC;QACtCU,QAAQ,CAACrB,eAAe,CAACqD,eAAe,CAAC;UACvCC,EAAE,EAAC3C,OAAO,CAAC4C;QACf,CAAC,CAAC,CAAC;QACD,IAAI9C,eAAe,CAACS,MAAM,GAAG,CAAC,EAAE;UAC9B,IAAI6B,MAAM,CAACC,IAAI,CAACvC,eAAe,CAAC,CAAC,CAAC,CAAC,CAACwC,QAAQ,CAAC,cAAc,CAAC,EAAE;YAC5Df,cAAc,CAAC,KAAK,CAAC;YACrBT,UAAU,CAAC,IAAIO,IAAI,CAACvB,eAAe,CAAC,CAAC,CAAC,CAACyC,YAAY,CAAC,CAAC;UACvD,CAAC,MAAM;YACLnB,cAAc,CAAC,IAAIC,IAAI,CAAC,CAAC,CAAC;UAC5B;QACF;MACF;IACF,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC;;IAEX,OAAO,MAAMwB,aAAa,CAACL,QAAQ,CAAC;EACtC,CAAC,EAAE,CAAC1C,eAAe,EAAEE,OAAO,CAAC4C,GAAG,EAAElC,QAAQ,EAAEY,eAAe,CAAC,CAAC;EAE7D,MAAMwB,YAAY,GAAGA,CAACC,IAAI,EAAEC,MAAM,KAAK;IAAA,IAAAC,iBAAA,EAAAC,kBAAA,EAAAC,kBAAA;IACrC,MAAMC,QAAQ,GAAG,IAAI/B,IAAI,CAAC,IAAIA,IAAI,CAAC,CAAC,CAACgC,QAAQ,CAACN,IAAI,EAAEC,MAAM,EAAE,CAAC,CAAC,CAAC;IAE/D,IAAI,EAAAC,iBAAA,GAAAnD,eAAe,CAAC,CAAC,CAAC,cAAAmD,iBAAA,uBAAlBA,iBAAA,CAAoBK,aAAa,CAAC/C,MAAM,IAAG,CAAC,EAAE;MAChD,KAAK,IAAIE,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGX,eAAe,CAAC,CAAC,CAAC,CAACwD,aAAa,CAAC/C,MAAM,EAAEE,CAAC,EAAE,EAAE;QAChE,IAAI,EAAE,gBAAgB,IAAIX,eAAe,CAAC,CAAC,CAAC,CAACwD,aAAa,CAAC7C,CAAC,CAAC,CAAC,EAAE;UAAA,IAAA8C,kBAAA,EAAAC,kBAAA;UAE9D;UACA,IAAI,EAAAD,kBAAA,GAAAzD,eAAe,CAAC,CAAC,CAAC,cAAAyD,kBAAA,uBAAlBA,kBAAA,CAAoBE,WAAW,CAAClD,MAAM,IAAG,CAAC,EAAE;YAC9C,KAAK,IAAIE,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGX,eAAe,CAAC,CAAC,CAAC,CAAC2D,WAAW,CAAClD,MAAM,EAAEE,CAAC,EAAE,EAAE;cAC9D,IAAI,EAAE,eAAe,IAAIX,eAAe,CAAC,CAAC,CAAC,CAAC2D,WAAW,CAAChD,CAAC,CAAC,CAAC,EAAE;gBAC3D,IACE2C,QAAQ,IACN,IAAI/B,IAAI,CACNvB,eAAe,CAAC,CAAC,CAAC,CAAC2D,WAAW,CAAChD,CAAC,CAAC,CAACiD,eACpC,CAAC,IACHN,QAAQ,IAAIjC,WAAW,EACvB;kBACA,OAAO,QAAQ;gBACjB,CAAC,MAAM;kBACL,IACEiC,QAAQ,IAAIzC,SAAS,IACrBA,SAAS,KAAK,IAAI,IAClByC,QAAQ,IAAIjC,WAAW,EACvB;oBACA,OAAO,SAAS;kBAClB,CAAC,MAAM;oBACL,OAAO,WAAW;kBACpB;gBACF;cACF,CAAC,MAAM;gBACL,IACEiC,QAAQ,IACN,IAAI/B,IAAI,CACNvB,eAAe,CAAC,CAAC,CAAC,CAAC2D,WAAW,CAAChD,CAAC,CAAC,CAACiD,eACpC,CAAC,IACHN,QAAQ,IACN,IAAI/B,IAAI,CAACvB,eAAe,CAAC,CAAC,CAAC,CAAC2D,WAAW,CAAChD,CAAC,CAAC,CAACkD,aAAa,CAAC,EAC3D;kBACA,OAAO,QAAQ;gBACjB;cACF;YACF;UACF;;UAEA;UACA,IAAI,EAAAH,kBAAA,GAAA1D,eAAe,CAAC,CAAC,CAAC,cAAA0D,kBAAA,uBAAlBA,kBAAA,CAAoBI,sBAAsB,CAACrD,MAAM,IAAG,CAAC,EAAE;YACzD,KAAK,IAAIE,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGX,eAAe,CAAC,CAAC,CAAC,CAAC8D,sBAAsB,CAACrD,MAAM,EAAEE,CAAC,EAAE,EAAE;cACzE,IAAI,EAAE,aAAa,IAAIX,eAAe,CAAC,CAAC,CAAC,CAAC8D,sBAAsB,CAACnD,CAAC,CAAC,CAAC,EAAE;gBACpE,IACE2C,QAAQ,IACN,IAAI/B,IAAI,CACNvB,eAAe,CAAC,CAAC,CAAC,CAAC8D,sBAAsB,CAACnD,CAAC,CAAC,CAACM,aAC/C,CAAC,IACHqC,QAAQ,IAAIjC,WAAW,EACvB;kBACA,OAAO,MAAM;gBACf,CAAC,MAAM;kBACL,IACEiC,QAAQ,IAAIzC,SAAS,IACrBA,SAAS,KAAK,IAAI,IAClByC,QAAQ,IAAIjC,WAAW,EACvB;oBACA,OAAO,SAAS;kBAClB,CAAC,MAAM;oBACL,OAAO,WAAW;kBACpB;gBACF;cACF,CAAC,MAAM;gBACL,IACEiC,QAAQ,IACN,IAAI/B,IAAI,CACNvB,eAAe,CAAC,CAAC,CAAC,CAAC8D,sBAAsB,CAACnD,CAAC,CAAC,CAACM,aAC/C,CAAC,IACHqC,QAAQ,IACN,IAAI/B,IAAI,CAACvB,eAAe,CAAC,CAAC,CAAC,CAAC8D,sBAAsB,CAACnD,CAAC,CAAC,CAACQ,WAAW,CAAC,EACpE;kBACA,OAAO,MAAM;gBACf;cACF;YACF;UACF;;UAEA;UACA,IACEmC,QAAQ,IACN,IAAI/B,IAAI,CAACvB,eAAe,CAAC,CAAC,CAAC,CAACwD,aAAa,CAAC7C,CAAC,CAAC,CAACoD,gBAAgB,CAAC,IAChET,QAAQ,IAAIjC,WAAW,EACvB;YACA,OAAO,KAAK;UACd,CAAC,MAAM;YACL,IACEiC,QAAQ,IAAIzC,SAAS,IACrBA,SAAS,KAAK,IAAI,IAClByC,QAAQ,IAAIjC,WAAW,EACvB;cACA,OAAO,SAAS;YAClB,CAAC,MAAM;cACL,OAAO,WAAW;YACpB;UACF;QACF,CAAC,MAAM;UACL,IACEiC,QAAQ,IACN,IAAI/B,IAAI,CAACvB,eAAe,CAAC,CAAC,CAAC,CAACwD,aAAa,CAAC7C,CAAC,CAAC,CAACoD,gBAAgB,CAAC,IAChET,QAAQ,IACN,IAAI/B,IAAI,CAACvB,eAAe,CAAC,CAAC,CAAC,CAACwD,aAAa,CAAC7C,CAAC,CAAC,CAACqD,cAAc,CAAC,EAC9D;YACA,OAAO,KAAK;UACd;QACF;MACF;IACF;IAEA,IAAI,EAAAZ,kBAAA,GAAApD,eAAe,CAAC,CAAC,CAAC,cAAAoD,kBAAA,uBAAlBA,kBAAA,CAAoBO,WAAW,CAAClD,MAAM,IAAG,CAAC,EAAE;MAC9C,KAAK,IAAIE,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGX,eAAe,CAAC,CAAC,CAAC,CAAC2D,WAAW,CAAClD,MAAM,EAAEE,CAAC,EAAE,EAAE;QAC9D,IAAI,EAAE,eAAe,IAAIX,eAAe,CAAC,CAAC,CAAC,CAAC2D,WAAW,CAAChD,CAAC,CAAC,CAAC,EAAE;UAC3D,IACE2C,QAAQ,IACN,IAAI/B,IAAI,CAACvB,eAAe,CAAC,CAAC,CAAC,CAAC2D,WAAW,CAAChD,CAAC,CAAC,CAACiD,eAAe,CAAC,IAC7DN,QAAQ,IAAIjC,WAAW,EACvB;YACA,OAAO,QAAQ;UACjB,CAAC,MAAM;YACL,IACEiC,QAAQ,IAAIzC,SAAS,IACrBA,SAAS,KAAK,IAAI,IAClByC,QAAQ,IAAIjC,WAAW,EACvB;cACA,OAAO,SAAS;YAClB,CAAC,MAAM;cACL,OAAO,WAAW;YACpB;UACF;QACF,CAAC,MAAM;UACL,IACEiC,QAAQ,IACN,IAAI/B,IAAI,CAACvB,eAAe,CAAC,CAAC,CAAC,CAAC2D,WAAW,CAAChD,CAAC,CAAC,CAACiD,eAAe,CAAC,IAC7DN,QAAQ,IACN,IAAI/B,IAAI,CAACvB,eAAe,CAAC,CAAC,CAAC,CAAC2D,WAAW,CAAChD,CAAC,CAAC,CAACkD,aAAa,CAAC,EAC3D;YACA,OAAO,QAAQ;UACjB;QACF;MACF;IACF;IAEA,IAAG,EAAAR,kBAAA,GAAArD,eAAe,CAAC,CAAC,CAAC,cAAAqD,kBAAA,uBAAlBA,kBAAA,CAAoBS,sBAAsB,CAACrD,MAAM,IAAG,CAAC,EAAE;MACxD,KAAK,IAAIE,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGX,eAAe,CAAC,CAAC,CAAC,CAAC8D,sBAAsB,CAACrD,MAAM,EAAEE,CAAC,EAAE,EAAE;QACzE;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACE,IACE2C,QAAQ,IACN,IAAI/B,IAAI,CAACvB,eAAe,CAAC,CAAC,CAAC,CAAC8D,sBAAsB,CAACnD,CAAC,CAAC,CAACM,aAAa,CAAC,IACtEqC,QAAQ,IACN,IAAI/B,IAAI,CAACvB,eAAe,CAAC,CAAC,CAAC,CAAC8D,sBAAsB,CAACnD,CAAC,CAAC,CAACQ,WAAW,CAAC,EACpE;UACA,OAAO,MAAM;QACf;QACF;MACF;IACF;IAEA,IACEmC,QAAQ,IAAIzC,SAAS,IACrBA,SAAS,KAAK,IAAI,IAClByC,QAAQ,IAAIjC,WAAW,IACvBN,OAAO,KAAK,IAAI,EAChB;MACA,OAAO,SAAS;IAClB,CAAC,MAAM,IAAIuC,QAAQ,IAAIzC,SAAS,IAAIyC,QAAQ,IAAIvC,OAAO,EAAE;MACvD,OAAO,SAAS;IAClB,CAAC,MAAM;MACL,OAAO,WAAW;IACpB;EACF,CAAC;EAED,MAAMkD,qBAAqB,GAAGA,CAAChB,IAAI,EAAEC,MAAM,KAAK;IAE9C,IAAIrC,SAAS,KAAK,IAAI,EAAE;MACtB,IAAIoC,IAAI,IAAIpC,SAAS,CAACqD,QAAQ,CAAC,CAAC,IAAIjB,IAAI,IAAI5B,WAAW,CAAC6C,QAAQ,CAAC,CAAC,EAAE;QAAA,IAAAC,kBAAA;QAElE,IAAInE,eAAe,IAAI,EAAAmE,kBAAA,GAAAnE,eAAe,CAAC,CAAC,CAAC,cAAAmE,kBAAA,uBAAlBA,kBAAA,CAAoBC,mBAAmB,CAAC3D,MAAM,IAAG,CAAC,EAAE;UACzE,KACE,IAAIE,CAAC,GAAG,CAAC,EACTA,CAAC,GAAGX,eAAe,CAAC,CAAC,CAAC,CAACoE,mBAAmB,CAAC3D,MAAM,EACjDE,CAAC,EAAE,EACH;YACA,IAAI0D,SAAS,GACXrE,eAAe,CAAC,CAAC,CAAC,CAACoE,mBAAmB,CAACzD,CAAC,CAAC,CAAC2D,SAAS,CAACC,KAAK,CAAC,GAAG,CAAC;YAEhE,IAAIC,MAAM,CAACH,SAAS,CAAC,CAAC,CAAC,CAAC,KAAKpB,IAAI,EAAE;cAAA,IAAAwB,qBAAA,EAAAC,sBAAA;cACjC,IACExB,MAAM,MAAAuB,qBAAA,GACNzE,eAAe,CAAC,CAAC,CAAC,CAACoE,mBAAmB,CAACzD,CAAC,CAAC,cAAA8D,qBAAA,uBAAzCA,qBAAA,CAA2CE,kBAAkB,GAC7D;gBACA,OAAO,SAAS;cAClB,CAAC,MAAM,IACLzB,MAAM,MAAAwB,sBAAA,GACN1E,eAAe,CAAC,CAAC,CAAC,CAACoE,mBAAmB,CAACzD,CAAC,CAAC,cAAA+D,sBAAA,uBAAzCA,sBAAA,CAA2CE,eAAe,GAC1D;gBACA,OAAO,SAAS;cAClB,CAAC,MAAM;gBACL,OAAO,WAAW;cACpB;YACF;UACF;UAEA,OAAO,UAAU;QACnB,CAAC,MAAM;UACL,OAAO,WAAW;QACpB;MACF,CAAC,MAAM;QACL,OAAO,WAAW;MACpB;IACF,CAAC,MAAM;MACL,OAAO,WAAW;IACpB;EACF,CAAC;EAED,MAAMC,YAAY,GAAIC,GAAG,IAAK;IAC5B,MAAMC,MAAM,GAAGD,GAAG,CAACE,KAAK,CAAC,MAAM,CAAC;IAChC,OAAOD,MAAM,GAAG,OAAOA,MAAM,CAAC,CAAC,CAAC,IAAIA,MAAM,CAAC,CAAC,CAAC,IAAIA,MAAM,CAAC,CAAC,CAAC,GAAG,GAAGD,GAAG;EACrE,CAAC;EAED,MAAMG,UAAU,GAAGA,CAACpE,SAAS,EAAEE,OAAO,KAAK;IACzC,MAAMmE,YAAY,GAAG,IAAI3D,IAAI,CAACV,SAAS,CAAC;IACxC,MAAMsE,UAAU,GAAG,IAAI5D,IAAI,CAACR,OAAO,CAAC;IACpC,IAAIgE,MAAM,GAAG,CAACI,UAAU,GAAGD,YAAY,IAAI,KAAK;IAChD,OAAOH,MAAM,GAAG,EAAE,GAAG,GAAGK,IAAI,CAACC,KAAK,CAACN,MAAM,CAAC,GAAG,GAAG,GAAGK,IAAI,CAACC,KAAK,CAACN,MAAM,GAAG,EAAE,CAAC,KAAKK,IAAI,CAACC,KAAK,CAACN,MAAM,GAAG,EAAE,CAAC,IAAI;EAC5G,CAAC;EAED,MAAMO,gBAAgB,GAAGA,CAACC,KAAK,EAAEtC,IAAI,EAAEC,MAAM,KAAK;IAChD,MAAMsC,QAAQ,GAAGC,gBAAgB,CAACF,KAAK,CAACG,aAAa,CAAC,CAACC,eAAe;IACtExD,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAACoD,QAAQ,CAAC;IAE5C,QAAQX,YAAY,CAACW,QAAQ,CAAC;MAC5B,KAAK,gBAAgB;QAAE;UACrB3D,oBAAoB,CAAC,IAAI,CAAC;UAC1B,MAAM+D,YAAY,GAAG,IAAIrE,IAAI,CAAC,IAAIA,IAAI,CAAC,CAAC,CAACgC,QAAQ,CAACN,IAAI,EAAEC,MAAM,GAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;UACxE,IAAI2C,SAAS,GAAG,KAAK;UACrB,KAAK,IAAIlF,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGX,eAAe,CAAC,CAAC,CAAC,CAAC2D,WAAW,CAAClD,MAAM,EAAEE,CAAC,EAAE,EAAE;YAC9D,MAAMmF,KAAK,GAAG,IAAIvE,IAAI,CAACvB,eAAe,CAAC,CAAC,CAAC,CAAC2D,WAAW,CAAChD,CAAC,CAAC,CAACiD,eAAe,CAAC;YACzE,MAAMmC,GAAG,GAAG,IAAIxE,IAAI,CAACvB,eAAe,CAAC,CAAC,CAAC,CAAC2D,WAAW,CAAChD,CAAC,CAAC,CAACkD,aAAa,CAAC;YACrE,IAAIiC,KAAK,IAAIF,YAAY,IAAIG,GAAG,IAAIH,YAAY,EAAE;cAChD,MAAMI,GAAG,GAAGf,UAAU,CAACa,KAAK,EAAEC,GAAG,CAAC;cAClCpE,eAAe,CAAC,QAAQqE,GAAG,IAAIF,KAAK,CAAC5B,QAAQ,CAAC,CAAC,IAAI4B,KAAK,CAACG,UAAU,CAAC,CAAC,IAAIH,KAAK,CAACI,UAAU,CAAC,CAAC,MAAMH,GAAG,CAAC7B,QAAQ,CAAC,CAAC,IAAI6B,GAAG,CAACE,UAAU,CAAC,CAAC,IAAIF,GAAG,CAACG,UAAU,CAAC,CAAC,EAAE,CAAC;cAC1J/D,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAACN,UAAU,CAAC;cAE9C+D,SAAS,GAAG,IAAI;cAChB;YACF;UACF;UACA,IAAI,CAACA,SAAS,EAAE;YACd1D,OAAO,CAACC,GAAG,CAAC,gBAAgB,CAAC;YAC7BT,eAAe,CAAC,MAAM,CAAC;UACzB;UACA;QACF;MACA,KAAK,gBAAgB;QAAE;UACrBE,oBAAoB,CAAC,IAAI,CAAC;UAC1B,MAAM+D,YAAY,GAAG,IAAIrE,IAAI,CAC3B,IAAIA,IAAI,CAAC,CAAC,CAACgC,QAAQ,CAACN,IAAI,EAAEC,MAAM,GAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAC1C,CAAC;UACD,IAAIiD,SAAS,GAAG,KAAK;UACrB,KAAK,IAAIxF,CAAC,GAAC,CAAC,EAAEA,CAAC,GAACX,eAAe,CAAC,CAAC,CAAC,CAACoG,wBAAwB,CAAC3F,MAAM,EAAEE,CAAC,EAAE,EAAE;YACvE,MAAMmF,KAAK,GAAG,IAAIvE,IAAI,CACpBvB,eAAe,CAAC,CAAC,CAAC,CAACoG,wBAAwB,CAACzF,CAAC,CAAC,CAAC0F,aACjD,CAAC;YACD,IAAIN,GAAG,GAAG,IAAI;YACd,IAAI/F,eAAe,CAAC,CAAC,CAAC,CAACoG,wBAAwB,CAACzF,CAAC,CAAC,CAAC2F,WAAW,EAAE;cAC9DP,GAAG,GAAG,IAAIxE,IAAI,CAACvB,eAAe,CAAC,CAAC,CAAC,CAACoG,wBAAwB,CAACzF,CAAC,CAAC,CAAC2F,WAAW,CAAC;YAC5E,CAAC,MAAM;cACLP,GAAG,GAAG,IAAIxE,IAAI,CAAC,CAAC;YAClB;YAEA,IAAIuE,KAAK,IAAIF,YAAY,IAAIG,GAAG,IAAIH,YAAY,EAAE;cAChD;cACA,MAAMI,GAAG,GAAGf,UAAU,CAACa,KAAK,EAAEC,GAAG,CAAC;cAClCpE,eAAe,CAAC,QAAQqE,GAAG,IAAIF,KAAK,CAAC5B,QAAQ,CAAC,CAAC,IAAI4B,KAAK,CAACG,UAAU,CAAC,CAAC,IAAIH,KAAK,CAACI,UAAU,CAAC,CAAC,MAAMH,GAAG,CAAC7B,QAAQ,CAAC,CAAC,IAAI6B,GAAG,CAACE,UAAU,CAAC,CAAC,IAAIF,GAAG,CAACG,UAAU,CAAC,CAAC,EAAE,CAAC;cAC1JC,SAAS,GAAG,IAAI;cAChB;YACF;UACF;UACA,IAAI,CAACA,SAAS,EAAE;YACdxE,eAAe,CAAC,MAAM,CAAC;UACzB;UACA;QACF;MACA,KAAK,cAAc;QAAE;UACnBE,oBAAoB,CAAC,IAAI,CAAC;UAC1B,MAAM+D,YAAY,GAAG,IAAIrE,IAAI,CAAC,IAAIA,IAAI,CAAC,CAAC,CAACgC,QAAQ,CAACN,IAAI,EAAEC,MAAM,GAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;UACxE,IAAIqD,UAAU,GAAG,KAAK;UACtB,KAAK,IAAI5F,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGX,eAAe,CAAC,CAAC,CAAC,CAACwD,aAAa,CAAC/C,MAAM,EAAEE,CAAC,EAAE,EAAE;YAChE,MAAMmF,KAAK,GAAG,IAAIvE,IAAI,CAACvB,eAAe,CAAC,CAAC,CAAC,CAACwD,aAAa,CAAC7C,CAAC,CAAC,CAACoD,gBAAgB,CAAC;YAC5E,IAAIgC,GAAG,GAAG,IAAI;YACd,IAAI/F,eAAe,CAAC,CAAC,CAAC,CAACwD,aAAa,CAAC7C,CAAC,CAAC,CAACqD,cAAc,EAAE;cACtD+B,GAAG,GAAG,IAAIxE,IAAI,CAACvB,eAAe,CAAC,CAAC,CAAC,CAACwD,aAAa,CAAC7C,CAAC,CAAC,CAACqD,cAAc,CAAC;YACpE,CAAC,MAAM;cACL+B,GAAG,GAAG,IAAIxE,IAAI,CAAC,CAAC;YAClB;YAEA,IAAIuE,KAAK,IAAIF,YAAY,IAAIG,GAAG,IAAIH,YAAY,EAAE;cAEhD,MAAMI,GAAG,GAAGf,UAAU,CAACa,KAAK,EAAEC,GAAG,CAAC;cAClCpE,eAAe,CAAC,SAASqE,GAAG,IAAIF,KAAK,CAAC5B,QAAQ,CAAC,CAAC,IAAI4B,KAAK,CAACG,UAAU,CAAC,CAAC,IAAIH,KAAK,CAACI,UAAU,CAAC,CAAC,MAAMH,GAAG,CAAC7B,QAAQ,CAAC,CAAC,IAAI6B,GAAG,CAACE,UAAU,CAAC,CAAC,IAAIF,GAAG,CAACG,UAAU,CAAC,CAAC,EAAE,CAAC;cAC3J;cACA/D,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAACN,UAAU,CAAC;cAC9C;cACA;cACAyE,UAAU,GAAG,IAAI;cACjB;YACF;UACF;UACA,IAAI,CAACA,UAAU,EAAE;YACfpE,OAAO,CAACC,GAAG,CAAC,SAAS,CAAC;YACtBT,eAAe,CAAC,OAAO,CAAC;UAC1B;UACA;QACF;MAEA,KAAK,cAAc;QAAE;UACnBQ,OAAO,CAACC,GAAG,CAAC,MAAM,CAAC;UACnBP,oBAAoB,CAAC,IAAI,CAAC;UAC1B,MAAM+D,YAAY,GAAG,IAAIrE,IAAI,CAAC,IAAIA,IAAI,CAAC,CAAC,CAACgC,QAAQ,CAACN,IAAI,EAAEC,MAAM,GAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;UACxE,IAAIsD,aAAa,GAAG,KAAK;UACzB,IAAIC,MAAM,GAAG,UAAU;UACvB,KAAK,IAAI9F,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGX,eAAe,CAAC,CAAC,CAAC,CAAC8D,sBAAsB,CAACrD,MAAM,EAAEE,CAAC,EAAE,EAAE;YACzE,MAAMmF,KAAK,GAAG,IAAIvE,IAAI,CAACvB,eAAe,CAAC,CAAC,CAAC,CAAC8D,sBAAsB,CAACnD,CAAC,CAAC,CAACM,aAAa,CAAC;YAClF,IAAI8E,GAAG,GAAG,IAAI;YACd,IAAI/F,eAAe,CAAC,CAAC,CAAC,CAAC8D,sBAAsB,CAACnD,CAAC,CAAC,CAACQ,WAAW,EAAE;cAC5D4E,GAAG,GAAG,IAAIxE,IAAI,CAACvB,eAAe,CAAC,CAAC,CAAC,CAAC8D,sBAAsB,CAACnD,CAAC,CAAC,CAACQ,WAAW,CAAC;YAC1E,CAAC,MAAM;cACL4E,GAAG,GAAG,IAAIxE,IAAI,CAAC,CAAC;YAClB;YAEA,IAAIuE,KAAK,IAAIF,YAAY,IAAIG,GAAG,IAAIH,YAAY,EAAE;cAEhD,MAAMI,GAAG,GAAGf,UAAU,CAACa,KAAK,EAAEC,GAAG,CAAC;cAClCpE,eAAe,CAAC,YAAYqE,GAAG,IAAIF,KAAK,CAAC5B,QAAQ,CAAC,CAAC,IAAI4B,KAAK,CAACG,UAAU,CAAC,CAAC,IAAIH,KAAK,CAACI,UAAU,CAAC,CAAC,MAAMH,GAAG,CAAC7B,QAAQ,CAAC,CAAC,IAAI6B,GAAG,CAACE,UAAU,CAAC,CAAC,IAAIF,GAAG,CAACG,UAAU,CAAC,CAAC,YAAYO,MAAM,EAAE,CAAC;cAEhLtE,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAACN,UAAU,CAAC;cAC9C0E,aAAa,GAAG,IAAI;cACpB;YACF;UACF;UACA,IAAI,CAACA,aAAa,EAAE;YAClBrE,OAAO,CAACC,GAAG,CAAC,YAAY,CAAC;YACzBT,eAAe,CAAC,SAAS,CAAC;UAC5B;UACA;QACF;MAEA;QAAS;UACP;UACAE,oBAAoB,CAAC,KAAK,CAAC;UAE3B;QACF;IACF;EACF,CAAC;EAED,MAAM6E,gBAAgB,GAAGA,CAACnB,KAAK,EAAEtC,IAAI,EAAEC,MAAM,KAAK;IAChD,MAAMsC,QAAQ,GAAGC,gBAAgB,CAACF,KAAK,CAACG,aAAa,CAAC,CAACC,eAAe;IAEtE,QAAQd,YAAY,CAACW,QAAQ,CAAC;MAC5B,KAAK,gBAAgB;QAAE;UACrB,MAAMI,YAAY,GAAG,IAAIrE,IAAI,CAAC,IAAIA,IAAI,CAAC,CAAC,CAACgC,QAAQ,CAACN,IAAI,EAAEC,MAAM,GAAG,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;UACxE,KAAK,IAAIvC,CAAC,GAAG,CAAC,EAAEA,CAAC,KAAAgG,kBAAA,GAAG3G,eAAe,CAAC,CAAC,CAAC,cAAA2G,kBAAA,uBAAlBA,kBAAA,CAAoBhD,WAAW,CAAClD,MAAM,GAAEE,CAAC,EAAE,EAAE;YAAA,IAAAgG,kBAAA,EAAAC,kBAAA,EAAAC,kBAAA;YAC/D;YACA,MAAMf,KAAK,GAAG,IAAIvE,IAAI,EAAAqF,kBAAA,GAAC5G,eAAe,CAAC,CAAC,CAAC,cAAA4G,kBAAA,uBAAlBA,kBAAA,CAAoBjD,WAAW,CAAChD,CAAC,CAAC,CAACiD,eAAe,CAAC;YAC1E,MAAMmC,GAAG,GAAG,IAAIxE,IAAI,EAAAsF,kBAAA,GAAC7G,eAAe,CAAC,CAAC,CAAC,cAAA6G,kBAAA,uBAAlBA,kBAAA,CAAoBlD,WAAW,CAAChD,CAAC,CAAC,CAACkD,aAAa,CAAC;YAEtE,IAAIiC,KAAK,IAAIF,YAAY,IAAIG,GAAG,IAAIH,YAAY,EAAE;cAChDzD,OAAO,CAACC,GAAG,CAAC,YAAY,EAAC0D,KAAK,CAAC5B,QAAQ,CAAC,CAAC,CAAC;cAC1CjC,cAAc,CAAC,MAAM,CAAC;cACtBf,gBAAgB,CAAC4E,KAAK,CAAC;cACvB1E,cAAc,CAAC2E,GAAG,CAAC;cACnBhE,aAAa,CAAC,IAAI,CAAC;cACnB;YACF;UACF;UAEF;QACF;MAEA,KAAK,cAAc;QAAE;UACnB,MAAM6D,YAAY,GAAG,IAAIrE,IAAI,CAAC,IAAIA,IAAI,CAAC,CAAC,CAACgC,QAAQ,CAACN,IAAI,EAAEC,MAAM,GAAG,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;UAC1E,KAAK,IAAIvC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGX,eAAe,CAAC,CAAC,CAAC,CAACwD,aAAa,CAAC/C,MAAM,EAAEE,CAAC,EAAE,EAAE;YAChE,MAAMmF,KAAK,GAAG,IAAIvE,IAAI,CAACvB,eAAe,CAAC,CAAC,CAAC,CAACwD,aAAa,CAAC7C,CAAC,CAAC,CAACoD,gBAAgB,CAAC;YAC5E,IAAIgC,GAAG,GAAG,IAAI;YAEd,IAAI/F,eAAe,CAAC,CAAC,CAAC,CAACwD,aAAa,CAAC7C,CAAC,CAAC,CAACqD,cAAc,EAAE;cACtD+B,GAAG,GAAG,IAAIxE,IAAI,CAACvB,eAAe,CAAC,CAAC,CAAC,CAACwD,aAAa,CAAC7C,CAAC,CAAC,CAACqD,cAAc,CAAC;YACpE,CAAC,MAAM;cACL+B,GAAG,GAAG,IAAIxE,IAAI,CAAC,CAAC;YAClB;YAEA,IAAIuE,KAAK,IAAIF,YAAY,IAAIG,GAAG,IAAIH,YAAY,EAAE;cAChD3D,cAAc,CAAC,SAAS,CAAC;cACzBE,OAAO,CAACC,GAAG,CAAC,eAAe,EAACpC,eAAe,CAAC,CAAC,CAAC,CAACwD,aAAa,CAAC7C,CAAC,CAAC,CAACoD,gBAAgB,CAAC;cACjF5B,OAAO,CAACC,GAAG,CAAC,aAAa,EAACpC,eAAe,CAAC,CAAC,CAAC,CAACwD,aAAa,CAAC7C,CAAC,CAAC,CAACqD,cAAc,CAAC;cAC7E9C,gBAAgB,CAAC4E,KAAK,CAAC;cACvB1E,cAAc,CAAC2E,GAAG,CAAC;cACnBhE,aAAa,CAAC,IAAI,CAAC;cACnB;YACF;UACF;UACA;QACF;MAEA;QAAUA,aAAa,CAAC,KAAK,CAAC;QAC5B;IACJ;EAEF,CAAC;EAGD,MAAM+E,kBAAkB,GAAGA,CAAA,KAAM;IAC/B,MAAMC,YAAY,GAAG,EAAE;IACvB,IAAIC,eAAe,GAAG,IAAI;IAC1B,IAAIC,oBAAoB,GAAG,CAAC;IAC5B,IAAIC,oBAAoB,GAAG,CAAC;IAE5B5G,KAAK,CAAC6G,OAAO,CAAC,CAAClE,IAAI,EAAEmE,SAAS,KAAK;MACjCjH,MAAM,CAACgH,OAAO,CAAEjE,MAAM,IAAK;QACzB,MAAMmE,QAAQ,GAAGrE,YAAY,CAACoE,SAAS,EAAElE,MAAM,CAAC;QAChD,IAAImE,QAAQ,KAAKL,eAAe,EAAE;UAChC,IAAIA,eAAe,KAAK,IAAI,EAAE;YAC5B;YACAD,YAAY,CAACO,IAAI,cACf1H,OAAA;cAEE2H,SAAS,EAAC,cAAc;cACxBC,IAAI,EAAC,aAAa;cAClBC,KAAK,EAAE;gBACLC,KAAK,EAAE,GAAGR,oBAAoB,GAAG;gBACjCvB,eAAe,EAAEqB;cACnB,CAAE;cACFW,YAAY,EAAGpC,KAAK,IAAKD,gBAAgB,CAACC,KAAK,EAAE6B,SAAS,EAAElE,MAAM,CAAE;cACpE0E,OAAO,EAAGrC,KAAK,IAAKmB,gBAAgB,CAACnB,KAAK,EAAE6B,SAAS,EAAElE,MAAM,CAAE;cAAA2E,QAAA,EAE/DjG,iBAAiB,gBAAGhC,OAAA,CAACH,OAAO;gBAACqI,KAAK,EAAEpG,YAAa;gBAACqG,KAAK;gBAAAF,QAAA,eACrDjI,OAAA;kBACE6H,KAAK,EAAE;oBAAEO,OAAO,EAAE,MAAM;oBAAEC,OAAO,EAAE;kBAAe;gBAAE;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAChD;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACA,CAAC,GAAG;YAAI,GAdZ,GAAGjB,SAAS,IAAIlE,MAAM,EAAE;cAAAgF,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAe1B,CACP,CAAC;UACH;UACA;UACArB,eAAe,GAAGK,QAAQ;UAC1BJ,oBAAoB,GAAG/D,MAAM;UAC7BgE,oBAAoB,GAAG,IAAI;QAC7B,CAAC,MAAM;UACL;UACAA,oBAAoB,IAAI,IAAI;QAC9B;MACF,CAAC,CAAC;IACJ,CAAC,CAAC;;IAEF;;IAEA,IAAIF,eAAe,KAAK,IAAI,EAAE;MAC5B;MACAD,YAAY,CAACO,IAAI,cACf1H,OAAA;QAEE2H,SAAS,EAAC,cAAc;QACxBC,IAAI,EAAC,aAAa;QAClBC,KAAK,EAAE;UACLC,KAAK,EAAE,GAAGR,oBAAoB,GAAG;UACjCvB,eAAe,EAAEqB;QACnB,CAAE;QACFW,YAAY,EAAGpC,KAAK,IAAKD,gBAAgB,CAACC,KAAK,EAAEjF,KAAK,CAACG,MAAM,GAAG,CAAC,EAAEN,MAAM,CAACM,MAAM,GAAG,CAAC;QACpF;QAAA;QAAAoH,QAAA,EAEEjG,iBAAiB,gBAAGhC,OAAA,CAACH,OAAO;UAACqI,KAAK,EAAEpG,YAAa;UAACqG,KAAK;UAAAF,QAAA,eACjDjI,OAAA;YACE6H,KAAK,EAAE;cAAEO,OAAO,EAAE,MAAM;cAAEC,OAAO,EAAE;YAAe;UAAE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9C;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC,GAAG;MAAI,GAdlB,QAAQpB,oBAAoB,EAAE;QAAAiB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAehC,CACP,CAAC;IACH;IAEA,OAAOtB,YAAY;EACrB,CAAC;EAED,oBACEnH,OAAA,CAAAE,SAAA;IAAA+H,QAAA,GACC/F,UAAU,gBACXlC,OAAA,CAAAE,SAAA;MAAA+H,QAAA,eACIjI,OAAA,CAACF,eAAe;QAACmB,SAAS,EAAEI,aAAc;QAACF,OAAO,EAAEI,WAAY;QAACW,UAAU,EAAEA,UAAW;QAACC,aAAa,EAAEG,WAAY;QAACF,WAAW,EAAEA;MAAY;QAAAkG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAmB;IAAC,gBACpK,CAAC,GACD,IAAI,eACJzI,OAAA;MAAI2H,SAAS,EAAC,aAAa;MAAAM,QAAA,EAAC;IAAsB;MAAAK,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC,eACvDzI,OAAA;MAAK2H,SAAS,EAAC,gCAAgC;MAAAM,QAAA,EAC5CvH,KAAK,CAACgI,GAAG,CAAC,CAACrF,IAAI,EAAEsF,KAAK,kBACrB3I,OAAA;QAEE2H,SAAS,EAAC,KAAK;QACfE,KAAK,EAAE;UAAEC,KAAK,EAAE,MAAM;UAAEc,SAAS,EAAE,QAAQ;UAAEC,MAAM,EAAE;QAAO,CAAE;QAAAZ,QAAA,eAE9DjI,OAAA;UACE2H,SAAS,EAAC,MAAM;UAChBE,KAAK,EAAE;YAAEQ,OAAO,EAAE,MAAM;YAAES,aAAa,EAAE;UAAS,CAAE;UAAAb,QAAA,EAEnDzH,SAAS,CAACkI,GAAG,CAAC,CAACpF,MAAM,EAAEyF,QAAQ,kBAC9B/I,OAAA;YAEE6H,KAAK,EAAE;cACLC,KAAK,EAAE,MAAM;cACbe,MAAM,EAAE,KAAK;cACb9C,eAAe,EAAE1B,qBAAqB,CAACsE,KAAK,EAAErF,MAAM;YACtD;UAAE,GALGA,MAAM;YAAAgF,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAMP,CACP;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC;MAAC,GAlBDE,KAAK;QAAAL,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAmBP,CACN;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eACNzI,OAAA;MAAK6H,KAAK,EAAE;QAAEmB,YAAY,EAAE;MAAO,CAAE;MAAAf,QAAA,eACnCjI,OAAA;QAAK2H,SAAS,EAAC,UAAU;QAACE,KAAK,EAAE;UAAEgB,MAAM,EAAE;QAAO,CAAE;QAAAZ,QAAA,EACjDf,kBAAkB,CAAC;MAAC;QAAAoB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAClB;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA,eAEN,CAAC;AAEP,CAAC;AAACpI,EAAA,CA/lBIF,iBAAiB;EAAA,QAELT,WAAW,EAcVD,WAAW;AAAA;AAAAwJ,EAAA,GAhBxB9I,iBAAiB;AAimBvBA,iBAAiB,CAAC+I,SAAS,GAAG;EAC5B9I,eAAe,EAAEf,SAAS,CAAC8J;AAC7B,CAAC;AAED,eAAehJ,iBAAiB;AAAC,IAAA8I,EAAA;AAAAG,YAAA,CAAAH,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}