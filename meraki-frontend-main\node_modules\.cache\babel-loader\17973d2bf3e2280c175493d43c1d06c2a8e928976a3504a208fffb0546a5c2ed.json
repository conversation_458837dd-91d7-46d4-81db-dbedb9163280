{"ast": null, "code": "import { createSlice } from \"@reduxjs/toolkit\";\nexport const ActivitySlice = createSlice({\n  name: \"Activity\",\n  initialState: {\n    activityArr: []\n  },\n  reducers: {\n    createTodayGoal: () => {},\n    createTodayStatus: () => {},\n    getUserActivitySuccessfull: (state, action) => {\n      if (action.payload.length === 0) {\n        state.activityArr = [];\n        // console.log(\"1 REDUCER ACTIVITY LOG \",action.payload)\n      } else {\n        state.activityArr = action.payload;\n        console.log(\"2 REDUCER ACTIVITY LOG \", action.payload);\n      }\n    },\n    getUserActivity: () => {},\n    checkOutStatusUpdate: () => {},\n    breakStartRed: () => {},\n    breakEndRed: () => {},\n    lateCheckIn: () => {},\n    earlyCheckOut: () => {},\n    idelStartRed: () => {},\n    idelEndRed: () => {},\n    productivityStatusRed: () => {},\n    overLimitBreakRed: () => {},\n    eraseActivity: (state = []) => {\n      state.activityArr = [];\n    },\n    createTimelineRequest: () => {},\n    updateTimelineRequest: () => {},\n    getTimelineRequests: () => {}\n  }\n});\nexport default ActivitySlice;", "map": {"version": 3, "names": ["createSlice", "ActivitySlice", "name", "initialState", "activityArr", "reducers", "createTodayGoal", "createTodayStatus", "getUserActivitySuccessfull", "state", "action", "payload", "length", "console", "log", "getUserActivity", "checkOutStatusUpdate", "breakStartRed", "breakEndRed", "lateCheckIn", "earlyCheckOut", "idelStartRed", "idelEndRed", "productivityStatusRed", "overLimitBreakRed", "eraseActivity", "createTimelineRequest", "updateTimelineRequest", "getTimelineRequests"], "sources": ["E:/Suraj Patil/Organization_Management_System/meraki-frontend-main/src/slices/slice/ActivitySlice.js"], "sourcesContent": ["import { createSlice } from \"@reduxjs/toolkit\";\r\n\r\n\r\nexport const ActivitySlice = createSlice({\r\n    name: \"Activity\",\r\n    initialState: {\r\n        activityArr: [],\r\n\r\n    },\r\n    reducers: {\r\n        createTodayGoal: () => { \r\n        },\r\n        createTodayStatus: () => {\r\n        },\r\n        getUserActivitySuccessfull: (state,action) => {\r\n            \r\n            if(action.payload.length === 0) {\r\n                state.activityArr = []\r\n                // console.log(\"1 REDUCER ACTIVITY LOG \",action.payload)\r\n            }else {\r\n                state.activityArr = action.payload\r\n                console.log(\"2 REDUCER ACTIVITY LOG \",action.payload)\r\n            }\r\n\r\n        },\r\n        getUserActivity: () => {\r\n        },\r\n        checkOutStatusUpdate: () => {},\r\n        breakStartRed: () => {},\r\n        breakEndRed: () => {},\r\n        lateCheckIn: () => {},\r\n        earlyCheckOut: () => {},\r\n        idelStartRed: () => {},\r\n        idelEndRed: () => {},\r\n        productivityStatusRed: () => {},\r\n        overLimitBreakRed: () => {},\r\n        eraseActivity: (state = []) => {\r\n            state.activityArr = []\r\n        },\r\n        createTimelineRequest: () => {},\r\n        updateTimelineRequest: () => {},\r\n        getTimelineRequests: () => {}\r\n    }\r\n});\r\n\r\nexport default ActivitySlice;"], "mappings": "AAAA,SAASA,WAAW,QAAQ,kBAAkB;AAG9C,OAAO,MAAMC,aAAa,GAAGD,WAAW,CAAC;EACrCE,IAAI,EAAE,UAAU;EAChBC,YAAY,EAAE;IACVC,WAAW,EAAE;EAEjB,CAAC;EACDC,QAAQ,EAAE;IACNC,eAAe,EAAEA,CAAA,KAAM,CACvB,CAAC;IACDC,iBAAiB,EAAEA,CAAA,KAAM,CACzB,CAAC;IACDC,0BAA0B,EAAEA,CAACC,KAAK,EAACC,MAAM,KAAK;MAE1C,IAAGA,MAAM,CAACC,OAAO,CAACC,MAAM,KAAK,CAAC,EAAE;QAC5BH,KAAK,CAACL,WAAW,GAAG,EAAE;QACtB;MACJ,CAAC,MAAK;QACFK,KAAK,CAACL,WAAW,GAAGM,MAAM,CAACC,OAAO;QAClCE,OAAO,CAACC,GAAG,CAAC,yBAAyB,EAACJ,MAAM,CAACC,OAAO,CAAC;MACzD;IAEJ,CAAC;IACDI,eAAe,EAAEA,CAAA,KAAM,CACvB,CAAC;IACDC,oBAAoB,EAAEA,CAAA,KAAM,CAAC,CAAC;IAC9BC,aAAa,EAAEA,CAAA,KAAM,CAAC,CAAC;IACvBC,WAAW,EAAEA,CAAA,KAAM,CAAC,CAAC;IACrBC,WAAW,EAAEA,CAAA,KAAM,CAAC,CAAC;IACrBC,aAAa,EAAEA,CAAA,KAAM,CAAC,CAAC;IACvBC,YAAY,EAAEA,CAAA,KAAM,CAAC,CAAC;IACtBC,UAAU,EAAEA,CAAA,KAAM,CAAC,CAAC;IACpBC,qBAAqB,EAAEA,CAAA,KAAM,CAAC,CAAC;IAC/BC,iBAAiB,EAAEA,CAAA,KAAM,CAAC,CAAC;IAC3BC,aAAa,EAAEA,CAAChB,KAAK,GAAG,EAAE,KAAK;MAC3BA,KAAK,CAACL,WAAW,GAAG,EAAE;IAC1B,CAAC;IACDsB,qBAAqB,EAAEA,CAAA,KAAM,CAAC,CAAC;IAC/BC,qBAAqB,EAAEA,CAAA,KAAM,CAAC,CAAC;IAC/BC,mBAAmB,EAAEA,CAAA,KAAM,CAAC;EAChC;AACJ,CAAC,CAAC;AAEF,eAAe3B,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}