{"ast": null, "code": "import { ActivityService } from \"services/ActivityService\";\nimport { ActivityActions, GeneralActions } from \"../slices/actions\";\nimport { all, call, put, takeLatest } from 'redux-saga/effects';\nfunction* createTodayGoal({\n  type,\n  payload\n}) {\n  try {\n    yield put(GeneralActions.removeError(type));\n    yield put(GeneralActions.startLoading(type));\n    console.warn(\"Create Today Goal\", payload);\n    yield call(ActivityService.createTodayGoal, payload);\n    const resultHis = yield call(ActivityService.getUserActivityHistory, payload.id);\n    const data = yield resultHis.json();\n    console.warn(\"Create Today Goal with data \", data);\n    yield put(ActivityActions.getUserActivitySuccessfull(data));\n    yield put(GeneralActions.stopLoading(type));\n  } catch (err) {\n    var _err$response, _err$response$data;\n    yield put(GeneralActions.stopLoading(type));\n    yield put(GeneralActions.addError({\n      action: type,\n      message: (_err$response = err.response) === null || _err$response === void 0 ? void 0 : (_err$response$data = _err$response.data) === null || _err$response$data === void 0 ? void 0 : _err$response$data.error\n    }));\n  }\n}\nfunction* getUserActivityHistory({\n  type,\n  payload\n}) {\n  try {\n    yield put(GeneralActions.removeError(type));\n    yield put(GeneralActions.startLoading(type));\n    console.warn(\"Get User Activvity History \", payload.id);\n    const result = yield call(ActivityService.getUserActivityHistory, payload.id);\n    const data = yield result.json();\n    // console.log(\" User Activity Data \",data)\n    yield put(ActivityActions.getUserActivitySuccessfull(data));\n    yield put(GeneralActions.stopLoading(type));\n  } catch (err) {\n    var _err$response2, _err$response2$data;\n    yield put(GeneralActions.stopLoading(type));\n    yield put(GeneralActions.addError({\n      action: type,\n      message: (_err$response2 = err.response) === null || _err$response2 === void 0 ? void 0 : (_err$response2$data = _err$response2.data) === null || _err$response2$data === void 0 ? void 0 : _err$response2$data.error\n    }));\n  }\n}\nfunction* updateCheckOutStatus({\n  type,\n  payload\n}) {\n  try {\n    yield put(GeneralActions.removeError(type));\n    yield put(GeneralActions.startLoading(type));\n    console.warn(\"payload check out \", payload);\n\n    // Call the checkout API\n    const result = yield call(ActivityService.updateCheckOutStatus, payload);\n    const data = yield result.json();\n\n    // Store the status data in the success object for the UI to access\n    if (data && data.data) {\n      yield put(GeneralActions.addSuccess({\n        action: type,\n        message: data.msg,\n        data: data.data // Include the status data\n      }));\n    }\n\n    // Update activity history\n    const result1 = yield call(ActivityService.getUserActivityHistory, payload.user);\n    const data1 = yield result1.json();\n    console.warn(\"activity history updated\", data1);\n    yield put(ActivityActions.getUserActivitySuccessfull(data1));\n    yield put(GeneralActions.stopLoading(type));\n  } catch (err) {\n    var _err$response3, _err$response3$data;\n    yield put(GeneralActions.stopLoading(type));\n    yield put(GeneralActions.addError({\n      action: type,\n      message: (_err$response3 = err.response) === null || _err$response3 === void 0 ? void 0 : (_err$response3$data = _err$response3.data) === null || _err$response3$data === void 0 ? void 0 : _err$response3$data.error\n    }));\n  }\n}\nfunction* updateBreakInStatus({\n  type,\n  payload\n}) {\n  try {\n    yield put(GeneralActions.removeError(type));\n    yield put(GeneralActions.startLoading(type));\n    console.warn(\"payload break in \", payload);\n    yield call(ActivityService.updateBreakInStatus, payload);\n    const resultHis = yield call(ActivityService.getUserActivityHistory, payload.user);\n    const data1 = yield resultHis.json();\n    console.log(\"Break in SAGA \", data1);\n    yield put(ActivityActions.getUserActivitySuccessfull(data1));\n    yield put(GeneralActions.stopLoading(type));\n  } catch (err) {\n    var _err$response4, _err$response4$data;\n    yield put(GeneralActions.stopLoading(type));\n    yield put(GeneralActions.addError({\n      action: type,\n      message: (_err$response4 = err.response) === null || _err$response4 === void 0 ? void 0 : (_err$response4$data = _err$response4.data) === null || _err$response4$data === void 0 ? void 0 : _err$response4$data.error\n    }));\n  }\n}\nfunction* updateBreakOutStatus({\n  type,\n  payload\n}) {\n  try {\n    yield put(GeneralActions.removeError(type));\n    yield put(GeneralActions.startLoading(type));\n\n    // Perform the break out status update\n    yield call(ActivityService.updateBreakOutStatus, payload);\n\n    // Fetch the updated user activity history\n    console.warn(\"Payload Break Out \", payload);\n    const resultHis = yield call(ActivityService.getUserActivityHistory, payload.user);\n    const data1 = yield resultHis.json();\n    console.warn(\" Updated Break Out Saga \", data1);\n    // Dispatch the updated user activity history\n    yield put(ActivityActions.getUserActivitySuccessfull(data1));\n    yield put(GeneralActions.stopLoading(type));\n  } catch (err) {\n    var _err$response5, _err$response5$data;\n    const errorMessage = ((_err$response5 = err.response) === null || _err$response5 === void 0 ? void 0 : (_err$response5$data = _err$response5.data) === null || _err$response5$data === void 0 ? void 0 : _err$response5$data.error) || \"Unknown error occurred\";\n    yield put(GeneralActions.addError({\n      action: type,\n      message: errorMessage\n    }));\n  } finally {\n    yield put(GeneralActions.stopLoading(type));\n  }\n}\nfunction* updateTodayStatus({\n  type,\n  payload\n}) {\n  try {\n    yield put(GeneralActions.removeError(type));\n    yield put(GeneralActions.startLoading(type));\n    yield call(ActivityService.updateTodayStatus, payload);\n    console.log(\"Today Status Payload \", payload);\n    const result1 = yield call(ActivityService.getUserActivityHistory, payload.user);\n    const data1 = yield result1.json();\n    yield put(ActivityActions.getUserActivitySuccessfull(data1));\n    yield put(GeneralActions.stopLoading(type));\n  } catch (err) {\n    var _err$response6, _err$response6$data;\n    yield put(GeneralActions.stopLoading(type));\n    yield put(GeneralActions.addError({\n      action: type,\n      message: (_err$response6 = err.response) === null || _err$response6 === void 0 ? void 0 : (_err$response6$data = _err$response6.data) === null || _err$response6$data === void 0 ? void 0 : _err$response6$data.error\n    }));\n  }\n}\nfunction* updateLateCheckIn({\n  type,\n  payload\n}) {\n  try {\n    yield put(GeneralActions.removeError(type));\n    yield put(GeneralActions.startLoading(type));\n    console.log(\"Update late check in \", payload._id);\n    yield call(ActivityService.updateLateCheckInStatus, payload);\n    const resultHis = yield call(ActivityService.getUserActivityHistory, payload.user);\n    const data1 = yield resultHis.json();\n    yield put(ActivityActions.getUserActivitySuccessfull(data1));\n    yield put(GeneralActions.stopLoading(type));\n  } catch (err) {\n    var _err$response7, _err$response7$data;\n    yield put(GeneralActions.stopLoading(type));\n    yield put(GeneralActions.addError({\n      action: type,\n      message: (_err$response7 = err.response) === null || _err$response7 === void 0 ? void 0 : (_err$response7$data = _err$response7.data) === null || _err$response7$data === void 0 ? void 0 : _err$response7$data.error\n    }));\n  }\n}\nfunction* updateEarlyCheckOut({\n  type,\n  payload\n}) {\n  try {\n    yield put(GeneralActions.removeError(type));\n    yield put(GeneralActions.startLoading(type));\n    yield call(ActivityService.updateEarlyCheckOutStatus, payload);\n    console.log(\"Update Early  check in \", payload._id);\n    const resultHis = yield call(ActivityService.getUserActivityHistory, payload.user);\n    const data1 = yield resultHis.json();\n    yield put(ActivityActions.getUserActivitySuccessfull(data1));\n    yield put(GeneralActions.stopLoading(type));\n  } catch (err) {\n    var _err$response8, _err$response8$data;\n    yield put(GeneralActions.stopLoading(type));\n    yield put(GeneralActions.addError({\n      action: type,\n      message: (_err$response8 = err.response) === null || _err$response8 === void 0 ? void 0 : (_err$response8$data = _err$response8.data) === null || _err$response8$data === void 0 ? void 0 : _err$response8$data.error\n    }));\n  }\n}\nfunction* updateIdelStart({\n  type,\n  payload\n}) {\n  try {\n    yield put(GeneralActions.removeError(type));\n    yield put(GeneralActions.startLoading(type));\n    yield call(ActivityService.updateIdelStartStatus, payload);\n    // const data = yield result.json()\n    const resultHis = yield call(ActivityService.getUserActivityHistory, payload.user);\n    const data1 = yield resultHis.json();\n    yield put(ActivityActions.getUserActivitySuccessfull(data1));\n    yield put(GeneralActions.stopLoading(type));\n  } catch (err) {\n    var _err$response9, _err$response9$data;\n    yield put(GeneralActions.stopLoading(type));\n    yield put(GeneralActions.addError({\n      action: type,\n      message: (_err$response9 = err.response) === null || _err$response9 === void 0 ? void 0 : (_err$response9$data = _err$response9.data) === null || _err$response9$data === void 0 ? void 0 : _err$response9$data.error\n    }));\n  }\n}\nfunction* updateIdelEnd({\n  type,\n  payload\n}) {\n  try {\n    console.log(\"IDEL END \");\n    yield put(GeneralActions.removeError(type));\n    yield put(GeneralActions.startLoading(type));\n    yield call(ActivityService.updateIdelEndStatus, payload);\n    const resultHis = yield call(ActivityService.getUserActivityHistory, payload.user);\n    const data1 = yield resultHis.json();\n    yield put(ActivityActions.getUserActivitySuccessfull(data1));\n    yield put(GeneralActions.stopLoading(type));\n  } catch (err) {\n    var _err$response0, _err$response0$data;\n    yield put(GeneralActions.stopLoading(type));\n    yield put(GeneralActions.addError({\n      action: type,\n      message: (_err$response0 = err.response) === null || _err$response0 === void 0 ? void 0 : (_err$response0$data = _err$response0.data) === null || _err$response0$data === void 0 ? void 0 : _err$response0$data.error\n    }));\n  }\n}\nfunction* updateProductiityStatus({\n  type,\n  payload\n}) {\n  try {\n    yield put(GeneralActions.removeError(type));\n    yield put(GeneralActions.startLoading(type));\n    console.log(\"Productivity Status \", payload);\n    yield call(ActivityService.updateProductivityStatus, payload);\n    const resultHis = yield call(ActivityService.getUserActivityHistory, payload.user);\n    const data1 = yield resultHis.json();\n    yield put(ActivityActions.getUserActivitySuccessfull(data1));\n    yield put(GeneralActions.stopLoading(type));\n  } catch (err) {\n    var _err$response1, _err$response1$data;\n    yield put(GeneralActions.stopLoading(type));\n    yield put(GeneralActions.addError({\n      action: type,\n      message: (_err$response1 = err.response) === null || _err$response1 === void 0 ? void 0 : (_err$response1$data = _err$response1.data) === null || _err$response1$data === void 0 ? void 0 : _err$response1$data.error\n    }));\n  }\n}\nfunction* overLimitBreakStatus({\n  type,\n  payload\n}) {\n  try {\n    yield put(GeneralActions.removeError(type));\n    yield put(GeneralActions.startLoading(type));\n    // console.log(\"OVER LIMIT STATUS \")\n    yield call(ActivityService.updateOverLimitBreakStatus, payload);\n    yield put(GeneralActions.stopLoading(type));\n  } catch (err) {\n    var _err$response10, _err$response10$data;\n    yield put(GeneralActions.stopLoading(type));\n    yield put(GeneralActions.addError({\n      action: type,\n      message: (_err$response10 = err.response) === null || _err$response10 === void 0 ? void 0 : (_err$response10$data = _err$response10.data) === null || _err$response10$data === void 0 ? void 0 : _err$response10$data.error\n    }));\n  }\n}\nexport function* ActivityWatcher() {\n  yield all([yield takeLatest(ActivityActions.createTodayGoal.type, createTodayGoal), yield takeLatest(ActivityActions.getUserActivity.type, getUserActivityHistory), yield takeLatest(ActivityActions.checkOutStatusUpdate.type, updateCheckOutStatus), yield takeLatest(ActivityActions.breakStartRed.type, updateBreakInStatus), yield takeLatest(ActivityActions.breakEndRed.type, updateBreakOutStatus), yield takeLatest(ActivityActions.createTodayStatus.type, updateTodayStatus), yield takeLatest(ActivityActions.lateCheckIn.type, updateLateCheckIn), yield takeLatest(ActivityActions.earlyCheckOut.type, updateEarlyCheckOut), yield takeLatest(ActivityActions.idelStartRed.type, updateIdelStart), yield takeLatest(ActivityActions.idelEndRed.type, updateIdelEnd), yield takeLatest(ActivityActions.overLimitBreakRed.type, overLimitBreakStatus), yield takeLatest(ActivityActions.productivityStatusRed.type, updateProductiityStatus)]);\n}\n_c = ActivityWatcher;\nvar _c;\n$RefreshReg$(_c, \"ActivityWatcher\");", "map": {"version": 3, "names": ["ActivityService", "ActivityActions", "GeneralActions", "all", "call", "put", "take<PERSON><PERSON>t", "createTodayGoal", "type", "payload", "removeError", "startLoading", "console", "warn", "resultHis", "getUserActivityHistory", "id", "data", "json", "getUserActivitySuccessfull", "stopLoading", "err", "_err$response", "_err$response$data", "addError", "action", "message", "response", "error", "result", "_err$response2", "_err$response2$data", "updateCheckOutStatus", "addSuccess", "msg", "result1", "user", "data1", "_err$response3", "_err$response3$data", "updateBreakInStatus", "log", "_err$response4", "_err$response4$data", "updateBreakOutStatus", "_err$response5", "_err$response5$data", "errorMessage", "updateTodayStatus", "_err$response6", "_err$response6$data", "updateLateCheckIn", "_id", "updateLateCheckInStatus", "_err$response7", "_err$response7$data", "updateEarlyCheckOut", "updateEarlyCheckOutStatus", "_err$response8", "_err$response8$data", "updateIdelStart", "updateIdelStartStatus", "_err$response9", "_err$response9$data", "updateIdelEnd", "updateIdelEndStatus", "_err$response0", "_err$response0$data", "updateProductiityStatus", "updateProductivityStatus", "_err$response1", "_err$response1$data", "overLimitBreakStatus", "updateOverLimitBreakStatus", "_err$response10", "_err$response10$data", "ActivityWatcher", "getUserActivity", "checkOutStatusUpdate", "breakStartRed", "breakEndRed", "createTodayStatus", "lateCheckIn", "earlyCheckOut", "idelStartRed", "idelEndRed", "overLimitBreakRed", "productivityStatusRed", "_c", "$RefreshReg$"], "sources": ["E:/Suraj Patil/Organization_Management_System/meraki-frontend-main/src/sagas/ActivitySaga.js"], "sourcesContent": ["import { ActivityService } from \"services/ActivityService\";\r\nimport {ActivityActions, GeneralActions} from \"../slices/actions\";\r\nimport {all, call, put, takeLatest} from 'redux-saga/effects'\r\n\r\n\r\nfunction *createTodayGoal({type, payload}) {\r\n    try {\r\n        yield put(GeneralActions.removeError(type));\r\n        yield put(GeneralActions.startLoading(type));\r\n        console.warn(\"Create Today Goal\",payload)\r\n         yield call(ActivityService.createTodayGoal, payload);\r\n        const resultHis = yield call(ActivityService.getUserActivityHistory,payload.id);\r\n        const data = yield resultHis.json()\r\n        console.warn(\"Create Today Goal with data \",data)\r\n        yield put(ActivityActions.getUserActivitySuccessfull(data));\r\n        yield put(GeneralActions.stopLoading(type))\r\n    } catch (err) {\r\n        yield put(GeneralActions.stopLoading(type));\r\n        yield put(GeneralActions.addError({\r\n            action: type,\r\n            message: err.response?.data?.error\r\n        }));\r\n    }\r\n}\r\n\r\nfunction *getUserActivityHistory({type,payload}) {\r\n\r\n    try {\r\n        yield put(GeneralActions.removeError(type));\r\n        yield put(GeneralActions.startLoading(type));\r\n        console.warn(\"Get User Activvity History \",payload.id)\r\n        const result = yield call(ActivityService.getUserActivityHistory,payload.id);\r\n        const data = yield result.json()\r\n        // console.log(\" User Activity Data \",data)\r\n         yield put(ActivityActions.getUserActivitySuccessfull(data));\r\n        yield put(GeneralActions.stopLoading(type))\r\n    } catch (err) {\r\n        yield put(GeneralActions.stopLoading(type));\r\n        yield put(GeneralActions.addError({\r\n            action: type,\r\n            message: err.response?.data?.error\r\n        }));\r\n    }\r\n}\r\n\r\nfunction *updateCheckOutStatus({type,payload}) {\r\n    try {\r\n        yield put(GeneralActions.removeError(type));\r\n        yield put(GeneralActions.startLoading(type));\r\n        console.warn(\"payload check out \", payload);\r\n\r\n        // Call the checkout API\r\n        const result = yield call(ActivityService.updateCheckOutStatus, payload);\r\n        const data = yield result.json();\r\n\r\n        // Store the status data in the success object for the UI to access\r\n        if (data && data.data) {\r\n            yield put(GeneralActions.addSuccess({\r\n                action: type,\r\n                message: data.msg,\r\n                data: data.data // Include the status data\r\n            }));\r\n        }\r\n\r\n        // Update activity history\r\n        const result1 = yield call(ActivityService.getUserActivityHistory, payload.user);\r\n        const data1 = yield result1.json();\r\n        console.warn(\"activity history updated\", data1);\r\n        yield put(ActivityActions.getUserActivitySuccessfull(data1));\r\n\r\n        yield put(GeneralActions.stopLoading(type));\r\n    } catch (err) {\r\n        yield put(GeneralActions.stopLoading(type));\r\n        yield put(GeneralActions.addError({\r\n            action: type,\r\n            message: err.response?.data?.error\r\n        }));\r\n    }\r\n}\r\n\r\nfunction *updateBreakInStatus({type,payload}) {\r\n    try {\r\n        yield put(GeneralActions.removeError(type));\r\n        yield put(GeneralActions.startLoading(type));\r\n        console.warn(\"payload break in \",payload)\r\n         yield call(ActivityService.updateBreakInStatus,payload);\r\n        \r\n        const resultHis = yield call(ActivityService.getUserActivityHistory,payload.user);\r\n        const data1 = yield resultHis.json()\r\n        console.log(\"Break in SAGA \",data1)\r\n        yield put(ActivityActions.getUserActivitySuccessfull(data1));\r\n        yield put(GeneralActions.stopLoading(type))\r\n    } catch (err) {\r\n        yield put(GeneralActions.stopLoading(type));\r\n        yield put(GeneralActions.addError({\r\n            action: type,\r\n            message: err.response?.data?.error\r\n        }));\r\n    }\r\n}\r\n\r\nfunction *updateBreakOutStatus({ type, payload }) {\r\n    try {\r\n      yield put(GeneralActions.removeError(type));\r\n      yield put(GeneralActions.startLoading(type));\r\n\r\n      // Perform the break out status update\r\n       yield call(ActivityService.updateBreakOutStatus, payload);\r\n\r\n      // Fetch the updated user activity history\r\n      console.warn(\"Payload Break Out \",payload)\r\n      const resultHis = yield call(ActivityService.getUserActivityHistory,payload.user);\r\n      const data1 = yield resultHis.json();\r\n    console.warn(\" Updated Break Out Saga \",data1)\r\n      // Dispatch the updated user activity history\r\n      yield put(ActivityActions.getUserActivitySuccessfull(data1));\r\n      yield put(GeneralActions.stopLoading(type))\r\n    } catch (err) {\r\n      const errorMessage = err.response?.data?.error || \"Unknown error occurred\";\r\n      yield put(GeneralActions.addError({\r\n        action: type,\r\n        message: errorMessage\r\n      }));\r\n    } finally {\r\n      yield put(GeneralActions.stopLoading(type));\r\n    }\r\n  }\r\n\r\nfunction *updateTodayStatus({type,payload}){\r\n    try {\r\n        yield put(GeneralActions.removeError(type));\r\n        yield put(GeneralActions.startLoading(type));\r\n        yield call(ActivityService.updateTodayStatus,payload);\r\n    \r\n        console.log(\"Today Status Payload \",payload)\r\n        const result1 = yield call(ActivityService.getUserActivityHistory,payload.user);\r\n        const data1 = yield result1.json()\r\n         yield put(ActivityActions.getUserActivitySuccessfull(data1));\r\n        yield put(GeneralActions.stopLoading(type))\r\n    } catch (err) {\r\n        yield put(GeneralActions.stopLoading(type));\r\n        yield put(GeneralActions.addError({\r\n            action: type,\r\n            message: err.response?.data?.error\r\n        }));\r\n    }\r\n\r\n}\r\n\r\nfunction *updateLateCheckIn({type,payload}){\r\n    try {\r\n        yield put(GeneralActions.removeError(type));\r\n        yield put(GeneralActions.startLoading(type));\r\n        console.log(\"Update late check in \",payload._id)\r\n         yield call(ActivityService.updateLateCheckInStatus,payload);\r\n \r\n        const resultHis = yield call(ActivityService.getUserActivityHistory,payload.user);\r\n        const data1 = yield resultHis.json()\r\n        yield put(ActivityActions.getUserActivitySuccessfull(data1));\r\n        yield put(GeneralActions.stopLoading(type))\r\n    } catch (err) {\r\n        yield put(GeneralActions.stopLoading(type));\r\n        yield put(GeneralActions.addError({\r\n            action: type,\r\n            message: err.response?.data?.error\r\n        }));\r\n    }\r\n\r\n}\r\n\r\nfunction *updateEarlyCheckOut({type,payload}){\r\n\r\n    try {\r\n        yield put(GeneralActions.removeError(type));\r\n        yield put(GeneralActions.startLoading(type));\r\n         yield call(ActivityService.updateEarlyCheckOutStatus,payload);\r\n\r\n        console.log(\"Update Early  check in \",payload._id)\r\n        const resultHis = yield call(ActivityService.getUserActivityHistory,payload.user);\r\n        const data1 = yield resultHis.json()\r\n        yield put(ActivityActions.getUserActivitySuccessfull(data1));\r\n        yield put(GeneralActions.stopLoading(type))\r\n    } catch (err) {\r\n        yield put(GeneralActions.stopLoading(type));\r\n        yield put(GeneralActions.addError({\r\n            action: type,\r\n            message: err.response?.data?.error\r\n        }));\r\n    }\r\n\r\n}\r\n\r\nfunction *updateIdelStart({type,payload}) {\r\n    try {\r\n        yield put(GeneralActions.removeError(type));\r\n        yield put(GeneralActions.startLoading(type));\r\n         yield call(ActivityService.updateIdelStartStatus,payload);\r\n        // const data = yield result.json()\r\n        const resultHis = yield call(ActivityService.getUserActivityHistory,payload.user);\r\n        const data1 = yield resultHis.json()\r\n        yield put(ActivityActions.getUserActivitySuccessfull(data1));\r\n        yield put(GeneralActions.stopLoading(type))\r\n    } catch (err) {\r\n        yield put(GeneralActions.stopLoading(type));\r\n        yield put(GeneralActions.addError({\r\n            action: type,\r\n            message: err.response?.data?.error\r\n        }));\r\n    }\r\n}\r\n\r\nfunction *updateIdelEnd({type,payload}) {\r\n    try {\r\n        console.log(\"IDEL END \")\r\n        yield put(GeneralActions.removeError(type));\r\n        yield put(GeneralActions.startLoading(type));\r\n         yield call(ActivityService.updateIdelEndStatus,payload);\r\n      \r\n        const resultHis = yield call(ActivityService.getUserActivityHistory,payload.user);\r\n        const data1 = yield resultHis.json()\r\n        yield put(ActivityActions.getUserActivitySuccessfull(data1));\r\n        yield put(GeneralActions.stopLoading(type))\r\n    } catch (err) {\r\n        yield put(GeneralActions.stopLoading(type));\r\n        yield put(GeneralActions.addError({\r\n            action: type,\r\n            message: err.response?.data?.error\r\n        }));\r\n    }\r\n\r\n}\r\n\r\nfunction *updateProductiityStatus({type,payload}) {\r\n    try {\r\n        yield put(GeneralActions.removeError(type));\r\n        yield put(GeneralActions.startLoading(type));\r\n         console.log(\"Productivity Status \",payload)\r\n         yield call(ActivityService.updateProductivityStatus,payload);\r\n    \r\n\r\n        const resultHis = yield call(ActivityService.getUserActivityHistory,payload.user);\r\n        const data1 = yield resultHis.json()\r\n        yield put(ActivityActions.getUserActivitySuccessfull(data1));\r\n        yield put(GeneralActions.stopLoading(type))\r\n    } catch (err) {\r\n        yield put(GeneralActions.stopLoading(type));\r\n        yield put(GeneralActions.addError({\r\n            action: type,\r\n            message: err.response?.data?.error\r\n        }));\r\n    }\r\n}\r\n\r\nfunction *overLimitBreakStatus({type,payload}) {\r\n    try {\r\n        yield put(GeneralActions.removeError(type));\r\n        yield put(GeneralActions.startLoading(type));\r\n        // console.log(\"OVER LIMIT STATUS \")\r\n       yield call(ActivityService.updateOverLimitBreakStatus,payload);\r\n        yield put(GeneralActions.stopLoading(type))\r\n    } catch (err) {\r\n        yield put(GeneralActions.stopLoading(type));\r\n        yield put(GeneralActions.addError({\r\n            action: type,\r\n            message: err.response?.data?.error\r\n        }));\r\n    }\r\n}\r\n\r\n\r\n\r\nexport function *ActivityWatcher() {\r\n    yield all([\r\n        yield takeLatest(ActivityActions.createTodayGoal.type, createTodayGoal),\r\n        yield takeLatest(ActivityActions.getUserActivity.type, getUserActivityHistory),\r\n        yield takeLatest(ActivityActions.checkOutStatusUpdate.type, updateCheckOutStatus),\r\n        yield takeLatest(ActivityActions.breakStartRed.type, updateBreakInStatus),\r\n        yield takeLatest(ActivityActions.breakEndRed.type, updateBreakOutStatus),\r\n        yield takeLatest(ActivityActions.createTodayStatus.type,updateTodayStatus),\r\n        yield takeLatest(ActivityActions.lateCheckIn.type,updateLateCheckIn),\r\n        yield takeLatest(ActivityActions.earlyCheckOut.type,updateEarlyCheckOut),\r\n        yield takeLatest(ActivityActions.idelStartRed.type,updateIdelStart),\r\n        yield takeLatest(ActivityActions.idelEndRed.type,updateIdelEnd),\r\n        yield takeLatest(ActivityActions.overLimitBreakRed.type,overLimitBreakStatus),\r\n        yield takeLatest(ActivityActions.productivityStatusRed.type,updateProductiityStatus)\r\n ]);\r\n}"], "mappings": "AAAA,SAASA,eAAe,QAAQ,0BAA0B;AAC1D,SAAQC,eAAe,EAAEC,cAAc,QAAO,mBAAmB;AACjE,SAAQC,GAAG,EAAEC,IAAI,EAAEC,GAAG,EAAEC,UAAU,QAAO,oBAAoB;AAG7D,UAAUC,eAAeA,CAAC;EAACC,IAAI;EAAEC;AAAO,CAAC,EAAE;EACvC,IAAI;IACA,MAAMJ,GAAG,CAACH,cAAc,CAACQ,WAAW,CAACF,IAAI,CAAC,CAAC;IAC3C,MAAMH,GAAG,CAACH,cAAc,CAACS,YAAY,CAACH,IAAI,CAAC,CAAC;IAC5CI,OAAO,CAACC,IAAI,CAAC,mBAAmB,EAACJ,OAAO,CAAC;IACxC,MAAML,IAAI,CAACJ,eAAe,CAACO,eAAe,EAAEE,OAAO,CAAC;IACrD,MAAMK,SAAS,GAAG,MAAMV,IAAI,CAACJ,eAAe,CAACe,sBAAsB,EAACN,OAAO,CAACO,EAAE,CAAC;IAC/E,MAAMC,IAAI,GAAG,MAAMH,SAAS,CAACI,IAAI,CAAC,CAAC;IACnCN,OAAO,CAACC,IAAI,CAAC,8BAA8B,EAACI,IAAI,CAAC;IACjD,MAAMZ,GAAG,CAACJ,eAAe,CAACkB,0BAA0B,CAACF,IAAI,CAAC,CAAC;IAC3D,MAAMZ,GAAG,CAACH,cAAc,CAACkB,WAAW,CAACZ,IAAI,CAAC,CAAC;EAC/C,CAAC,CAAC,OAAOa,GAAG,EAAE;IAAA,IAAAC,aAAA,EAAAC,kBAAA;IACV,MAAMlB,GAAG,CAACH,cAAc,CAACkB,WAAW,CAACZ,IAAI,CAAC,CAAC;IAC3C,MAAMH,GAAG,CAACH,cAAc,CAACsB,QAAQ,CAAC;MAC9BC,MAAM,EAAEjB,IAAI;MACZkB,OAAO,GAAAJ,aAAA,GAAED,GAAG,CAACM,QAAQ,cAAAL,aAAA,wBAAAC,kBAAA,GAAZD,aAAA,CAAcL,IAAI,cAAAM,kBAAA,uBAAlBA,kBAAA,CAAoBK;IACjC,CAAC,CAAC,CAAC;EACP;AACJ;AAEA,UAAUb,sBAAsBA,CAAC;EAACP,IAAI;EAACC;AAAO,CAAC,EAAE;EAE7C,IAAI;IACA,MAAMJ,GAAG,CAACH,cAAc,CAACQ,WAAW,CAACF,IAAI,CAAC,CAAC;IAC3C,MAAMH,GAAG,CAACH,cAAc,CAACS,YAAY,CAACH,IAAI,CAAC,CAAC;IAC5CI,OAAO,CAACC,IAAI,CAAC,6BAA6B,EAACJ,OAAO,CAACO,EAAE,CAAC;IACtD,MAAMa,MAAM,GAAG,MAAMzB,IAAI,CAACJ,eAAe,CAACe,sBAAsB,EAACN,OAAO,CAACO,EAAE,CAAC;IAC5E,MAAMC,IAAI,GAAG,MAAMY,MAAM,CAACX,IAAI,CAAC,CAAC;IAChC;IACC,MAAMb,GAAG,CAACJ,eAAe,CAACkB,0BAA0B,CAACF,IAAI,CAAC,CAAC;IAC5D,MAAMZ,GAAG,CAACH,cAAc,CAACkB,WAAW,CAACZ,IAAI,CAAC,CAAC;EAC/C,CAAC,CAAC,OAAOa,GAAG,EAAE;IAAA,IAAAS,cAAA,EAAAC,mBAAA;IACV,MAAM1B,GAAG,CAACH,cAAc,CAACkB,WAAW,CAACZ,IAAI,CAAC,CAAC;IAC3C,MAAMH,GAAG,CAACH,cAAc,CAACsB,QAAQ,CAAC;MAC9BC,MAAM,EAAEjB,IAAI;MACZkB,OAAO,GAAAI,cAAA,GAAET,GAAG,CAACM,QAAQ,cAAAG,cAAA,wBAAAC,mBAAA,GAAZD,cAAA,CAAcb,IAAI,cAAAc,mBAAA,uBAAlBA,mBAAA,CAAoBH;IACjC,CAAC,CAAC,CAAC;EACP;AACJ;AAEA,UAAUI,oBAAoBA,CAAC;EAACxB,IAAI;EAACC;AAAO,CAAC,EAAE;EAC3C,IAAI;IACA,MAAMJ,GAAG,CAACH,cAAc,CAACQ,WAAW,CAACF,IAAI,CAAC,CAAC;IAC3C,MAAMH,GAAG,CAACH,cAAc,CAACS,YAAY,CAACH,IAAI,CAAC,CAAC;IAC5CI,OAAO,CAACC,IAAI,CAAC,oBAAoB,EAAEJ,OAAO,CAAC;;IAE3C;IACA,MAAMoB,MAAM,GAAG,MAAMzB,IAAI,CAACJ,eAAe,CAACgC,oBAAoB,EAAEvB,OAAO,CAAC;IACxE,MAAMQ,IAAI,GAAG,MAAMY,MAAM,CAACX,IAAI,CAAC,CAAC;;IAEhC;IACA,IAAID,IAAI,IAAIA,IAAI,CAACA,IAAI,EAAE;MACnB,MAAMZ,GAAG,CAACH,cAAc,CAAC+B,UAAU,CAAC;QAChCR,MAAM,EAAEjB,IAAI;QACZkB,OAAO,EAAET,IAAI,CAACiB,GAAG;QACjBjB,IAAI,EAAEA,IAAI,CAACA,IAAI,CAAC;MACpB,CAAC,CAAC,CAAC;IACP;;IAEA;IACA,MAAMkB,OAAO,GAAG,MAAM/B,IAAI,CAACJ,eAAe,CAACe,sBAAsB,EAAEN,OAAO,CAAC2B,IAAI,CAAC;IAChF,MAAMC,KAAK,GAAG,MAAMF,OAAO,CAACjB,IAAI,CAAC,CAAC;IAClCN,OAAO,CAACC,IAAI,CAAC,0BAA0B,EAAEwB,KAAK,CAAC;IAC/C,MAAMhC,GAAG,CAACJ,eAAe,CAACkB,0BAA0B,CAACkB,KAAK,CAAC,CAAC;IAE5D,MAAMhC,GAAG,CAACH,cAAc,CAACkB,WAAW,CAACZ,IAAI,CAAC,CAAC;EAC/C,CAAC,CAAC,OAAOa,GAAG,EAAE;IAAA,IAAAiB,cAAA,EAAAC,mBAAA;IACV,MAAMlC,GAAG,CAACH,cAAc,CAACkB,WAAW,CAACZ,IAAI,CAAC,CAAC;IAC3C,MAAMH,GAAG,CAACH,cAAc,CAACsB,QAAQ,CAAC;MAC9BC,MAAM,EAAEjB,IAAI;MACZkB,OAAO,GAAAY,cAAA,GAAEjB,GAAG,CAACM,QAAQ,cAAAW,cAAA,wBAAAC,mBAAA,GAAZD,cAAA,CAAcrB,IAAI,cAAAsB,mBAAA,uBAAlBA,mBAAA,CAAoBX;IACjC,CAAC,CAAC,CAAC;EACP;AACJ;AAEA,UAAUY,mBAAmBA,CAAC;EAAChC,IAAI;EAACC;AAAO,CAAC,EAAE;EAC1C,IAAI;IACA,MAAMJ,GAAG,CAACH,cAAc,CAACQ,WAAW,CAACF,IAAI,CAAC,CAAC;IAC3C,MAAMH,GAAG,CAACH,cAAc,CAACS,YAAY,CAACH,IAAI,CAAC,CAAC;IAC5CI,OAAO,CAACC,IAAI,CAAC,mBAAmB,EAACJ,OAAO,CAAC;IACxC,MAAML,IAAI,CAACJ,eAAe,CAACwC,mBAAmB,EAAC/B,OAAO,CAAC;IAExD,MAAMK,SAAS,GAAG,MAAMV,IAAI,CAACJ,eAAe,CAACe,sBAAsB,EAACN,OAAO,CAAC2B,IAAI,CAAC;IACjF,MAAMC,KAAK,GAAG,MAAMvB,SAAS,CAACI,IAAI,CAAC,CAAC;IACpCN,OAAO,CAAC6B,GAAG,CAAC,gBAAgB,EAACJ,KAAK,CAAC;IACnC,MAAMhC,GAAG,CAACJ,eAAe,CAACkB,0BAA0B,CAACkB,KAAK,CAAC,CAAC;IAC5D,MAAMhC,GAAG,CAACH,cAAc,CAACkB,WAAW,CAACZ,IAAI,CAAC,CAAC;EAC/C,CAAC,CAAC,OAAOa,GAAG,EAAE;IAAA,IAAAqB,cAAA,EAAAC,mBAAA;IACV,MAAMtC,GAAG,CAACH,cAAc,CAACkB,WAAW,CAACZ,IAAI,CAAC,CAAC;IAC3C,MAAMH,GAAG,CAACH,cAAc,CAACsB,QAAQ,CAAC;MAC9BC,MAAM,EAAEjB,IAAI;MACZkB,OAAO,GAAAgB,cAAA,GAAErB,GAAG,CAACM,QAAQ,cAAAe,cAAA,wBAAAC,mBAAA,GAAZD,cAAA,CAAczB,IAAI,cAAA0B,mBAAA,uBAAlBA,mBAAA,CAAoBf;IACjC,CAAC,CAAC,CAAC;EACP;AACJ;AAEA,UAAUgB,oBAAoBA,CAAC;EAAEpC,IAAI;EAAEC;AAAQ,CAAC,EAAE;EAC9C,IAAI;IACF,MAAMJ,GAAG,CAACH,cAAc,CAACQ,WAAW,CAACF,IAAI,CAAC,CAAC;IAC3C,MAAMH,GAAG,CAACH,cAAc,CAACS,YAAY,CAACH,IAAI,CAAC,CAAC;;IAE5C;IACC,MAAMJ,IAAI,CAACJ,eAAe,CAAC4C,oBAAoB,EAAEnC,OAAO,CAAC;;IAE1D;IACAG,OAAO,CAACC,IAAI,CAAC,oBAAoB,EAACJ,OAAO,CAAC;IAC1C,MAAMK,SAAS,GAAG,MAAMV,IAAI,CAACJ,eAAe,CAACe,sBAAsB,EAACN,OAAO,CAAC2B,IAAI,CAAC;IACjF,MAAMC,KAAK,GAAG,MAAMvB,SAAS,CAACI,IAAI,CAAC,CAAC;IACtCN,OAAO,CAACC,IAAI,CAAC,0BAA0B,EAACwB,KAAK,CAAC;IAC5C;IACA,MAAMhC,GAAG,CAACJ,eAAe,CAACkB,0BAA0B,CAACkB,KAAK,CAAC,CAAC;IAC5D,MAAMhC,GAAG,CAACH,cAAc,CAACkB,WAAW,CAACZ,IAAI,CAAC,CAAC;EAC7C,CAAC,CAAC,OAAOa,GAAG,EAAE;IAAA,IAAAwB,cAAA,EAAAC,mBAAA;IACZ,MAAMC,YAAY,GAAG,EAAAF,cAAA,GAAAxB,GAAG,CAACM,QAAQ,cAAAkB,cAAA,wBAAAC,mBAAA,GAAZD,cAAA,CAAc5B,IAAI,cAAA6B,mBAAA,uBAAlBA,mBAAA,CAAoBlB,KAAK,KAAI,wBAAwB;IAC1E,MAAMvB,GAAG,CAACH,cAAc,CAACsB,QAAQ,CAAC;MAChCC,MAAM,EAAEjB,IAAI;MACZkB,OAAO,EAAEqB;IACX,CAAC,CAAC,CAAC;EACL,CAAC,SAAS;IACR,MAAM1C,GAAG,CAACH,cAAc,CAACkB,WAAW,CAACZ,IAAI,CAAC,CAAC;EAC7C;AACF;AAEF,UAAUwC,iBAAiBA,CAAC;EAACxC,IAAI;EAACC;AAAO,CAAC,EAAC;EACvC,IAAI;IACA,MAAMJ,GAAG,CAACH,cAAc,CAACQ,WAAW,CAACF,IAAI,CAAC,CAAC;IAC3C,MAAMH,GAAG,CAACH,cAAc,CAACS,YAAY,CAACH,IAAI,CAAC,CAAC;IAC5C,MAAMJ,IAAI,CAACJ,eAAe,CAACgD,iBAAiB,EAACvC,OAAO,CAAC;IAErDG,OAAO,CAAC6B,GAAG,CAAC,uBAAuB,EAAChC,OAAO,CAAC;IAC5C,MAAM0B,OAAO,GAAG,MAAM/B,IAAI,CAACJ,eAAe,CAACe,sBAAsB,EAACN,OAAO,CAAC2B,IAAI,CAAC;IAC/E,MAAMC,KAAK,GAAG,MAAMF,OAAO,CAACjB,IAAI,CAAC,CAAC;IACjC,MAAMb,GAAG,CAACJ,eAAe,CAACkB,0BAA0B,CAACkB,KAAK,CAAC,CAAC;IAC7D,MAAMhC,GAAG,CAACH,cAAc,CAACkB,WAAW,CAACZ,IAAI,CAAC,CAAC;EAC/C,CAAC,CAAC,OAAOa,GAAG,EAAE;IAAA,IAAA4B,cAAA,EAAAC,mBAAA;IACV,MAAM7C,GAAG,CAACH,cAAc,CAACkB,WAAW,CAACZ,IAAI,CAAC,CAAC;IAC3C,MAAMH,GAAG,CAACH,cAAc,CAACsB,QAAQ,CAAC;MAC9BC,MAAM,EAAEjB,IAAI;MACZkB,OAAO,GAAAuB,cAAA,GAAE5B,GAAG,CAACM,QAAQ,cAAAsB,cAAA,wBAAAC,mBAAA,GAAZD,cAAA,CAAchC,IAAI,cAAAiC,mBAAA,uBAAlBA,mBAAA,CAAoBtB;IACjC,CAAC,CAAC,CAAC;EACP;AAEJ;AAEA,UAAUuB,iBAAiBA,CAAC;EAAC3C,IAAI;EAACC;AAAO,CAAC,EAAC;EACvC,IAAI;IACA,MAAMJ,GAAG,CAACH,cAAc,CAACQ,WAAW,CAACF,IAAI,CAAC,CAAC;IAC3C,MAAMH,GAAG,CAACH,cAAc,CAACS,YAAY,CAACH,IAAI,CAAC,CAAC;IAC5CI,OAAO,CAAC6B,GAAG,CAAC,uBAAuB,EAAChC,OAAO,CAAC2C,GAAG,CAAC;IAC/C,MAAMhD,IAAI,CAACJ,eAAe,CAACqD,uBAAuB,EAAC5C,OAAO,CAAC;IAE5D,MAAMK,SAAS,GAAG,MAAMV,IAAI,CAACJ,eAAe,CAACe,sBAAsB,EAACN,OAAO,CAAC2B,IAAI,CAAC;IACjF,MAAMC,KAAK,GAAG,MAAMvB,SAAS,CAACI,IAAI,CAAC,CAAC;IACpC,MAAMb,GAAG,CAACJ,eAAe,CAACkB,0BAA0B,CAACkB,KAAK,CAAC,CAAC;IAC5D,MAAMhC,GAAG,CAACH,cAAc,CAACkB,WAAW,CAACZ,IAAI,CAAC,CAAC;EAC/C,CAAC,CAAC,OAAOa,GAAG,EAAE;IAAA,IAAAiC,cAAA,EAAAC,mBAAA;IACV,MAAMlD,GAAG,CAACH,cAAc,CAACkB,WAAW,CAACZ,IAAI,CAAC,CAAC;IAC3C,MAAMH,GAAG,CAACH,cAAc,CAACsB,QAAQ,CAAC;MAC9BC,MAAM,EAAEjB,IAAI;MACZkB,OAAO,GAAA4B,cAAA,GAAEjC,GAAG,CAACM,QAAQ,cAAA2B,cAAA,wBAAAC,mBAAA,GAAZD,cAAA,CAAcrC,IAAI,cAAAsC,mBAAA,uBAAlBA,mBAAA,CAAoB3B;IACjC,CAAC,CAAC,CAAC;EACP;AAEJ;AAEA,UAAU4B,mBAAmBA,CAAC;EAAChD,IAAI;EAACC;AAAO,CAAC,EAAC;EAEzC,IAAI;IACA,MAAMJ,GAAG,CAACH,cAAc,CAACQ,WAAW,CAACF,IAAI,CAAC,CAAC;IAC3C,MAAMH,GAAG,CAACH,cAAc,CAACS,YAAY,CAACH,IAAI,CAAC,CAAC;IAC3C,MAAMJ,IAAI,CAACJ,eAAe,CAACyD,yBAAyB,EAAChD,OAAO,CAAC;IAE9DG,OAAO,CAAC6B,GAAG,CAAC,yBAAyB,EAAChC,OAAO,CAAC2C,GAAG,CAAC;IAClD,MAAMtC,SAAS,GAAG,MAAMV,IAAI,CAACJ,eAAe,CAACe,sBAAsB,EAACN,OAAO,CAAC2B,IAAI,CAAC;IACjF,MAAMC,KAAK,GAAG,MAAMvB,SAAS,CAACI,IAAI,CAAC,CAAC;IACpC,MAAMb,GAAG,CAACJ,eAAe,CAACkB,0BAA0B,CAACkB,KAAK,CAAC,CAAC;IAC5D,MAAMhC,GAAG,CAACH,cAAc,CAACkB,WAAW,CAACZ,IAAI,CAAC,CAAC;EAC/C,CAAC,CAAC,OAAOa,GAAG,EAAE;IAAA,IAAAqC,cAAA,EAAAC,mBAAA;IACV,MAAMtD,GAAG,CAACH,cAAc,CAACkB,WAAW,CAACZ,IAAI,CAAC,CAAC;IAC3C,MAAMH,GAAG,CAACH,cAAc,CAACsB,QAAQ,CAAC;MAC9BC,MAAM,EAAEjB,IAAI;MACZkB,OAAO,GAAAgC,cAAA,GAAErC,GAAG,CAACM,QAAQ,cAAA+B,cAAA,wBAAAC,mBAAA,GAAZD,cAAA,CAAczC,IAAI,cAAA0C,mBAAA,uBAAlBA,mBAAA,CAAoB/B;IACjC,CAAC,CAAC,CAAC;EACP;AAEJ;AAEA,UAAUgC,eAAeA,CAAC;EAACpD,IAAI;EAACC;AAAO,CAAC,EAAE;EACtC,IAAI;IACA,MAAMJ,GAAG,CAACH,cAAc,CAACQ,WAAW,CAACF,IAAI,CAAC,CAAC;IAC3C,MAAMH,GAAG,CAACH,cAAc,CAACS,YAAY,CAACH,IAAI,CAAC,CAAC;IAC3C,MAAMJ,IAAI,CAACJ,eAAe,CAAC6D,qBAAqB,EAACpD,OAAO,CAAC;IAC1D;IACA,MAAMK,SAAS,GAAG,MAAMV,IAAI,CAACJ,eAAe,CAACe,sBAAsB,EAACN,OAAO,CAAC2B,IAAI,CAAC;IACjF,MAAMC,KAAK,GAAG,MAAMvB,SAAS,CAACI,IAAI,CAAC,CAAC;IACpC,MAAMb,GAAG,CAACJ,eAAe,CAACkB,0BAA0B,CAACkB,KAAK,CAAC,CAAC;IAC5D,MAAMhC,GAAG,CAACH,cAAc,CAACkB,WAAW,CAACZ,IAAI,CAAC,CAAC;EAC/C,CAAC,CAAC,OAAOa,GAAG,EAAE;IAAA,IAAAyC,cAAA,EAAAC,mBAAA;IACV,MAAM1D,GAAG,CAACH,cAAc,CAACkB,WAAW,CAACZ,IAAI,CAAC,CAAC;IAC3C,MAAMH,GAAG,CAACH,cAAc,CAACsB,QAAQ,CAAC;MAC9BC,MAAM,EAAEjB,IAAI;MACZkB,OAAO,GAAAoC,cAAA,GAAEzC,GAAG,CAACM,QAAQ,cAAAmC,cAAA,wBAAAC,mBAAA,GAAZD,cAAA,CAAc7C,IAAI,cAAA8C,mBAAA,uBAAlBA,mBAAA,CAAoBnC;IACjC,CAAC,CAAC,CAAC;EACP;AACJ;AAEA,UAAUoC,aAAaA,CAAC;EAACxD,IAAI;EAACC;AAAO,CAAC,EAAE;EACpC,IAAI;IACAG,OAAO,CAAC6B,GAAG,CAAC,WAAW,CAAC;IACxB,MAAMpC,GAAG,CAACH,cAAc,CAACQ,WAAW,CAACF,IAAI,CAAC,CAAC;IAC3C,MAAMH,GAAG,CAACH,cAAc,CAACS,YAAY,CAACH,IAAI,CAAC,CAAC;IAC3C,MAAMJ,IAAI,CAACJ,eAAe,CAACiE,mBAAmB,EAACxD,OAAO,CAAC;IAExD,MAAMK,SAAS,GAAG,MAAMV,IAAI,CAACJ,eAAe,CAACe,sBAAsB,EAACN,OAAO,CAAC2B,IAAI,CAAC;IACjF,MAAMC,KAAK,GAAG,MAAMvB,SAAS,CAACI,IAAI,CAAC,CAAC;IACpC,MAAMb,GAAG,CAACJ,eAAe,CAACkB,0BAA0B,CAACkB,KAAK,CAAC,CAAC;IAC5D,MAAMhC,GAAG,CAACH,cAAc,CAACkB,WAAW,CAACZ,IAAI,CAAC,CAAC;EAC/C,CAAC,CAAC,OAAOa,GAAG,EAAE;IAAA,IAAA6C,cAAA,EAAAC,mBAAA;IACV,MAAM9D,GAAG,CAACH,cAAc,CAACkB,WAAW,CAACZ,IAAI,CAAC,CAAC;IAC3C,MAAMH,GAAG,CAACH,cAAc,CAACsB,QAAQ,CAAC;MAC9BC,MAAM,EAAEjB,IAAI;MACZkB,OAAO,GAAAwC,cAAA,GAAE7C,GAAG,CAACM,QAAQ,cAAAuC,cAAA,wBAAAC,mBAAA,GAAZD,cAAA,CAAcjD,IAAI,cAAAkD,mBAAA,uBAAlBA,mBAAA,CAAoBvC;IACjC,CAAC,CAAC,CAAC;EACP;AAEJ;AAEA,UAAUwC,uBAAuBA,CAAC;EAAC5D,IAAI;EAACC;AAAO,CAAC,EAAE;EAC9C,IAAI;IACA,MAAMJ,GAAG,CAACH,cAAc,CAACQ,WAAW,CAACF,IAAI,CAAC,CAAC;IAC3C,MAAMH,GAAG,CAACH,cAAc,CAACS,YAAY,CAACH,IAAI,CAAC,CAAC;IAC3CI,OAAO,CAAC6B,GAAG,CAAC,sBAAsB,EAAChC,OAAO,CAAC;IAC3C,MAAML,IAAI,CAACJ,eAAe,CAACqE,wBAAwB,EAAC5D,OAAO,CAAC;IAG7D,MAAMK,SAAS,GAAG,MAAMV,IAAI,CAACJ,eAAe,CAACe,sBAAsB,EAACN,OAAO,CAAC2B,IAAI,CAAC;IACjF,MAAMC,KAAK,GAAG,MAAMvB,SAAS,CAACI,IAAI,CAAC,CAAC;IACpC,MAAMb,GAAG,CAACJ,eAAe,CAACkB,0BAA0B,CAACkB,KAAK,CAAC,CAAC;IAC5D,MAAMhC,GAAG,CAACH,cAAc,CAACkB,WAAW,CAACZ,IAAI,CAAC,CAAC;EAC/C,CAAC,CAAC,OAAOa,GAAG,EAAE;IAAA,IAAAiD,cAAA,EAAAC,mBAAA;IACV,MAAMlE,GAAG,CAACH,cAAc,CAACkB,WAAW,CAACZ,IAAI,CAAC,CAAC;IAC3C,MAAMH,GAAG,CAACH,cAAc,CAACsB,QAAQ,CAAC;MAC9BC,MAAM,EAAEjB,IAAI;MACZkB,OAAO,GAAA4C,cAAA,GAAEjD,GAAG,CAACM,QAAQ,cAAA2C,cAAA,wBAAAC,mBAAA,GAAZD,cAAA,CAAcrD,IAAI,cAAAsD,mBAAA,uBAAlBA,mBAAA,CAAoB3C;IACjC,CAAC,CAAC,CAAC;EACP;AACJ;AAEA,UAAU4C,oBAAoBA,CAAC;EAAChE,IAAI;EAACC;AAAO,CAAC,EAAE;EAC3C,IAAI;IACA,MAAMJ,GAAG,CAACH,cAAc,CAACQ,WAAW,CAACF,IAAI,CAAC,CAAC;IAC3C,MAAMH,GAAG,CAACH,cAAc,CAACS,YAAY,CAACH,IAAI,CAAC,CAAC;IAC5C;IACD,MAAMJ,IAAI,CAACJ,eAAe,CAACyE,0BAA0B,EAAChE,OAAO,CAAC;IAC7D,MAAMJ,GAAG,CAACH,cAAc,CAACkB,WAAW,CAACZ,IAAI,CAAC,CAAC;EAC/C,CAAC,CAAC,OAAOa,GAAG,EAAE;IAAA,IAAAqD,eAAA,EAAAC,oBAAA;IACV,MAAMtE,GAAG,CAACH,cAAc,CAACkB,WAAW,CAACZ,IAAI,CAAC,CAAC;IAC3C,MAAMH,GAAG,CAACH,cAAc,CAACsB,QAAQ,CAAC;MAC9BC,MAAM,EAAEjB,IAAI;MACZkB,OAAO,GAAAgD,eAAA,GAAErD,GAAG,CAACM,QAAQ,cAAA+C,eAAA,wBAAAC,oBAAA,GAAZD,eAAA,CAAczD,IAAI,cAAA0D,oBAAA,uBAAlBA,oBAAA,CAAoB/C;IACjC,CAAC,CAAC,CAAC;EACP;AACJ;AAIA,OAAO,UAAUgD,eAAeA,CAAA,EAAG;EAC/B,MAAMzE,GAAG,CAAC,CACN,MAAMG,UAAU,CAACL,eAAe,CAACM,eAAe,CAACC,IAAI,EAAED,eAAe,CAAC,EACvE,MAAMD,UAAU,CAACL,eAAe,CAAC4E,eAAe,CAACrE,IAAI,EAAEO,sBAAsB,CAAC,EAC9E,MAAMT,UAAU,CAACL,eAAe,CAAC6E,oBAAoB,CAACtE,IAAI,EAAEwB,oBAAoB,CAAC,EACjF,MAAM1B,UAAU,CAACL,eAAe,CAAC8E,aAAa,CAACvE,IAAI,EAAEgC,mBAAmB,CAAC,EACzE,MAAMlC,UAAU,CAACL,eAAe,CAAC+E,WAAW,CAACxE,IAAI,EAAEoC,oBAAoB,CAAC,EACxE,MAAMtC,UAAU,CAACL,eAAe,CAACgF,iBAAiB,CAACzE,IAAI,EAACwC,iBAAiB,CAAC,EAC1E,MAAM1C,UAAU,CAACL,eAAe,CAACiF,WAAW,CAAC1E,IAAI,EAAC2C,iBAAiB,CAAC,EACpE,MAAM7C,UAAU,CAACL,eAAe,CAACkF,aAAa,CAAC3E,IAAI,EAACgD,mBAAmB,CAAC,EACxE,MAAMlD,UAAU,CAACL,eAAe,CAACmF,YAAY,CAAC5E,IAAI,EAACoD,eAAe,CAAC,EACnE,MAAMtD,UAAU,CAACL,eAAe,CAACoF,UAAU,CAAC7E,IAAI,EAACwD,aAAa,CAAC,EAC/D,MAAM1D,UAAU,CAACL,eAAe,CAACqF,iBAAiB,CAAC9E,IAAI,EAACgE,oBAAoB,CAAC,EAC7E,MAAMlE,UAAU,CAACL,eAAe,CAACsF,qBAAqB,CAAC/E,IAAI,EAAC4D,uBAAuB,CAAC,CAC1F,CAAC;AACH;AAACoB,EAAA,GAfgBZ,eAAe;AAAA,IAAAY,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}