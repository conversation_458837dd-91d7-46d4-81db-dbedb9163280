import { createSlice } from "@reduxjs/toolkit";


export const ActivitySlice = createSlice({
    name: "Activity",
    initialState: {
        activityArr: [], // For single user activity (legacy)
        multiUserActivityArr: [], // For multi-user activity data (new)
    },
    reducers: {
        createTodayGoal: () => {
        },
        createTodayStatus: () => {
        },
        getUserActivitySuccessfull: (state,action) => {

            if(action.payload.length === 0) {
                state.activityArr = []
                state.multiUserActivityArr = []
                // console.log("1 REDUCER ACTIVITY LOG ",action.payload)
            } else {
                // Check if this is multi-user data (has properties like name, email, clockin, etc.)
                // vs single-user data (has properties like user, checkInTime, etc.)
                const isMultiUserData = action.payload.length > 0 &&
                    Object.prototype.hasOwnProperty.call(action.payload[0], 'name') &&
                    Object.prototype.hasOwnProperty.call(action.payload[0], 'clockin');

                if (isMultiUserData) {
                    state.multiUserActivityArr = action.payload;
                    console.log("REDUCER MULTI-USER ACTIVITY LOG ", action.payload);
                } else {
                    state.activityArr = action.payload;
                    console.log("REDUCER SINGLE-USER ACTIVITY LOG ", action.payload);
                }
            }

        },
        getUserActivity: () => {
        },
        checkOutStatusUpdate: () => {},
        breakStartRed: () => {},
        breakEndRed: () => {},
        lateCheckIn: () => {},
        earlyCheckOut: () => {},
        idelStartRed: () => {},
        idelEndRed: () => {},
        productivityStatusRed: () => {},
        overLimitBreakRed: () => {},
        eraseActivity: (state = []) => {
            state.activityArr = []
            state.multiUserActivityArr = []
        },
        createTimelineRequest: () => {},
        updateTimelineRequest: () => {},
        getTimelineRequests: () => {}
    }
});

export default ActivitySlice;