{"ast": null, "code": "/**\r\n * Work Schedule Service\r\n * \r\n * This service handles all work schedule-related API calls including:\r\n * - Getting user work schedules\r\n * - Updating user work schedules\r\n * - Managing work schedule templates\r\n */\n\nimport { del, get, patch, post } from \"../utils/api\";\n\n/**\r\n * Get user's work schedule\r\n *\r\n * @param {string} userId - User ID\r\n * @returns {Promise<Object>} Response containing user's work schedule\r\n */\nconst GetUserWorkSchedule = async userId => {\n  try {\n    return await get(`work-schedule/user/${userId}`);\n  } catch (error) {\n    throw new Error(`Failed to fetch user work schedule: ${error.message}`);\n  }\n};\n\n/**\r\n * Update user's work schedule\r\n *\r\n * @param {string} userId - User ID\r\n * @param {Object} workSchedule - Work schedule data\r\n * @returns {Promise<Object>} Response containing update result\r\n */\n_c = GetUserWorkSchedule;\nconst UpdateUserWorkSchedule = async (userId, workSchedule) => {\n  try {\n    return await patch(`work-schedule/user/${userId}`, {\n      workSchedule\n    });\n  } catch (error) {\n    throw new Error(`Failed to update user work schedule: ${error.message}`);\n  }\n};\n\n/**\r\n * Get work schedule templates\r\n *\r\n * @returns {Promise<Object>} Response containing work schedule templates\r\n */\n_c2 = UpdateUserWorkSchedule;\nconst GetWorkScheduleTemplates = async () => {\n  try {\n    return await get(`work-schedule/templates`);\n  } catch (error) {\n    throw new Error(`Failed to fetch work schedule templates: ${error.message}`);\n  }\n};\n\n/**\r\n * Create work schedule template\r\n *\r\n * @param {Object} templateData - Template data\r\n * @returns {Promise<Object>} Response containing created template\r\n */\n_c3 = GetWorkScheduleTemplates;\nconst CreateWorkScheduleTemplate = async templateData => {\n  try {\n    return await post(`work-schedule/templates`, templateData);\n  } catch (error) {\n    throw new Error(`Failed to create work schedule template: ${error.message}`);\n  }\n};\n\n/**\r\n * Update work schedule template\r\n *\r\n * @param {string} templateId - Template ID\r\n * @param {Object} templateData - Updated template data\r\n * @returns {Promise<Object>} Response containing update result\r\n */\n_c4 = CreateWorkScheduleTemplate;\nconst UpdateWorkScheduleTemplate = async (templateId, templateData) => {\n  try {\n    return await patch(`work-schedule/templates/${templateId}`, templateData);\n  } catch (error) {\n    throw new Error(`Failed to update work schedule template: ${error.message}`);\n  }\n};\n\n/**\r\n * Delete work schedule template\r\n *\r\n * @param {string} templateId - Template ID\r\n * @returns {Promise<Object>} Response containing deletion result\r\n */\n_c5 = UpdateWorkScheduleTemplate;\nconst DeleteWorkScheduleTemplate = async templateId => {\n  try {\n    return await del(`work-schedule/templates/${templateId}`);\n  } catch (error) {\n    throw new Error(`Failed to delete work schedule template: ${error.message}`);\n  }\n};\n\n/**\r\n * Work Schedule service object with all work schedule-related API methods\r\n */\n_c6 = DeleteWorkScheduleTemplate;\nexport const WorkScheduleService = {\n  GetUserWorkSchedule,\n  UpdateUserWorkSchedule,\n  GetWorkScheduleTemplates,\n  CreateWorkScheduleTemplate,\n  UpdateWorkScheduleTemplate,\n  DeleteWorkScheduleTemplate\n};\nvar _c, _c2, _c3, _c4, _c5, _c6;\n$RefreshReg$(_c, \"GetUserWorkSchedule\");\n$RefreshReg$(_c2, \"UpdateUserWorkSchedule\");\n$RefreshReg$(_c3, \"GetWorkScheduleTemplates\");\n$RefreshReg$(_c4, \"CreateWorkScheduleTemplate\");\n$RefreshReg$(_c5, \"UpdateWorkScheduleTemplate\");\n$RefreshReg$(_c6, \"DeleteWorkScheduleTemplate\");", "map": {"version": 3, "names": ["del", "get", "patch", "post", "GetUserWorkSchedule", "userId", "error", "Error", "message", "_c", "UpdateUserWorkSchedule", "workSchedule", "_c2", "GetWorkScheduleTemplates", "_c3", "CreateWorkScheduleTemplate", "templateData", "_c4", "UpdateWorkScheduleTemplate", "templateId", "_c5", "DeleteWorkScheduleTemplate", "_c6", "WorkScheduleService", "$RefreshReg$"], "sources": ["E:/Suraj Patil/Organization_Management_System/meraki-frontend-main/src/services/WorkScheduleService.js"], "sourcesContent": ["/**\r\n * Work Schedule Service\r\n * \r\n * This service handles all work schedule-related API calls including:\r\n * - Getting user work schedules\r\n * - Updating user work schedules\r\n * - Managing work schedule templates\r\n */\r\n\r\nimport { del, get, patch, post } from \"../utils/api\";\r\n\r\n/**\r\n * Get user's work schedule\r\n *\r\n * @param {string} userId - User ID\r\n * @returns {Promise<Object>} Response containing user's work schedule\r\n */\r\nconst GetUserWorkSchedule = async (userId) => {\r\n    try {\r\n        return await get(`work-schedule/user/${userId}`);\r\n    } catch (error) {\r\n        throw new Error(`Failed to fetch user work schedule: ${error.message}`);\r\n    }\r\n};\r\n\r\n/**\r\n * Update user's work schedule\r\n *\r\n * @param {string} userId - User ID\r\n * @param {Object} workSchedule - Work schedule data\r\n * @returns {Promise<Object>} Response containing update result\r\n */\r\nconst UpdateUserWorkSchedule = async (userId, workSchedule) => {\r\n    try {\r\n        return await patch(`work-schedule/user/${userId}`, { workSchedule });\r\n    } catch (error) {\r\n        throw new Error(`Failed to update user work schedule: ${error.message}`);\r\n    }\r\n};\r\n\r\n/**\r\n * Get work schedule templates\r\n *\r\n * @returns {Promise<Object>} Response containing work schedule templates\r\n */\r\nconst GetWorkScheduleTemplates = async () => {\r\n    try {\r\n        return await get(`work-schedule/templates`);\r\n    } catch (error) {\r\n        throw new Error(`Failed to fetch work schedule templates: ${error.message}`);\r\n    }\r\n};\r\n\r\n/**\r\n * Create work schedule template\r\n *\r\n * @param {Object} templateData - Template data\r\n * @returns {Promise<Object>} Response containing created template\r\n */\r\nconst CreateWorkScheduleTemplate = async (templateData) => {\r\n    try {\r\n        return await post(`work-schedule/templates`, templateData);\r\n    } catch (error) {\r\n        throw new Error(`Failed to create work schedule template: ${error.message}`);\r\n    }\r\n};\r\n\r\n/**\r\n * Update work schedule template\r\n *\r\n * @param {string} templateId - Template ID\r\n * @param {Object} templateData - Updated template data\r\n * @returns {Promise<Object>} Response containing update result\r\n */\r\nconst UpdateWorkScheduleTemplate = async (templateId, templateData) => {\r\n    try {\r\n        return await patch(`work-schedule/templates/${templateId}`, templateData);\r\n    } catch (error) {\r\n        throw new Error(`Failed to update work schedule template: ${error.message}`);\r\n    }\r\n};\r\n\r\n/**\r\n * Delete work schedule template\r\n *\r\n * @param {string} templateId - Template ID\r\n * @returns {Promise<Object>} Response containing deletion result\r\n */\r\nconst DeleteWorkScheduleTemplate = async (templateId) => {\r\n    try {\r\n        return await del(`work-schedule/templates/${templateId}`);\r\n    } catch (error) {\r\n        throw new Error(`Failed to delete work schedule template: ${error.message}`);\r\n    }\r\n};\r\n\r\n/**\r\n * Work Schedule service object with all work schedule-related API methods\r\n */\r\nexport const WorkScheduleService = {\r\n    GetUserWorkSchedule,\r\n    UpdateUserWorkSchedule,\r\n    GetWorkScheduleTemplates,\r\n    CreateWorkScheduleTemplate,\r\n    UpdateWorkScheduleTemplate,\r\n    DeleteWorkScheduleTemplate\r\n};\r\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,SAASA,GAAG,EAAEC,GAAG,EAAEC,KAAK,EAAEC,IAAI,QAAQ,cAAc;;AAEpD;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,mBAAmB,GAAG,MAAOC,MAAM,IAAK;EAC1C,IAAI;IACA,OAAO,MAAMJ,GAAG,CAAC,sBAAsBI,MAAM,EAAE,CAAC;EACpD,CAAC,CAAC,OAAOC,KAAK,EAAE;IACZ,MAAM,IAAIC,KAAK,CAAC,uCAAuCD,KAAK,CAACE,OAAO,EAAE,CAAC;EAC3E;AACJ,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AANAC,EAAA,GARML,mBAAmB;AAezB,MAAMM,sBAAsB,GAAG,MAAAA,CAAOL,MAAM,EAAEM,YAAY,KAAK;EAC3D,IAAI;IACA,OAAO,MAAMT,KAAK,CAAC,sBAAsBG,MAAM,EAAE,EAAE;MAAEM;IAAa,CAAC,CAAC;EACxE,CAAC,CAAC,OAAOL,KAAK,EAAE;IACZ,MAAM,IAAIC,KAAK,CAAC,wCAAwCD,KAAK,CAACE,OAAO,EAAE,CAAC;EAC5E;AACJ,CAAC;;AAED;AACA;AACA;AACA;AACA;AAJAI,GAAA,GARMF,sBAAsB;AAa5B,MAAMG,wBAAwB,GAAG,MAAAA,CAAA,KAAY;EACzC,IAAI;IACA,OAAO,MAAMZ,GAAG,CAAC,yBAAyB,CAAC;EAC/C,CAAC,CAAC,OAAOK,KAAK,EAAE;IACZ,MAAM,IAAIC,KAAK,CAAC,4CAA4CD,KAAK,CAACE,OAAO,EAAE,CAAC;EAChF;AACJ,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AALAM,GAAA,GARMD,wBAAwB;AAc9B,MAAME,0BAA0B,GAAG,MAAOC,YAAY,IAAK;EACvD,IAAI;IACA,OAAO,MAAMb,IAAI,CAAC,yBAAyB,EAAEa,YAAY,CAAC;EAC9D,CAAC,CAAC,OAAOV,KAAK,EAAE;IACZ,MAAM,IAAIC,KAAK,CAAC,4CAA4CD,KAAK,CAACE,OAAO,EAAE,CAAC;EAChF;AACJ,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AANAS,GAAA,GARMF,0BAA0B;AAehC,MAAMG,0BAA0B,GAAG,MAAAA,CAAOC,UAAU,EAAEH,YAAY,KAAK;EACnE,IAAI;IACA,OAAO,MAAMd,KAAK,CAAC,2BAA2BiB,UAAU,EAAE,EAAEH,YAAY,CAAC;EAC7E,CAAC,CAAC,OAAOV,KAAK,EAAE;IACZ,MAAM,IAAIC,KAAK,CAAC,4CAA4CD,KAAK,CAACE,OAAO,EAAE,CAAC;EAChF;AACJ,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AALAY,GAAA,GARMF,0BAA0B;AAchC,MAAMG,0BAA0B,GAAG,MAAOF,UAAU,IAAK;EACrD,IAAI;IACA,OAAO,MAAMnB,GAAG,CAAC,2BAA2BmB,UAAU,EAAE,CAAC;EAC7D,CAAC,CAAC,OAAOb,KAAK,EAAE;IACZ,MAAM,IAAIC,KAAK,CAAC,4CAA4CD,KAAK,CAACE,OAAO,EAAE,CAAC;EAChF;AACJ,CAAC;;AAED;AACA;AACA;AAFAc,GAAA,GARMD,0BAA0B;AAWhC,OAAO,MAAME,mBAAmB,GAAG;EAC/BnB,mBAAmB;EACnBM,sBAAsB;EACtBG,wBAAwB;EACxBE,0BAA0B;EAC1BG,0BAA0B;EAC1BG;AACJ,CAAC;AAAC,IAAAZ,EAAA,EAAAG,GAAA,EAAAE,GAAA,EAAAG,GAAA,EAAAG,GAAA,EAAAE,GAAA;AAAAE,YAAA,CAAAf,EAAA;AAAAe,YAAA,CAAAZ,GAAA;AAAAY,YAAA,CAAAV,GAAA;AAAAU,YAAA,CAAAP,GAAA;AAAAO,YAAA,CAAAJ,GAAA;AAAAI,YAAA,CAAAF,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}