{"ast": null, "code": "var _jsxFileName = \"E:\\\\Suraj Patil\\\\Organization_Management_System\\\\meraki-frontend-main\\\\src\\\\screens\\\\Department\\\\Form.js\",\n  _s = $RefreshSig$();\nimport React, { useEffect } from \"react\";\nimport { Box, Button, Card, Grid } from \"@mui/material\";\nimport PageTitle from \"components/PageTitle\";\nimport * as yup from \"yup\";\nimport { useFormik } from \"formik\";\nimport { useDispatch, useSelector } from \"react-redux\";\nimport { useParams } from \"react-router-dom\";\nimport { toast } from \"react-toastify\";\nimport { DepartmentSelector, GeneralSelector } from \"selectors\";\nimport { DepartmentActions, GeneralActions } from \"slices/actions\";\nimport Input from \"components/Input\";\nimport FormSkeleton from \"../../components/Skeleton/FormSkeleton\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nexport default function FormDepartment() {\n  _s();\n  var _department$name;\n  const dispatch = useDispatch();\n  const {\n    id\n  } = useParams();\n  const department = useSelector(DepartmentSelector.getDepartmentById());\n  const loading = useSelector(GeneralSelector.loader(DepartmentActions.getDepartmentById.type));\n  const actions = [DepartmentActions.createDepartment.type, DepartmentActions.updateDepartment.type];\n  const success = useSelector(GeneralSelector.success(actions));\n  useEffect(() => {\n    if (id) {\n      dispatch(DepartmentActions.getDepartmentById(id));\n    }\n  }, []);\n  useEffect(() => {\n    if (success.length > 0) {\n      var _action$message;\n      const action = success.find(item => actions.includes(item.action));\n      toast.success(`${(_action$message = action === null || action === void 0 ? void 0 : action.message) !== null && _action$message !== void 0 ? _action$message : \"Success\"}`, {\n        position: \"top-right\",\n        autoClose: 3000,\n        closeOnClick: true,\n        pauseOnHover: false,\n        pauseOnFocusLoss: false\n      });\n      dispatch(GeneralActions.removeSuccess(actions));\n    }\n  }, [success]);\n  const validationSchema = yup.object({\n    name: yup.string().required('Name is required')\n  });\n  const formik = useFormik({\n    initialValues: {\n      name: (_department$name = department === null || department === void 0 ? void 0 : department.name) !== null && _department$name !== void 0 ? _department$name : \"\"\n    },\n    enableReinitialize: true,\n    validationSchema: validationSchema,\n    onSubmit: values => {\n      handleSubmit(values);\n    }\n  });\n  const handleSubmit = params => {\n    if (id) {\n      dispatch(DepartmentActions.updateDepartment({\n        ...params,\n        id\n      }));\n    } else {\n      dispatch(DepartmentActions.createDepartment(params));\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(Box, {\n    children: [/*#__PURE__*/_jsxDEV(PageTitle, {\n      isBack: true,\n      title: `${id ? \"Update\" : \"Create\"} Department`\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 77,\n      columnNumber: 13\n    }, this), loading ? /*#__PURE__*/_jsxDEV(FormSkeleton, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 80,\n      columnNumber: 17\n    }, this) : /*#__PURE__*/_jsxDEV(Grid, {\n      container: true,\n      justifyContent: \"center\",\n      children: /*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        lg: 6,\n        sm: 12,\n        xs: 12,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          children: /*#__PURE__*/_jsxDEV(\"form\", {\n            onSubmit: formik.handleSubmit,\n            children: [/*#__PURE__*/_jsxDEV(Input, {\n              fullWidth: true,\n              label: \"Name\",\n              sx: {\n                mb: 2\n              },\n              name: \"name\",\n              value: formik.values.name,\n              onChange: formik.handleChange,\n              error: formik.touched.name && Boolean(formik.errors.name),\n              helpertext: formik.touched.name ? formik.errors.name : \"\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 86,\n              columnNumber: 33\n            }, this), /*#__PURE__*/_jsxDEV(Grid, {\n              container: true,\n              justifyContent: \"flex-end\",\n              children: /*#__PURE__*/_jsxDEV(Button, {\n                color: \"primary\",\n                variant: \"contained\",\n                type: \"submit\",\n                children: \"Submit\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 98,\n                columnNumber: 37\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 97,\n              columnNumber: 33\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 85,\n            columnNumber: 29\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 84,\n          columnNumber: 25\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 83,\n        columnNumber: 21\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 82,\n      columnNumber: 17\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 76,\n    columnNumber: 9\n  }, this);\n}\n_s(FormDepartment, \"1hrEY4SVVr01t3bKaEhA2OSw3EA=\", false, function () {\n  return [useDispatch, useParams, useSelector, useSelector, useSelector, useFormik];\n});\n_c = FormDepartment;\nvar _c;\n$RefreshReg$(_c, \"FormDepartment\");", "map": {"version": 3, "names": ["React", "useEffect", "Box", "<PERSON><PERSON>", "Card", "Grid", "Page<PERSON><PERSON>le", "yup", "useFormik", "useDispatch", "useSelector", "useParams", "toast", "DepartmentSelector", "GeneralSelector", "DepartmentActions", "GeneralActions", "Input", "FormSkeleton", "jsxDEV", "_jsxDEV", "FormDepartment", "_s", "_department$name", "dispatch", "id", "department", "getDepartmentById", "loading", "loader", "type", "actions", "createDepartment", "updateDepartment", "success", "length", "_action$message", "action", "find", "item", "includes", "message", "position", "autoClose", "closeOnClick", "pauseOnHover", "pauseOnFocusLoss", "removeSuccess", "validationSchema", "object", "name", "string", "required", "formik", "initialValues", "enableReinitialize", "onSubmit", "values", "handleSubmit", "params", "children", "isBack", "title", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "container", "justifyContent", "lg", "sm", "xs", "fullWidth", "label", "sx", "mb", "value", "onChange", "handleChange", "error", "touched", "Boolean", "errors", "helpertext", "color", "variant", "_c", "$RefreshReg$"], "sources": ["E:/Suraj Patil/Organization_Management_System/meraki-frontend-main/src/screens/Department/Form.js"], "sourcesContent": ["import React, {useEffect} from \"react\";\r\nimport {Box, Button, Card, Grid} from \"@mui/material\";\r\nimport PageTitle from \"components/PageTitle\";\r\nimport * as yup from \"yup\";\r\nimport {useFormik} from \"formik\";\r\nimport {useDispatch, useSelector} from \"react-redux\";\r\nimport {useParams} from \"react-router-dom\";\r\nimport {toast} from \"react-toastify\";\r\nimport {DepartmentSelector, GeneralSelector} from \"selectors\";\r\nimport {DepartmentActions, GeneralActions} from \"slices/actions\";\r\nimport Input from \"components/Input\";\r\nimport FormSkeleton from \"../../components/Skeleton/FormSkeleton\";\r\n\r\nexport default function FormDepartment() {\r\n    const dispatch = useDispatch();\r\n    const { id } = useParams();\r\n    const department = useSelector(DepartmentSelector.getDepartmentById());\r\n    const loading = useSelector(GeneralSelector.loader(DepartmentActions.getDepartmentById.type));\r\n    const actions = [\r\n        DepartmentActions.createDepartment.type,\r\n        DepartmentActions.updateDepartment.type\r\n    ];\r\n    const success = useSelector(GeneralSelector.success(actions));\r\n\r\n    useEffect(() => {\r\n        if (id) {\r\n            dispatch(DepartmentActions.getDepartmentById(id));\r\n        }\r\n    }, []);\r\n\r\n    useEffect(() => {\r\n        if (success.length > 0) {\r\n            const action = success.find(item => actions.includes(item.action));\r\n\r\n            toast.success(`${action?.message ?? \"Success\"}`, {\r\n                    position: \"top-right\",\r\n                    autoClose: 3000,\r\n                    closeOnClick: true,\r\n                    pauseOnHover: false,\r\n                    pauseOnFocusLoss: false\r\n                });\r\n\r\n            dispatch(GeneralActions.removeSuccess(actions));\r\n        }\r\n    }, [success]);\r\n\r\n    const validationSchema = yup.object({\r\n        name: yup.\r\n            string().\r\n            required('Name is required')\r\n    });\r\n\r\n    const formik = useFormik({\r\n        initialValues: {\r\n            name: department?.name ?? \"\"\r\n        },\r\n        enableReinitialize: true,\r\n        validationSchema: validationSchema,\r\n        onSubmit: (values) => {\r\n            handleSubmit(values);\r\n        },\r\n    });\r\n\r\n    const handleSubmit = (params) => {\r\n        if (id) {\r\n            dispatch(DepartmentActions.updateDepartment({\r\n                ...params,\r\n                id\r\n            }));\r\n        } else {\r\n            dispatch(DepartmentActions.createDepartment(params));\r\n        }\r\n    };\r\n\r\n    return (\r\n        <Box>\r\n            <PageTitle isBack={true} title={`${id ? \"Update\" : \"Create\"} Department`}/>\r\n\r\n            {loading ? (\r\n                <FormSkeleton/>\r\n            ) : (\r\n                <Grid container justifyContent=\"center\">\r\n                    <Grid item lg={6} sm={12} xs={12}>\r\n                        <Card>\r\n                            <form onSubmit={formik.handleSubmit}>\r\n                                <Input\r\n                                    fullWidth\r\n                                    label=\"Name\"\r\n                                    sx={{\r\n                                        mb: 2\r\n                                    }}\r\n                                    name=\"name\"\r\n                                    value={formik.values.name}\r\n                                    onChange={formik.handleChange}\r\n                                    error={formik.touched.name && Boolean(formik.errors.name)}\r\n                                    helpertext={formik.touched.name ? formik.errors.name : \"\"}/>\r\n                                <Grid container justifyContent=\"flex-end\">\r\n                                    <Button\r\n                                        color=\"primary\"\r\n                                        variant=\"contained\"\r\n                                        type=\"submit\">\r\n                                        Submit\r\n                                    </Button>\r\n                                </Grid>\r\n                            </form>\r\n                        </Card>\r\n                    </Grid>\r\n                </Grid>\r\n            )}\r\n        </Box>\r\n    )\r\n}"], "mappings": ";;AAAA,OAAOA,KAAK,IAAGC,SAAS,QAAO,OAAO;AACtC,SAAQC,GAAG,EAAEC,MAAM,EAAEC,IAAI,EAAEC,IAAI,QAAO,eAAe;AACrD,OAAOC,SAAS,MAAM,sBAAsB;AAC5C,OAAO,KAAKC,GAAG,MAAM,KAAK;AAC1B,SAAQC,SAAS,QAAO,QAAQ;AAChC,SAAQC,WAAW,EAAEC,WAAW,QAAO,aAAa;AACpD,SAAQC,SAAS,QAAO,kBAAkB;AAC1C,SAAQC,KAAK,QAAO,gBAAgB;AACpC,SAAQC,kBAAkB,EAAEC,eAAe,QAAO,WAAW;AAC7D,SAAQC,iBAAiB,EAAEC,cAAc,QAAO,gBAAgB;AAChE,OAAOC,KAAK,MAAM,kBAAkB;AACpC,OAAOC,YAAY,MAAM,wCAAwC;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAElE,eAAe,SAASC,cAAcA,CAAA,EAAG;EAAAC,EAAA;EAAA,IAAAC,gBAAA;EACrC,MAAMC,QAAQ,GAAGf,WAAW,CAAC,CAAC;EAC9B,MAAM;IAAEgB;EAAG,CAAC,GAAGd,SAAS,CAAC,CAAC;EAC1B,MAAMe,UAAU,GAAGhB,WAAW,CAACG,kBAAkB,CAACc,iBAAiB,CAAC,CAAC,CAAC;EACtE,MAAMC,OAAO,GAAGlB,WAAW,CAACI,eAAe,CAACe,MAAM,CAACd,iBAAiB,CAACY,iBAAiB,CAACG,IAAI,CAAC,CAAC;EAC7F,MAAMC,OAAO,GAAG,CACZhB,iBAAiB,CAACiB,gBAAgB,CAACF,IAAI,EACvCf,iBAAiB,CAACkB,gBAAgB,CAACH,IAAI,CAC1C;EACD,MAAMI,OAAO,GAAGxB,WAAW,CAACI,eAAe,CAACoB,OAAO,CAACH,OAAO,CAAC,CAAC;EAE7D9B,SAAS,CAAC,MAAM;IACZ,IAAIwB,EAAE,EAAE;MACJD,QAAQ,CAACT,iBAAiB,CAACY,iBAAiB,CAACF,EAAE,CAAC,CAAC;IACrD;EACJ,CAAC,EAAE,EAAE,CAAC;EAENxB,SAAS,CAAC,MAAM;IACZ,IAAIiC,OAAO,CAACC,MAAM,GAAG,CAAC,EAAE;MAAA,IAAAC,eAAA;MACpB,MAAMC,MAAM,GAAGH,OAAO,CAACI,IAAI,CAACC,IAAI,IAAIR,OAAO,CAACS,QAAQ,CAACD,IAAI,CAACF,MAAM,CAAC,CAAC;MAElEzB,KAAK,CAACsB,OAAO,CAAC,IAAAE,eAAA,GAAGC,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAEI,OAAO,cAAAL,eAAA,cAAAA,eAAA,GAAI,SAAS,EAAE,EAAE;QACzCM,QAAQ,EAAE,WAAW;QACrBC,SAAS,EAAE,IAAI;QACfC,YAAY,EAAE,IAAI;QAClBC,YAAY,EAAE,KAAK;QACnBC,gBAAgB,EAAE;MACtB,CAAC,CAAC;MAENtB,QAAQ,CAACR,cAAc,CAAC+B,aAAa,CAAChB,OAAO,CAAC,CAAC;IACnD;EACJ,CAAC,EAAE,CAACG,OAAO,CAAC,CAAC;EAEb,MAAMc,gBAAgB,GAAGzC,GAAG,CAAC0C,MAAM,CAAC;IAChCC,IAAI,EAAE3C,GAAG,CACL4C,MAAM,CAAC,CAAC,CACRC,QAAQ,CAAC,kBAAkB;EACnC,CAAC,CAAC;EAEF,MAAMC,MAAM,GAAG7C,SAAS,CAAC;IACrB8C,aAAa,EAAE;MACXJ,IAAI,GAAA3B,gBAAA,GAAEG,UAAU,aAAVA,UAAU,uBAAVA,UAAU,CAAEwB,IAAI,cAAA3B,gBAAA,cAAAA,gBAAA,GAAI;IAC9B,CAAC;IACDgC,kBAAkB,EAAE,IAAI;IACxBP,gBAAgB,EAAEA,gBAAgB;IAClCQ,QAAQ,EAAGC,MAAM,IAAK;MAClBC,YAAY,CAACD,MAAM,CAAC;IACxB;EACJ,CAAC,CAAC;EAEF,MAAMC,YAAY,GAAIC,MAAM,IAAK;IAC7B,IAAIlC,EAAE,EAAE;MACJD,QAAQ,CAACT,iBAAiB,CAACkB,gBAAgB,CAAC;QACxC,GAAG0B,MAAM;QACTlC;MACJ,CAAC,CAAC,CAAC;IACP,CAAC,MAAM;MACHD,QAAQ,CAACT,iBAAiB,CAACiB,gBAAgB,CAAC2B,MAAM,CAAC,CAAC;IACxD;EACJ,CAAC;EAED,oBACIvC,OAAA,CAAClB,GAAG;IAAA0D,QAAA,gBACAxC,OAAA,CAACd,SAAS;MAACuD,MAAM,EAAE,IAAK;MAACC,KAAK,EAAE,GAAGrC,EAAE,GAAG,QAAQ,GAAG,QAAQ;IAAc;MAAAsC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAC,CAAC,EAE1EtC,OAAO,gBACJR,OAAA,CAACF,YAAY;MAAA6C,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAC,CAAC,gBAEf9C,OAAA,CAACf,IAAI;MAAC8D,SAAS;MAACC,cAAc,EAAC,QAAQ;MAAAR,QAAA,eACnCxC,OAAA,CAACf,IAAI;QAACkC,IAAI;QAAC8B,EAAE,EAAE,CAAE;QAACC,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,EAAG;QAAAX,QAAA,eAC7BxC,OAAA,CAAChB,IAAI;UAAAwD,QAAA,eACDxC,OAAA;YAAMoC,QAAQ,EAAEH,MAAM,CAACK,YAAa;YAAAE,QAAA,gBAChCxC,OAAA,CAACH,KAAK;cACFuD,SAAS;cACTC,KAAK,EAAC,MAAM;cACZC,EAAE,EAAE;gBACAC,EAAE,EAAE;cACR,CAAE;cACFzB,IAAI,EAAC,MAAM;cACX0B,KAAK,EAAEvB,MAAM,CAACI,MAAM,CAACP,IAAK;cAC1B2B,QAAQ,EAAExB,MAAM,CAACyB,YAAa;cAC9BC,KAAK,EAAE1B,MAAM,CAAC2B,OAAO,CAAC9B,IAAI,IAAI+B,OAAO,CAAC5B,MAAM,CAAC6B,MAAM,CAAChC,IAAI,CAAE;cAC1DiC,UAAU,EAAE9B,MAAM,CAAC2B,OAAO,CAAC9B,IAAI,GAAGG,MAAM,CAAC6B,MAAM,CAAChC,IAAI,GAAG;YAAG;cAAAa,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAC,CAAC,eAChE9C,OAAA,CAACf,IAAI;cAAC8D,SAAS;cAACC,cAAc,EAAC,UAAU;cAAAR,QAAA,eACrCxC,OAAA,CAACjB,MAAM;gBACHiF,KAAK,EAAC,SAAS;gBACfC,OAAO,EAAC,WAAW;gBACnBvD,IAAI,EAAC,QAAQ;gBAAA8B,QAAA,EAAC;cAElB;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACP,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CACT;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACA,CAAC;AAEd;AAAC5C,EAAA,CAlGuBD,cAAc;EAAA,QACjBZ,WAAW,EACbE,SAAS,EACLD,WAAW,EACdA,WAAW,EAKXA,WAAW,EA8BZF,SAAS;AAAA;AAAA8E,EAAA,GAvCJjE,cAAc;AAAA,IAAAiE,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}