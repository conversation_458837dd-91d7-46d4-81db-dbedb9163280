{"ast": null, "code": "import { SprintService } from \"../services/SprintService\";\nimport { SprintActions, GeneralActions } from \"../slices/actions\";\nimport { all, call, put, takeLatest } from 'redux-saga/effects';\n\n/**\r\n * Get sprint details by ID\r\n */\nfunction* getSprintById({\n  type,\n  payload\n}) {\n  try {\n    yield put(GeneralActions.removeError(type));\n    yield put(GeneralActions.startLoading(type));\n\n    // console.log(\"Getting sprint details for:\", payload.id);\n    const result = yield call(SprintService.getSprintById, payload.id);\n    yield put(SprintActions.getSuccessfullySprintById(result));\n    yield put(GeneralActions.stopLoading(type));\n  } catch (err) {\n    // console.error(\"Error in getSprintById saga:\", err);\n    yield put(GeneralActions.stopLoading(type));\n    yield put(GeneralActions.addError({\n      action: type,\n      message: err.message || \"Failed to get sprint details\"\n    }));\n  }\n}\n\n/**\r\n * Get all sprints\r\n */\nfunction* getSprints({\n  type,\n  payload\n}) {\n  try {\n    yield put(GeneralActions.removeError(type));\n    yield put(GeneralActions.startLoading(type));\n\n    // console.log(\"Getting sprints with filter:\", payload);\n    const result = yield call(SprintService.getSprints, payload);\n    yield put(SprintActions.getSuccessfullySprints(result));\n    yield put(GeneralActions.stopLoading(type));\n  } catch (err) {\n    // console.error(\"Error in getSprints saga:\", err);\n    yield put(GeneralActions.stopLoading(type));\n    yield put(GeneralActions.addError({\n      action: type,\n      message: err.message || \"Failed to get sprints\"\n    }));\n  }\n}\n\n/**\r\n * Create a new sprint\r\n */\nfunction* createSprint({\n  type,\n  payload\n}) {\n  try {\n    yield put(GeneralActions.removeError(type));\n    yield put(GeneralActions.startLoading(type));\n\n    // console.log(\"Creating sprint:\", payload);\n    yield call(SprintService.createSprint, payload);\n\n    // Refresh sprints list\n    const result = yield call(SprintService.getSprints);\n    yield put(SprintActions.getSuccessfullySprints(result));\n    yield put(GeneralActions.stopLoading(type));\n  } catch (err) {\n    // console.error(\"Error in createSprint saga:\", err);\n    yield put(GeneralActions.stopLoading(type));\n    yield put(GeneralActions.addError({\n      action: type,\n      message: err.message || \"Failed to create sprint\"\n    }));\n  }\n}\n\n/**\r\n * Update an existing sprint\r\n */\nfunction* updateSprint({\n  type,\n  payload\n}) {\n  try {\n    yield put(GeneralActions.removeError(type));\n    yield put(GeneralActions.startLoading(type));\n\n    // console.log(\"Updating sprint:\", payload);\n    yield call(SprintService.updateSprint, payload.id, payload);\n\n    // Refresh sprints list\n    const result = yield call(SprintService.getSprints);\n    yield put(SprintActions.getSuccessfullySprints(result));\n    yield put(GeneralActions.stopLoading(type));\n  } catch (err) {\n    // console.error(\"Error in updateSprint saga:\", err);\n    yield put(GeneralActions.stopLoading(type));\n    yield put(GeneralActions.addError({\n      action: type,\n      message: err.message || \"Failed to update sprint\"\n    }));\n  }\n}\n\n/**\r\n * Sprint saga watcher\r\n */\nexport function* SprintWatcher() {\n  yield all([yield takeLatest(SprintActions.getSprintById.type, getSprintById), yield takeLatest(SprintActions.getSprints.type, getSprints), yield takeLatest(SprintActions.createSprint.type, createSprint), yield takeLatest(SprintActions.updateSprint.type, updateSprint)]);\n}\n_c = SprintWatcher;\nvar _c;\n$RefreshReg$(_c, \"SprintWatcher\");", "map": {"version": 3, "names": ["SprintService", "SprintActions", "GeneralActions", "all", "call", "put", "take<PERSON><PERSON>t", "getSprintById", "type", "payload", "removeError", "startLoading", "result", "id", "getSuccessfullySprintById", "stopLoading", "err", "addError", "action", "message", "getSprints", "getSuccessfullySprints", "createSprint", "updateSprint", "SprintWatcher", "_c", "$RefreshReg$"], "sources": ["E:/Suraj Patil/Organization_Management_System/meraki-frontend-main/src/sagas/SprintSaga.js"], "sourcesContent": ["import { SprintService } from \"../services/SprintService\";\r\nimport { SprintActions, GeneralActions } from \"../slices/actions\";\r\nimport { all, call, put, takeLatest } from 'redux-saga/effects';\r\n\r\n/**\r\n * Get sprint details by ID\r\n */\r\nfunction *getSprintById({ type, payload }) {\r\n  try {\r\n    yield put(GeneralActions.removeError(type));\r\n    yield put(GeneralActions.startLoading(type));\r\n    \r\n    // console.log(\"Getting sprint details for:\", payload.id);\r\n    const result = yield call(SprintService.getSprintById, payload.id);\r\n    \r\n    yield put(SprintActions.getSuccessfullySprintById(result));\r\n    yield put(GeneralActions.stopLoading(type));\r\n  } catch (err) {\r\n    // console.error(\"Error in getSprintById saga:\", err);\r\n    yield put(GeneralActions.stopLoading(type));\r\n    yield put(GeneralActions.addError({\r\n      action: type,\r\n      message: err.message || \"Failed to get sprint details\"\r\n    }));\r\n  }\r\n}\r\n\r\n/**\r\n * Get all sprints\r\n */\r\nfunction *getSprints({ type, payload }) {\r\n  try {\r\n    yield put(GeneralActions.removeError(type));\r\n    yield put(GeneralActions.startLoading(type));\r\n    \r\n    // console.log(\"Getting sprints with filter:\", payload);\r\n    const result = yield call(SprintService.getSprints, payload);\r\n    \r\n    yield put(SprintActions.getSuccessfullySprints(result));\r\n    yield put(GeneralActions.stopLoading(type));\r\n  } catch (err) {\r\n    // console.error(\"Error in getSprints saga:\", err);\r\n    yield put(GeneralActions.stopLoading(type));\r\n    yield put(GeneralActions.addError({\r\n      action: type,\r\n      message: err.message || \"Failed to get sprints\"\r\n    }));\r\n  }\r\n}\r\n\r\n/**\r\n * Create a new sprint\r\n */\r\nfunction *createSprint({ type, payload }) {\r\n  try {\r\n    yield put(GeneralActions.removeError(type));\r\n    yield put(GeneralActions.startLoading(type));\r\n    \r\n    // console.log(\"Creating sprint:\", payload);\r\n    yield call(SprintService.createSprint, payload);\r\n    \r\n    // Refresh sprints list\r\n    const result = yield call(SprintService.getSprints);\r\n    yield put(SprintActions.getSuccessfullySprints(result));\r\n    \r\n    yield put(GeneralActions.stopLoading(type));\r\n  } catch (err) {\r\n    // console.error(\"Error in createSprint saga:\", err);\r\n    yield put(GeneralActions.stopLoading(type));\r\n    yield put(GeneralActions.addError({\r\n      action: type,\r\n      message: err.message || \"Failed to create sprint\"\r\n    }));\r\n  }\r\n}\r\n\r\n/**\r\n * Update an existing sprint\r\n */\r\nfunction *updateSprint({ type, payload }) {\r\n  try {\r\n    yield put(GeneralActions.removeError(type));\r\n    yield put(GeneralActions.startLoading(type));\r\n    \r\n    // console.log(\"Updating sprint:\", payload);\r\n    yield call(SprintService.updateSprint, payload.id, payload);\r\n    \r\n    // Refresh sprints list\r\n    const result = yield call(SprintService.getSprints);\r\n    yield put(SprintActions.getSuccessfullySprints(result));\r\n    \r\n    yield put(GeneralActions.stopLoading(type));\r\n  } catch (err) {\r\n    // console.error(\"Error in updateSprint saga:\", err);\r\n    yield put(GeneralActions.stopLoading(type));\r\n    yield put(GeneralActions.addError({\r\n      action: type,\r\n      message: err.message || \"Failed to update sprint\"\r\n    }));\r\n  }\r\n}\r\n\r\n/**\r\n * Sprint saga watcher\r\n */\r\nexport function *SprintWatcher() {\r\n  yield all([\r\n    yield takeLatest(SprintActions.getSprintById.type, getSprintById),\r\n    yield takeLatest(SprintActions.getSprints.type, getSprints),\r\n    yield takeLatest(SprintActions.createSprint.type, createSprint),\r\n    yield takeLatest(SprintActions.updateSprint.type, updateSprint)\r\n  ]);\r\n}"], "mappings": "AAAA,SAASA,aAAa,QAAQ,2BAA2B;AACzD,SAASC,aAAa,EAAEC,cAAc,QAAQ,mBAAmB;AACjE,SAASC,GAAG,EAAEC,IAAI,EAAEC,GAAG,EAAEC,UAAU,QAAQ,oBAAoB;;AAE/D;AACA;AACA;AACA,UAAUC,aAAaA,CAAC;EAAEC,IAAI;EAAEC;AAAQ,CAAC,EAAE;EACzC,IAAI;IACF,MAAMJ,GAAG,CAACH,cAAc,CAACQ,WAAW,CAACF,IAAI,CAAC,CAAC;IAC3C,MAAMH,GAAG,CAACH,cAAc,CAACS,YAAY,CAACH,IAAI,CAAC,CAAC;;IAE5C;IACA,MAAMI,MAAM,GAAG,MAAMR,IAAI,CAACJ,aAAa,CAACO,aAAa,EAAEE,OAAO,CAACI,EAAE,CAAC;IAElE,MAAMR,GAAG,CAACJ,aAAa,CAACa,yBAAyB,CAACF,MAAM,CAAC,CAAC;IAC1D,MAAMP,GAAG,CAACH,cAAc,CAACa,WAAW,CAACP,IAAI,CAAC,CAAC;EAC7C,CAAC,CAAC,OAAOQ,GAAG,EAAE;IACZ;IACA,MAAMX,GAAG,CAACH,cAAc,CAACa,WAAW,CAACP,IAAI,CAAC,CAAC;IAC3C,MAAMH,GAAG,CAACH,cAAc,CAACe,QAAQ,CAAC;MAChCC,MAAM,EAAEV,IAAI;MACZW,OAAO,EAAEH,GAAG,CAACG,OAAO,IAAI;IAC1B,CAAC,CAAC,CAAC;EACL;AACF;;AAEA;AACA;AACA;AACA,UAAUC,UAAUA,CAAC;EAAEZ,IAAI;EAAEC;AAAQ,CAAC,EAAE;EACtC,IAAI;IACF,MAAMJ,GAAG,CAACH,cAAc,CAACQ,WAAW,CAACF,IAAI,CAAC,CAAC;IAC3C,MAAMH,GAAG,CAACH,cAAc,CAACS,YAAY,CAACH,IAAI,CAAC,CAAC;;IAE5C;IACA,MAAMI,MAAM,GAAG,MAAMR,IAAI,CAACJ,aAAa,CAACoB,UAAU,EAAEX,OAAO,CAAC;IAE5D,MAAMJ,GAAG,CAACJ,aAAa,CAACoB,sBAAsB,CAACT,MAAM,CAAC,CAAC;IACvD,MAAMP,GAAG,CAACH,cAAc,CAACa,WAAW,CAACP,IAAI,CAAC,CAAC;EAC7C,CAAC,CAAC,OAAOQ,GAAG,EAAE;IACZ;IACA,MAAMX,GAAG,CAACH,cAAc,CAACa,WAAW,CAACP,IAAI,CAAC,CAAC;IAC3C,MAAMH,GAAG,CAACH,cAAc,CAACe,QAAQ,CAAC;MAChCC,MAAM,EAAEV,IAAI;MACZW,OAAO,EAAEH,GAAG,CAACG,OAAO,IAAI;IAC1B,CAAC,CAAC,CAAC;EACL;AACF;;AAEA;AACA;AACA;AACA,UAAUG,YAAYA,CAAC;EAAEd,IAAI;EAAEC;AAAQ,CAAC,EAAE;EACxC,IAAI;IACF,MAAMJ,GAAG,CAACH,cAAc,CAACQ,WAAW,CAACF,IAAI,CAAC,CAAC;IAC3C,MAAMH,GAAG,CAACH,cAAc,CAACS,YAAY,CAACH,IAAI,CAAC,CAAC;;IAE5C;IACA,MAAMJ,IAAI,CAACJ,aAAa,CAACsB,YAAY,EAAEb,OAAO,CAAC;;IAE/C;IACA,MAAMG,MAAM,GAAG,MAAMR,IAAI,CAACJ,aAAa,CAACoB,UAAU,CAAC;IACnD,MAAMf,GAAG,CAACJ,aAAa,CAACoB,sBAAsB,CAACT,MAAM,CAAC,CAAC;IAEvD,MAAMP,GAAG,CAACH,cAAc,CAACa,WAAW,CAACP,IAAI,CAAC,CAAC;EAC7C,CAAC,CAAC,OAAOQ,GAAG,EAAE;IACZ;IACA,MAAMX,GAAG,CAACH,cAAc,CAACa,WAAW,CAACP,IAAI,CAAC,CAAC;IAC3C,MAAMH,GAAG,CAACH,cAAc,CAACe,QAAQ,CAAC;MAChCC,MAAM,EAAEV,IAAI;MACZW,OAAO,EAAEH,GAAG,CAACG,OAAO,IAAI;IAC1B,CAAC,CAAC,CAAC;EACL;AACF;;AAEA;AACA;AACA;AACA,UAAUI,YAAYA,CAAC;EAAEf,IAAI;EAAEC;AAAQ,CAAC,EAAE;EACxC,IAAI;IACF,MAAMJ,GAAG,CAACH,cAAc,CAACQ,WAAW,CAACF,IAAI,CAAC,CAAC;IAC3C,MAAMH,GAAG,CAACH,cAAc,CAACS,YAAY,CAACH,IAAI,CAAC,CAAC;;IAE5C;IACA,MAAMJ,IAAI,CAACJ,aAAa,CAACuB,YAAY,EAAEd,OAAO,CAACI,EAAE,EAAEJ,OAAO,CAAC;;IAE3D;IACA,MAAMG,MAAM,GAAG,MAAMR,IAAI,CAACJ,aAAa,CAACoB,UAAU,CAAC;IACnD,MAAMf,GAAG,CAACJ,aAAa,CAACoB,sBAAsB,CAACT,MAAM,CAAC,CAAC;IAEvD,MAAMP,GAAG,CAACH,cAAc,CAACa,WAAW,CAACP,IAAI,CAAC,CAAC;EAC7C,CAAC,CAAC,OAAOQ,GAAG,EAAE;IACZ;IACA,MAAMX,GAAG,CAACH,cAAc,CAACa,WAAW,CAACP,IAAI,CAAC,CAAC;IAC3C,MAAMH,GAAG,CAACH,cAAc,CAACe,QAAQ,CAAC;MAChCC,MAAM,EAAEV,IAAI;MACZW,OAAO,EAAEH,GAAG,CAACG,OAAO,IAAI;IAC1B,CAAC,CAAC,CAAC;EACL;AACF;;AAEA;AACA;AACA;AACA,OAAO,UAAUK,aAAaA,CAAA,EAAG;EAC/B,MAAMrB,GAAG,CAAC,CACR,MAAMG,UAAU,CAACL,aAAa,CAACM,aAAa,CAACC,IAAI,EAAED,aAAa,CAAC,EACjE,MAAMD,UAAU,CAACL,aAAa,CAACmB,UAAU,CAACZ,IAAI,EAAEY,UAAU,CAAC,EAC3D,MAAMd,UAAU,CAACL,aAAa,CAACqB,YAAY,CAACd,IAAI,EAAEc,YAAY,CAAC,EAC/D,MAAMhB,UAAU,CAACL,aAAa,CAACsB,YAAY,CAACf,IAAI,EAAEe,YAAY,CAAC,CAChE,CAAC;AACJ;AAACE,EAAA,GAPgBD,aAAa;AAAA,IAAAC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}