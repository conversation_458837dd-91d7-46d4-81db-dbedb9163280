{"ast": null, "code": "import { ActivityService } from \"services/ActivityService\";\nimport { ActivityActions, GeneralActions } from \"../slices/actions\";\nimport { all, call, put, takeLatest } from 'redux-saga/effects';\nfunction* createTodayGoal({\n  type,\n  payload\n}) {\n  try {\n    yield put(GeneralActions.removeError(type));\n    yield put(GeneralActions.startLoading(type));\n    console.warn(\"Create Today Goal\", payload);\n    yield call(ActivityService.createTodayGoal, payload);\n    const resultHis = yield call(ActivityService.getUserActivityHistory, payload.id);\n    const data = yield resultHis.json();\n    console.warn(\"Create Today Goal with data \", data);\n    yield put(ActivityActions.getUserActivitySuccessfull(data));\n    yield put(GeneralActions.stopLoading(type));\n  } catch (err) {\n    var _err$response, _err$response$data;\n    yield put(GeneralActions.stopLoading(type));\n    yield put(GeneralActions.addError({\n      action: type,\n      message: (_err$response = err.response) === null || _err$response === void 0 ? void 0 : (_err$response$data = _err$response.data) === null || _err$response$data === void 0 ? void 0 : _err$response$data.error\n    }));\n  }\n}\nfunction* getUserActivityHistory({\n  type,\n  payload\n}) {\n  try {\n    yield put(GeneralActions.removeError(type));\n    yield put(GeneralActions.startLoading(type));\n\n    // Check if payload has date range or user ID\n    if (payload && payload.startDate && payload.endDate) {\n      // This is a date range request for multiple users\n      const result = yield call(ActivityService.getUserActivity, payload);\n      const data = yield result.json();\n      yield put(ActivityActions.getUserActivitySuccessfull(data));\n    } else if (payload && payload.id) {\n      // This is a single user history request\n      const result = yield call(ActivityService.getUserActivityHistory, payload.id);\n      const data = yield result.json();\n      yield put(ActivityActions.getUserActivitySuccessfull(data));\n    }\n    yield put(GeneralActions.stopLoading(type));\n  } catch (err) {\n    var _err$response2, _err$response2$data;\n    yield put(GeneralActions.stopLoading(type));\n    yield put(GeneralActions.addError({\n      action: type,\n      message: (_err$response2 = err.response) === null || _err$response2 === void 0 ? void 0 : (_err$response2$data = _err$response2.data) === null || _err$response2$data === void 0 ? void 0 : _err$response2$data.error\n    }));\n  }\n}\nfunction* updateCheckOutStatus({\n  type,\n  payload\n}) {\n  try {\n    yield put(GeneralActions.removeError(type));\n    yield put(GeneralActions.startLoading(type));\n    console.warn(\"payload check out \", payload);\n\n    // Call the checkout API\n    const result = yield call(ActivityService.updateCheckOutStatus, payload);\n    const data = yield result.json();\n\n    // Store the status data in the success object for the UI to access\n    if (data && data.data) {\n      yield put(GeneralActions.addSuccess({\n        action: type,\n        message: data.msg,\n        data: data.data // Include the status data\n      }));\n    }\n\n    // Update activity history\n    const result1 = yield call(ActivityService.getUserActivityHistory, payload.user);\n    const data1 = yield result1.json();\n    console.warn(\"activity history updated\", data1);\n    yield put(ActivityActions.getUserActivitySuccessfull(data1));\n    yield put(GeneralActions.stopLoading(type));\n  } catch (err) {\n    var _err$response3, _err$response3$data;\n    yield put(GeneralActions.stopLoading(type));\n    yield put(GeneralActions.addError({\n      action: type,\n      message: (_err$response3 = err.response) === null || _err$response3 === void 0 ? void 0 : (_err$response3$data = _err$response3.data) === null || _err$response3$data === void 0 ? void 0 : _err$response3$data.error\n    }));\n  }\n}\nfunction* updateBreakInStatus({\n  type,\n  payload\n}) {\n  try {\n    yield put(GeneralActions.removeError(type));\n    yield put(GeneralActions.startLoading(type));\n    console.warn(\"payload break in \", payload);\n    yield call(ActivityService.updateBreakInStatus, payload);\n    const resultHis = yield call(ActivityService.getUserActivityHistory, payload.user);\n    const data1 = yield resultHis.json();\n    console.log(\"Break in SAGA \", data1);\n    yield put(ActivityActions.getUserActivitySuccessfull(data1));\n    yield put(GeneralActions.stopLoading(type));\n  } catch (err) {\n    var _err$response4, _err$response4$data;\n    yield put(GeneralActions.stopLoading(type));\n    yield put(GeneralActions.addError({\n      action: type,\n      message: (_err$response4 = err.response) === null || _err$response4 === void 0 ? void 0 : (_err$response4$data = _err$response4.data) === null || _err$response4$data === void 0 ? void 0 : _err$response4$data.error\n    }));\n  }\n}\nfunction* updateBreakOutStatus({\n  type,\n  payload\n}) {\n  try {\n    yield put(GeneralActions.removeError(type));\n    yield put(GeneralActions.startLoading(type));\n\n    // Perform the break out status update\n    yield call(ActivityService.updateBreakOutStatus, payload);\n\n    // Fetch the updated user activity history\n    console.warn(\"Payload Break Out \", payload);\n    const resultHis = yield call(ActivityService.getUserActivityHistory, payload.user);\n    const data1 = yield resultHis.json();\n    console.warn(\" Updated Break Out Saga \", data1);\n    // Dispatch the updated user activity history\n    yield put(ActivityActions.getUserActivitySuccessfull(data1));\n    yield put(GeneralActions.stopLoading(type));\n  } catch (err) {\n    var _err$response5, _err$response5$data;\n    const errorMessage = ((_err$response5 = err.response) === null || _err$response5 === void 0 ? void 0 : (_err$response5$data = _err$response5.data) === null || _err$response5$data === void 0 ? void 0 : _err$response5$data.error) || \"Unknown error occurred\";\n    yield put(GeneralActions.addError({\n      action: type,\n      message: errorMessage\n    }));\n  } finally {\n    yield put(GeneralActions.stopLoading(type));\n  }\n}\nfunction* updateTodayStatus({\n  type,\n  payload\n}) {\n  try {\n    yield put(GeneralActions.removeError(type));\n    yield put(GeneralActions.startLoading(type));\n    yield call(ActivityService.updateTodayStatus, payload);\n    console.log(\"Today Status Payload \", payload);\n    const result1 = yield call(ActivityService.getUserActivityHistory, payload.user);\n    const data1 = yield result1.json();\n    yield put(ActivityActions.getUserActivitySuccessfull(data1));\n    yield put(GeneralActions.stopLoading(type));\n  } catch (err) {\n    var _err$response6, _err$response6$data;\n    yield put(GeneralActions.stopLoading(type));\n    yield put(GeneralActions.addError({\n      action: type,\n      message: (_err$response6 = err.response) === null || _err$response6 === void 0 ? void 0 : (_err$response6$data = _err$response6.data) === null || _err$response6$data === void 0 ? void 0 : _err$response6$data.error\n    }));\n  }\n}\nfunction* updateLateCheckIn({\n  type,\n  payload\n}) {\n  try {\n    yield put(GeneralActions.removeError(type));\n    yield put(GeneralActions.startLoading(type));\n    console.log(\"Update late check in \", payload._id);\n    yield call(ActivityService.updateLateCheckInStatus, payload);\n    const resultHis = yield call(ActivityService.getUserActivityHistory, payload.user);\n    const data1 = yield resultHis.json();\n    yield put(ActivityActions.getUserActivitySuccessfull(data1));\n    yield put(GeneralActions.stopLoading(type));\n  } catch (err) {\n    var _err$response7, _err$response7$data;\n    yield put(GeneralActions.stopLoading(type));\n    yield put(GeneralActions.addError({\n      action: type,\n      message: (_err$response7 = err.response) === null || _err$response7 === void 0 ? void 0 : (_err$response7$data = _err$response7.data) === null || _err$response7$data === void 0 ? void 0 : _err$response7$data.error\n    }));\n  }\n}\nfunction* updateEarlyCheckOut({\n  type,\n  payload\n}) {\n  try {\n    yield put(GeneralActions.removeError(type));\n    yield put(GeneralActions.startLoading(type));\n    yield call(ActivityService.updateEarlyCheckOutStatus, payload);\n    console.log(\"Update Early  check in \", payload._id);\n    const resultHis = yield call(ActivityService.getUserActivityHistory, payload.user);\n    const data1 = yield resultHis.json();\n    yield put(ActivityActions.getUserActivitySuccessfull(data1));\n    yield put(GeneralActions.stopLoading(type));\n  } catch (err) {\n    var _err$response8, _err$response8$data;\n    yield put(GeneralActions.stopLoading(type));\n    yield put(GeneralActions.addError({\n      action: type,\n      message: (_err$response8 = err.response) === null || _err$response8 === void 0 ? void 0 : (_err$response8$data = _err$response8.data) === null || _err$response8$data === void 0 ? void 0 : _err$response8$data.error\n    }));\n  }\n}\nfunction* updateIdelStart({\n  type,\n  payload\n}) {\n  try {\n    yield put(GeneralActions.removeError(type));\n    yield put(GeneralActions.startLoading(type));\n    yield call(ActivityService.updateIdelStartStatus, payload);\n    // const data = yield result.json()\n    const resultHis = yield call(ActivityService.getUserActivityHistory, payload.user);\n    const data1 = yield resultHis.json();\n    yield put(ActivityActions.getUserActivitySuccessfull(data1));\n    yield put(GeneralActions.stopLoading(type));\n  } catch (err) {\n    var _err$response9, _err$response9$data;\n    yield put(GeneralActions.stopLoading(type));\n    yield put(GeneralActions.addError({\n      action: type,\n      message: (_err$response9 = err.response) === null || _err$response9 === void 0 ? void 0 : (_err$response9$data = _err$response9.data) === null || _err$response9$data === void 0 ? void 0 : _err$response9$data.error\n    }));\n  }\n}\nfunction* updateIdelEnd({\n  type,\n  payload\n}) {\n  try {\n    console.log(\"IDEL END \");\n    yield put(GeneralActions.removeError(type));\n    yield put(GeneralActions.startLoading(type));\n    yield call(ActivityService.updateIdelEndStatus, payload);\n    const resultHis = yield call(ActivityService.getUserActivityHistory, payload.user);\n    const data1 = yield resultHis.json();\n    yield put(ActivityActions.getUserActivitySuccessfull(data1));\n    yield put(GeneralActions.stopLoading(type));\n  } catch (err) {\n    var _err$response0, _err$response0$data;\n    yield put(GeneralActions.stopLoading(type));\n    yield put(GeneralActions.addError({\n      action: type,\n      message: (_err$response0 = err.response) === null || _err$response0 === void 0 ? void 0 : (_err$response0$data = _err$response0.data) === null || _err$response0$data === void 0 ? void 0 : _err$response0$data.error\n    }));\n  }\n}\nfunction* updateProductiityStatus({\n  type,\n  payload\n}) {\n  try {\n    yield put(GeneralActions.removeError(type));\n    yield put(GeneralActions.startLoading(type));\n    console.log(\"Productivity Status \", payload);\n    yield call(ActivityService.updateProductivityStatus, payload);\n    const resultHis = yield call(ActivityService.getUserActivityHistory, payload.user);\n    const data1 = yield resultHis.json();\n    yield put(ActivityActions.getUserActivitySuccessfull(data1));\n    yield put(GeneralActions.stopLoading(type));\n  } catch (err) {\n    var _err$response1, _err$response1$data;\n    yield put(GeneralActions.stopLoading(type));\n    yield put(GeneralActions.addError({\n      action: type,\n      message: (_err$response1 = err.response) === null || _err$response1 === void 0 ? void 0 : (_err$response1$data = _err$response1.data) === null || _err$response1$data === void 0 ? void 0 : _err$response1$data.error\n    }));\n  }\n}\nfunction* overLimitBreakStatus({\n  type,\n  payload\n}) {\n  try {\n    yield put(GeneralActions.removeError(type));\n    yield put(GeneralActions.startLoading(type));\n    // console.log(\"OVER LIMIT STATUS \")\n    yield call(ActivityService.updateOverLimitBreakStatus, payload);\n    yield put(GeneralActions.stopLoading(type));\n  } catch (err) {\n    var _err$response10, _err$response10$data;\n    yield put(GeneralActions.stopLoading(type));\n    yield put(GeneralActions.addError({\n      action: type,\n      message: (_err$response10 = err.response) === null || _err$response10 === void 0 ? void 0 : (_err$response10$data = _err$response10.data) === null || _err$response10$data === void 0 ? void 0 : _err$response10$data.error\n    }));\n  }\n}\nexport function* ActivityWatcher() {\n  yield all([yield takeLatest(ActivityActions.createTodayGoal.type, createTodayGoal), yield takeLatest(ActivityActions.getUserActivity.type, getUserActivityHistory), yield takeLatest(ActivityActions.checkOutStatusUpdate.type, updateCheckOutStatus), yield takeLatest(ActivityActions.breakStartRed.type, updateBreakInStatus), yield takeLatest(ActivityActions.breakEndRed.type, updateBreakOutStatus), yield takeLatest(ActivityActions.createTodayStatus.type, updateTodayStatus), yield takeLatest(ActivityActions.lateCheckIn.type, updateLateCheckIn), yield takeLatest(ActivityActions.earlyCheckOut.type, updateEarlyCheckOut), yield takeLatest(ActivityActions.idelStartRed.type, updateIdelStart), yield takeLatest(ActivityActions.idelEndRed.type, updateIdelEnd), yield takeLatest(ActivityActions.overLimitBreakRed.type, overLimitBreakStatus), yield takeLatest(ActivityActions.productivityStatusRed.type, updateProductiityStatus)]);\n}\n_c = ActivityWatcher;\nvar _c;\n$RefreshReg$(_c, \"ActivityWatcher\");", "map": {"version": 3, "names": ["ActivityService", "ActivityActions", "GeneralActions", "all", "call", "put", "take<PERSON><PERSON>t", "createTodayGoal", "type", "payload", "removeError", "startLoading", "console", "warn", "resultHis", "getUserActivityHistory", "id", "data", "json", "getUserActivitySuccessfull", "stopLoading", "err", "_err$response", "_err$response$data", "addError", "action", "message", "response", "error", "startDate", "endDate", "result", "getUserActivity", "_err$response2", "_err$response2$data", "updateCheckOutStatus", "addSuccess", "msg", "result1", "user", "data1", "_err$response3", "_err$response3$data", "updateBreakInStatus", "log", "_err$response4", "_err$response4$data", "updateBreakOutStatus", "_err$response5", "_err$response5$data", "errorMessage", "updateTodayStatus", "_err$response6", "_err$response6$data", "updateLateCheckIn", "_id", "updateLateCheckInStatus", "_err$response7", "_err$response7$data", "updateEarlyCheckOut", "updateEarlyCheckOutStatus", "_err$response8", "_err$response8$data", "updateIdelStart", "updateIdelStartStatus", "_err$response9", "_err$response9$data", "updateIdelEnd", "updateIdelEndStatus", "_err$response0", "_err$response0$data", "updateProductiityStatus", "updateProductivityStatus", "_err$response1", "_err$response1$data", "overLimitBreakStatus", "updateOverLimitBreakStatus", "_err$response10", "_err$response10$data", "ActivityWatcher", "checkOutStatusUpdate", "breakStartRed", "breakEndRed", "createTodayStatus", "lateCheckIn", "earlyCheckOut", "idelStartRed", "idelEndRed", "overLimitBreakRed", "productivityStatusRed", "_c", "$RefreshReg$"], "sources": ["E:/Suraj Patil/Organization_Management_System/meraki-frontend-main/src/sagas/ActivitySaga.js"], "sourcesContent": ["import { ActivityService } from \"services/ActivityService\";\r\nimport {ActivityActions, GeneralActions} from \"../slices/actions\";\r\nimport {all, call, put, takeLatest} from 'redux-saga/effects'\r\n\r\n\r\nfunction *createTodayGoal({type, payload}) {\r\n    try {\r\n        yield put(GeneralActions.removeError(type));\r\n        yield put(GeneralActions.startLoading(type));\r\n        console.warn(\"Create Today Goal\",payload)\r\n         yield call(ActivityService.createTodayGoal, payload);\r\n        const resultHis = yield call(ActivityService.getUserActivityHistory,payload.id);\r\n        const data = yield resultHis.json()\r\n        console.warn(\"Create Today Goal with data \",data)\r\n        yield put(ActivityActions.getUserActivitySuccessfull(data));\r\n        yield put(GeneralActions.stopLoading(type))\r\n    } catch (err) {\r\n        yield put(GeneralActions.stopLoading(type));\r\n        yield put(GeneralActions.addError({\r\n            action: type,\r\n            message: err.response?.data?.error\r\n        }));\r\n    }\r\n}\r\n\r\nfunction *getUserActivityHistory({type,payload}) {\r\n    try {\r\n        yield put(GeneralActions.removeError(type));\r\n        yield put(GeneralActions.startLoading(type));\r\n        \r\n        // Check if payload has date range or user ID\r\n        if (payload && payload.startDate && payload.endDate) {\r\n            // This is a date range request for multiple users\r\n            const result = yield call(ActivityService.getUserActivity, payload);\r\n            const data = yield result.json();\r\n            yield put(ActivityActions.getUserActivitySuccessfull(data));\r\n        } else if (payload && payload.id) {\r\n            // This is a single user history request\r\n            const result = yield call(ActivityService.getUserActivityHistory, payload.id);\r\n            const data = yield result.json();\r\n            yield put(ActivityActions.getUserActivitySuccessfull(data));\r\n        }\r\n        \r\n        yield put(GeneralActions.stopLoading(type));\r\n    } catch (err) {\r\n        yield put(GeneralActions.stopLoading(type));\r\n        yield put(GeneralActions.addError({\r\n            action: type,\r\n            message: err.response?.data?.error\r\n        }));\r\n    }\r\n}\r\n\r\nfunction *updateCheckOutStatus({type,payload}) {\r\n    try {\r\n        yield put(GeneralActions.removeError(type));\r\n        yield put(GeneralActions.startLoading(type));\r\n        console.warn(\"payload check out \", payload);\r\n\r\n        // Call the checkout API\r\n        const result = yield call(ActivityService.updateCheckOutStatus, payload);\r\n        const data = yield result.json();\r\n\r\n        // Store the status data in the success object for the UI to access\r\n        if (data && data.data) {\r\n            yield put(GeneralActions.addSuccess({\r\n                action: type,\r\n                message: data.msg,\r\n                data: data.data // Include the status data\r\n            }));\r\n        }\r\n\r\n        // Update activity history\r\n        const result1 = yield call(ActivityService.getUserActivityHistory, payload.user);\r\n        const data1 = yield result1.json();\r\n        console.warn(\"activity history updated\", data1);\r\n        yield put(ActivityActions.getUserActivitySuccessfull(data1));\r\n\r\n        yield put(GeneralActions.stopLoading(type));\r\n    } catch (err) {\r\n        yield put(GeneralActions.stopLoading(type));\r\n        yield put(GeneralActions.addError({\r\n            action: type,\r\n            message: err.response?.data?.error\r\n        }));\r\n    }\r\n}\r\n\r\nfunction *updateBreakInStatus({type,payload}) {\r\n    try {\r\n        yield put(GeneralActions.removeError(type));\r\n        yield put(GeneralActions.startLoading(type));\r\n        console.warn(\"payload break in \",payload)\r\n         yield call(ActivityService.updateBreakInStatus,payload);\r\n        \r\n        const resultHis = yield call(ActivityService.getUserActivityHistory,payload.user);\r\n        const data1 = yield resultHis.json()\r\n        console.log(\"Break in SAGA \",data1)\r\n        yield put(ActivityActions.getUserActivitySuccessfull(data1));\r\n        yield put(GeneralActions.stopLoading(type))\r\n    } catch (err) {\r\n        yield put(GeneralActions.stopLoading(type));\r\n        yield put(GeneralActions.addError({\r\n            action: type,\r\n            message: err.response?.data?.error\r\n        }));\r\n    }\r\n}\r\n\r\nfunction *updateBreakOutStatus({ type, payload }) {\r\n    try {\r\n      yield put(GeneralActions.removeError(type));\r\n      yield put(GeneralActions.startLoading(type));\r\n\r\n      // Perform the break out status update\r\n       yield call(ActivityService.updateBreakOutStatus, payload);\r\n\r\n      // Fetch the updated user activity history\r\n      console.warn(\"Payload Break Out \",payload)\r\n      const resultHis = yield call(ActivityService.getUserActivityHistory,payload.user);\r\n      const data1 = yield resultHis.json();\r\n    console.warn(\" Updated Break Out Saga \",data1)\r\n      // Dispatch the updated user activity history\r\n      yield put(ActivityActions.getUserActivitySuccessfull(data1));\r\n      yield put(GeneralActions.stopLoading(type))\r\n    } catch (err) {\r\n      const errorMessage = err.response?.data?.error || \"Unknown error occurred\";\r\n      yield put(GeneralActions.addError({\r\n        action: type,\r\n        message: errorMessage\r\n      }));\r\n    } finally {\r\n      yield put(GeneralActions.stopLoading(type));\r\n    }\r\n  }\r\n\r\nfunction *updateTodayStatus({type,payload}){\r\n    try {\r\n        yield put(GeneralActions.removeError(type));\r\n        yield put(GeneralActions.startLoading(type));\r\n        yield call(ActivityService.updateTodayStatus,payload);\r\n    \r\n        console.log(\"Today Status Payload \",payload)\r\n        const result1 = yield call(ActivityService.getUserActivityHistory,payload.user);\r\n        const data1 = yield result1.json()\r\n         yield put(ActivityActions.getUserActivitySuccessfull(data1));\r\n        yield put(GeneralActions.stopLoading(type))\r\n    } catch (err) {\r\n        yield put(GeneralActions.stopLoading(type));\r\n        yield put(GeneralActions.addError({\r\n            action: type,\r\n            message: err.response?.data?.error\r\n        }));\r\n    }\r\n\r\n}\r\n\r\nfunction *updateLateCheckIn({type,payload}){\r\n    try {\r\n        yield put(GeneralActions.removeError(type));\r\n        yield put(GeneralActions.startLoading(type));\r\n        console.log(\"Update late check in \",payload._id)\r\n         yield call(ActivityService.updateLateCheckInStatus,payload);\r\n \r\n        const resultHis = yield call(ActivityService.getUserActivityHistory,payload.user);\r\n        const data1 = yield resultHis.json()\r\n        yield put(ActivityActions.getUserActivitySuccessfull(data1));\r\n        yield put(GeneralActions.stopLoading(type))\r\n    } catch (err) {\r\n        yield put(GeneralActions.stopLoading(type));\r\n        yield put(GeneralActions.addError({\r\n            action: type,\r\n            message: err.response?.data?.error\r\n        }));\r\n    }\r\n\r\n}\r\n\r\nfunction *updateEarlyCheckOut({type,payload}){\r\n\r\n    try {\r\n        yield put(GeneralActions.removeError(type));\r\n        yield put(GeneralActions.startLoading(type));\r\n         yield call(ActivityService.updateEarlyCheckOutStatus,payload);\r\n\r\n        console.log(\"Update Early  check in \",payload._id)\r\n        const resultHis = yield call(ActivityService.getUserActivityHistory,payload.user);\r\n        const data1 = yield resultHis.json()\r\n        yield put(ActivityActions.getUserActivitySuccessfull(data1));\r\n        yield put(GeneralActions.stopLoading(type))\r\n    } catch (err) {\r\n        yield put(GeneralActions.stopLoading(type));\r\n        yield put(GeneralActions.addError({\r\n            action: type,\r\n            message: err.response?.data?.error\r\n        }));\r\n    }\r\n\r\n}\r\n\r\nfunction *updateIdelStart({type,payload}) {\r\n    try {\r\n        yield put(GeneralActions.removeError(type));\r\n        yield put(GeneralActions.startLoading(type));\r\n         yield call(ActivityService.updateIdelStartStatus,payload);\r\n        // const data = yield result.json()\r\n        const resultHis = yield call(ActivityService.getUserActivityHistory,payload.user);\r\n        const data1 = yield resultHis.json()\r\n        yield put(ActivityActions.getUserActivitySuccessfull(data1));\r\n        yield put(GeneralActions.stopLoading(type))\r\n    } catch (err) {\r\n        yield put(GeneralActions.stopLoading(type));\r\n        yield put(GeneralActions.addError({\r\n            action: type,\r\n            message: err.response?.data?.error\r\n        }));\r\n    }\r\n}\r\n\r\nfunction *updateIdelEnd({type,payload}) {\r\n    try {\r\n        console.log(\"IDEL END \")\r\n        yield put(GeneralActions.removeError(type));\r\n        yield put(GeneralActions.startLoading(type));\r\n         yield call(ActivityService.updateIdelEndStatus,payload);\r\n      \r\n        const resultHis = yield call(ActivityService.getUserActivityHistory,payload.user);\r\n        const data1 = yield resultHis.json()\r\n        yield put(ActivityActions.getUserActivitySuccessfull(data1));\r\n        yield put(GeneralActions.stopLoading(type))\r\n    } catch (err) {\r\n        yield put(GeneralActions.stopLoading(type));\r\n        yield put(GeneralActions.addError({\r\n            action: type,\r\n            message: err.response?.data?.error\r\n        }));\r\n    }\r\n\r\n}\r\n\r\nfunction *updateProductiityStatus({type,payload}) {\r\n    try {\r\n        yield put(GeneralActions.removeError(type));\r\n        yield put(GeneralActions.startLoading(type));\r\n         console.log(\"Productivity Status \",payload)\r\n         yield call(ActivityService.updateProductivityStatus,payload);\r\n    \r\n\r\n        const resultHis = yield call(ActivityService.getUserActivityHistory,payload.user);\r\n        const data1 = yield resultHis.json()\r\n        yield put(ActivityActions.getUserActivitySuccessfull(data1));\r\n        yield put(GeneralActions.stopLoading(type))\r\n    } catch (err) {\r\n        yield put(GeneralActions.stopLoading(type));\r\n        yield put(GeneralActions.addError({\r\n            action: type,\r\n            message: err.response?.data?.error\r\n        }));\r\n    }\r\n}\r\n\r\nfunction *overLimitBreakStatus({type,payload}) {\r\n    try {\r\n        yield put(GeneralActions.removeError(type));\r\n        yield put(GeneralActions.startLoading(type));\r\n        // console.log(\"OVER LIMIT STATUS \")\r\n       yield call(ActivityService.updateOverLimitBreakStatus,payload);\r\n        yield put(GeneralActions.stopLoading(type))\r\n    } catch (err) {\r\n        yield put(GeneralActions.stopLoading(type));\r\n        yield put(GeneralActions.addError({\r\n            action: type,\r\n            message: err.response?.data?.error\r\n        }));\r\n    }\r\n}\r\n\r\n\r\n\r\nexport function *ActivityWatcher() {\r\n    yield all([\r\n        yield takeLatest(ActivityActions.createTodayGoal.type, createTodayGoal),\r\n        yield takeLatest(ActivityActions.getUserActivity.type, getUserActivityHistory),\r\n        yield takeLatest(ActivityActions.checkOutStatusUpdate.type, updateCheckOutStatus),\r\n        yield takeLatest(ActivityActions.breakStartRed.type, updateBreakInStatus),\r\n        yield takeLatest(ActivityActions.breakEndRed.type, updateBreakOutStatus),\r\n        yield takeLatest(ActivityActions.createTodayStatus.type,updateTodayStatus),\r\n        yield takeLatest(ActivityActions.lateCheckIn.type,updateLateCheckIn),\r\n        yield takeLatest(ActivityActions.earlyCheckOut.type,updateEarlyCheckOut),\r\n        yield takeLatest(ActivityActions.idelStartRed.type,updateIdelStart),\r\n        yield takeLatest(ActivityActions.idelEndRed.type,updateIdelEnd),\r\n        yield takeLatest(ActivityActions.overLimitBreakRed.type,overLimitBreakStatus),\r\n        yield takeLatest(ActivityActions.productivityStatusRed.type,updateProductiityStatus)\r\n ]);\r\n}"], "mappings": "AAAA,SAASA,eAAe,QAAQ,0BAA0B;AAC1D,SAAQC,eAAe,EAAEC,cAAc,QAAO,mBAAmB;AACjE,SAAQC,GAAG,EAAEC,IAAI,EAAEC,GAAG,EAAEC,UAAU,QAAO,oBAAoB;AAG7D,UAAUC,eAAeA,CAAC;EAACC,IAAI;EAAEC;AAAO,CAAC,EAAE;EACvC,IAAI;IACA,MAAMJ,GAAG,CAACH,cAAc,CAACQ,WAAW,CAACF,IAAI,CAAC,CAAC;IAC3C,MAAMH,GAAG,CAACH,cAAc,CAACS,YAAY,CAACH,IAAI,CAAC,CAAC;IAC5CI,OAAO,CAACC,IAAI,CAAC,mBAAmB,EAACJ,OAAO,CAAC;IACxC,MAAML,IAAI,CAACJ,eAAe,CAACO,eAAe,EAAEE,OAAO,CAAC;IACrD,MAAMK,SAAS,GAAG,MAAMV,IAAI,CAACJ,eAAe,CAACe,sBAAsB,EAACN,OAAO,CAACO,EAAE,CAAC;IAC/E,MAAMC,IAAI,GAAG,MAAMH,SAAS,CAACI,IAAI,CAAC,CAAC;IACnCN,OAAO,CAACC,IAAI,CAAC,8BAA8B,EAACI,IAAI,CAAC;IACjD,MAAMZ,GAAG,CAACJ,eAAe,CAACkB,0BAA0B,CAACF,IAAI,CAAC,CAAC;IAC3D,MAAMZ,GAAG,CAACH,cAAc,CAACkB,WAAW,CAACZ,IAAI,CAAC,CAAC;EAC/C,CAAC,CAAC,OAAOa,GAAG,EAAE;IAAA,IAAAC,aAAA,EAAAC,kBAAA;IACV,MAAMlB,GAAG,CAACH,cAAc,CAACkB,WAAW,CAACZ,IAAI,CAAC,CAAC;IAC3C,MAAMH,GAAG,CAACH,cAAc,CAACsB,QAAQ,CAAC;MAC9BC,MAAM,EAAEjB,IAAI;MACZkB,OAAO,GAAAJ,aAAA,GAAED,GAAG,CAACM,QAAQ,cAAAL,aAAA,wBAAAC,kBAAA,GAAZD,aAAA,CAAcL,IAAI,cAAAM,kBAAA,uBAAlBA,kBAAA,CAAoBK;IACjC,CAAC,CAAC,CAAC;EACP;AACJ;AAEA,UAAUb,sBAAsBA,CAAC;EAACP,IAAI;EAACC;AAAO,CAAC,EAAE;EAC7C,IAAI;IACA,MAAMJ,GAAG,CAACH,cAAc,CAACQ,WAAW,CAACF,IAAI,CAAC,CAAC;IAC3C,MAAMH,GAAG,CAACH,cAAc,CAACS,YAAY,CAACH,IAAI,CAAC,CAAC;;IAE5C;IACA,IAAIC,OAAO,IAAIA,OAAO,CAACoB,SAAS,IAAIpB,OAAO,CAACqB,OAAO,EAAE;MACjD;MACA,MAAMC,MAAM,GAAG,MAAM3B,IAAI,CAACJ,eAAe,CAACgC,eAAe,EAAEvB,OAAO,CAAC;MACnE,MAAMQ,IAAI,GAAG,MAAMc,MAAM,CAACb,IAAI,CAAC,CAAC;MAChC,MAAMb,GAAG,CAACJ,eAAe,CAACkB,0BAA0B,CAACF,IAAI,CAAC,CAAC;IAC/D,CAAC,MAAM,IAAIR,OAAO,IAAIA,OAAO,CAACO,EAAE,EAAE;MAC9B;MACA,MAAMe,MAAM,GAAG,MAAM3B,IAAI,CAACJ,eAAe,CAACe,sBAAsB,EAAEN,OAAO,CAACO,EAAE,CAAC;MAC7E,MAAMC,IAAI,GAAG,MAAMc,MAAM,CAACb,IAAI,CAAC,CAAC;MAChC,MAAMb,GAAG,CAACJ,eAAe,CAACkB,0BAA0B,CAACF,IAAI,CAAC,CAAC;IAC/D;IAEA,MAAMZ,GAAG,CAACH,cAAc,CAACkB,WAAW,CAACZ,IAAI,CAAC,CAAC;EAC/C,CAAC,CAAC,OAAOa,GAAG,EAAE;IAAA,IAAAY,cAAA,EAAAC,mBAAA;IACV,MAAM7B,GAAG,CAACH,cAAc,CAACkB,WAAW,CAACZ,IAAI,CAAC,CAAC;IAC3C,MAAMH,GAAG,CAACH,cAAc,CAACsB,QAAQ,CAAC;MAC9BC,MAAM,EAAEjB,IAAI;MACZkB,OAAO,GAAAO,cAAA,GAAEZ,GAAG,CAACM,QAAQ,cAAAM,cAAA,wBAAAC,mBAAA,GAAZD,cAAA,CAAchB,IAAI,cAAAiB,mBAAA,uBAAlBA,mBAAA,CAAoBN;IACjC,CAAC,CAAC,CAAC;EACP;AACJ;AAEA,UAAUO,oBAAoBA,CAAC;EAAC3B,IAAI;EAACC;AAAO,CAAC,EAAE;EAC3C,IAAI;IACA,MAAMJ,GAAG,CAACH,cAAc,CAACQ,WAAW,CAACF,IAAI,CAAC,CAAC;IAC3C,MAAMH,GAAG,CAACH,cAAc,CAACS,YAAY,CAACH,IAAI,CAAC,CAAC;IAC5CI,OAAO,CAACC,IAAI,CAAC,oBAAoB,EAAEJ,OAAO,CAAC;;IAE3C;IACA,MAAMsB,MAAM,GAAG,MAAM3B,IAAI,CAACJ,eAAe,CAACmC,oBAAoB,EAAE1B,OAAO,CAAC;IACxE,MAAMQ,IAAI,GAAG,MAAMc,MAAM,CAACb,IAAI,CAAC,CAAC;;IAEhC;IACA,IAAID,IAAI,IAAIA,IAAI,CAACA,IAAI,EAAE;MACnB,MAAMZ,GAAG,CAACH,cAAc,CAACkC,UAAU,CAAC;QAChCX,MAAM,EAAEjB,IAAI;QACZkB,OAAO,EAAET,IAAI,CAACoB,GAAG;QACjBpB,IAAI,EAAEA,IAAI,CAACA,IAAI,CAAC;MACpB,CAAC,CAAC,CAAC;IACP;;IAEA;IACA,MAAMqB,OAAO,GAAG,MAAMlC,IAAI,CAACJ,eAAe,CAACe,sBAAsB,EAAEN,OAAO,CAAC8B,IAAI,CAAC;IAChF,MAAMC,KAAK,GAAG,MAAMF,OAAO,CAACpB,IAAI,CAAC,CAAC;IAClCN,OAAO,CAACC,IAAI,CAAC,0BAA0B,EAAE2B,KAAK,CAAC;IAC/C,MAAMnC,GAAG,CAACJ,eAAe,CAACkB,0BAA0B,CAACqB,KAAK,CAAC,CAAC;IAE5D,MAAMnC,GAAG,CAACH,cAAc,CAACkB,WAAW,CAACZ,IAAI,CAAC,CAAC;EAC/C,CAAC,CAAC,OAAOa,GAAG,EAAE;IAAA,IAAAoB,cAAA,EAAAC,mBAAA;IACV,MAAMrC,GAAG,CAACH,cAAc,CAACkB,WAAW,CAACZ,IAAI,CAAC,CAAC;IAC3C,MAAMH,GAAG,CAACH,cAAc,CAACsB,QAAQ,CAAC;MAC9BC,MAAM,EAAEjB,IAAI;MACZkB,OAAO,GAAAe,cAAA,GAAEpB,GAAG,CAACM,QAAQ,cAAAc,cAAA,wBAAAC,mBAAA,GAAZD,cAAA,CAAcxB,IAAI,cAAAyB,mBAAA,uBAAlBA,mBAAA,CAAoBd;IACjC,CAAC,CAAC,CAAC;EACP;AACJ;AAEA,UAAUe,mBAAmBA,CAAC;EAACnC,IAAI;EAACC;AAAO,CAAC,EAAE;EAC1C,IAAI;IACA,MAAMJ,GAAG,CAACH,cAAc,CAACQ,WAAW,CAACF,IAAI,CAAC,CAAC;IAC3C,MAAMH,GAAG,CAACH,cAAc,CAACS,YAAY,CAACH,IAAI,CAAC,CAAC;IAC5CI,OAAO,CAACC,IAAI,CAAC,mBAAmB,EAACJ,OAAO,CAAC;IACxC,MAAML,IAAI,CAACJ,eAAe,CAAC2C,mBAAmB,EAAClC,OAAO,CAAC;IAExD,MAAMK,SAAS,GAAG,MAAMV,IAAI,CAACJ,eAAe,CAACe,sBAAsB,EAACN,OAAO,CAAC8B,IAAI,CAAC;IACjF,MAAMC,KAAK,GAAG,MAAM1B,SAAS,CAACI,IAAI,CAAC,CAAC;IACpCN,OAAO,CAACgC,GAAG,CAAC,gBAAgB,EAACJ,KAAK,CAAC;IACnC,MAAMnC,GAAG,CAACJ,eAAe,CAACkB,0BAA0B,CAACqB,KAAK,CAAC,CAAC;IAC5D,MAAMnC,GAAG,CAACH,cAAc,CAACkB,WAAW,CAACZ,IAAI,CAAC,CAAC;EAC/C,CAAC,CAAC,OAAOa,GAAG,EAAE;IAAA,IAAAwB,cAAA,EAAAC,mBAAA;IACV,MAAMzC,GAAG,CAACH,cAAc,CAACkB,WAAW,CAACZ,IAAI,CAAC,CAAC;IAC3C,MAAMH,GAAG,CAACH,cAAc,CAACsB,QAAQ,CAAC;MAC9BC,MAAM,EAAEjB,IAAI;MACZkB,OAAO,GAAAmB,cAAA,GAAExB,GAAG,CAACM,QAAQ,cAAAkB,cAAA,wBAAAC,mBAAA,GAAZD,cAAA,CAAc5B,IAAI,cAAA6B,mBAAA,uBAAlBA,mBAAA,CAAoBlB;IACjC,CAAC,CAAC,CAAC;EACP;AACJ;AAEA,UAAUmB,oBAAoBA,CAAC;EAAEvC,IAAI;EAAEC;AAAQ,CAAC,EAAE;EAC9C,IAAI;IACF,MAAMJ,GAAG,CAACH,cAAc,CAACQ,WAAW,CAACF,IAAI,CAAC,CAAC;IAC3C,MAAMH,GAAG,CAACH,cAAc,CAACS,YAAY,CAACH,IAAI,CAAC,CAAC;;IAE5C;IACC,MAAMJ,IAAI,CAACJ,eAAe,CAAC+C,oBAAoB,EAAEtC,OAAO,CAAC;;IAE1D;IACAG,OAAO,CAACC,IAAI,CAAC,oBAAoB,EAACJ,OAAO,CAAC;IAC1C,MAAMK,SAAS,GAAG,MAAMV,IAAI,CAACJ,eAAe,CAACe,sBAAsB,EAACN,OAAO,CAAC8B,IAAI,CAAC;IACjF,MAAMC,KAAK,GAAG,MAAM1B,SAAS,CAACI,IAAI,CAAC,CAAC;IACtCN,OAAO,CAACC,IAAI,CAAC,0BAA0B,EAAC2B,KAAK,CAAC;IAC5C;IACA,MAAMnC,GAAG,CAACJ,eAAe,CAACkB,0BAA0B,CAACqB,KAAK,CAAC,CAAC;IAC5D,MAAMnC,GAAG,CAACH,cAAc,CAACkB,WAAW,CAACZ,IAAI,CAAC,CAAC;EAC7C,CAAC,CAAC,OAAOa,GAAG,EAAE;IAAA,IAAA2B,cAAA,EAAAC,mBAAA;IACZ,MAAMC,YAAY,GAAG,EAAAF,cAAA,GAAA3B,GAAG,CAACM,QAAQ,cAAAqB,cAAA,wBAAAC,mBAAA,GAAZD,cAAA,CAAc/B,IAAI,cAAAgC,mBAAA,uBAAlBA,mBAAA,CAAoBrB,KAAK,KAAI,wBAAwB;IAC1E,MAAMvB,GAAG,CAACH,cAAc,CAACsB,QAAQ,CAAC;MAChCC,MAAM,EAAEjB,IAAI;MACZkB,OAAO,EAAEwB;IACX,CAAC,CAAC,CAAC;EACL,CAAC,SAAS;IACR,MAAM7C,GAAG,CAACH,cAAc,CAACkB,WAAW,CAACZ,IAAI,CAAC,CAAC;EAC7C;AACF;AAEF,UAAU2C,iBAAiBA,CAAC;EAAC3C,IAAI;EAACC;AAAO,CAAC,EAAC;EACvC,IAAI;IACA,MAAMJ,GAAG,CAACH,cAAc,CAACQ,WAAW,CAACF,IAAI,CAAC,CAAC;IAC3C,MAAMH,GAAG,CAACH,cAAc,CAACS,YAAY,CAACH,IAAI,CAAC,CAAC;IAC5C,MAAMJ,IAAI,CAACJ,eAAe,CAACmD,iBAAiB,EAAC1C,OAAO,CAAC;IAErDG,OAAO,CAACgC,GAAG,CAAC,uBAAuB,EAACnC,OAAO,CAAC;IAC5C,MAAM6B,OAAO,GAAG,MAAMlC,IAAI,CAACJ,eAAe,CAACe,sBAAsB,EAACN,OAAO,CAAC8B,IAAI,CAAC;IAC/E,MAAMC,KAAK,GAAG,MAAMF,OAAO,CAACpB,IAAI,CAAC,CAAC;IACjC,MAAMb,GAAG,CAACJ,eAAe,CAACkB,0BAA0B,CAACqB,KAAK,CAAC,CAAC;IAC7D,MAAMnC,GAAG,CAACH,cAAc,CAACkB,WAAW,CAACZ,IAAI,CAAC,CAAC;EAC/C,CAAC,CAAC,OAAOa,GAAG,EAAE;IAAA,IAAA+B,cAAA,EAAAC,mBAAA;IACV,MAAMhD,GAAG,CAACH,cAAc,CAACkB,WAAW,CAACZ,IAAI,CAAC,CAAC;IAC3C,MAAMH,GAAG,CAACH,cAAc,CAACsB,QAAQ,CAAC;MAC9BC,MAAM,EAAEjB,IAAI;MACZkB,OAAO,GAAA0B,cAAA,GAAE/B,GAAG,CAACM,QAAQ,cAAAyB,cAAA,wBAAAC,mBAAA,GAAZD,cAAA,CAAcnC,IAAI,cAAAoC,mBAAA,uBAAlBA,mBAAA,CAAoBzB;IACjC,CAAC,CAAC,CAAC;EACP;AAEJ;AAEA,UAAU0B,iBAAiBA,CAAC;EAAC9C,IAAI;EAACC;AAAO,CAAC,EAAC;EACvC,IAAI;IACA,MAAMJ,GAAG,CAACH,cAAc,CAACQ,WAAW,CAACF,IAAI,CAAC,CAAC;IAC3C,MAAMH,GAAG,CAACH,cAAc,CAACS,YAAY,CAACH,IAAI,CAAC,CAAC;IAC5CI,OAAO,CAACgC,GAAG,CAAC,uBAAuB,EAACnC,OAAO,CAAC8C,GAAG,CAAC;IAC/C,MAAMnD,IAAI,CAACJ,eAAe,CAACwD,uBAAuB,EAAC/C,OAAO,CAAC;IAE5D,MAAMK,SAAS,GAAG,MAAMV,IAAI,CAACJ,eAAe,CAACe,sBAAsB,EAACN,OAAO,CAAC8B,IAAI,CAAC;IACjF,MAAMC,KAAK,GAAG,MAAM1B,SAAS,CAACI,IAAI,CAAC,CAAC;IACpC,MAAMb,GAAG,CAACJ,eAAe,CAACkB,0BAA0B,CAACqB,KAAK,CAAC,CAAC;IAC5D,MAAMnC,GAAG,CAACH,cAAc,CAACkB,WAAW,CAACZ,IAAI,CAAC,CAAC;EAC/C,CAAC,CAAC,OAAOa,GAAG,EAAE;IAAA,IAAAoC,cAAA,EAAAC,mBAAA;IACV,MAAMrD,GAAG,CAACH,cAAc,CAACkB,WAAW,CAACZ,IAAI,CAAC,CAAC;IAC3C,MAAMH,GAAG,CAACH,cAAc,CAACsB,QAAQ,CAAC;MAC9BC,MAAM,EAAEjB,IAAI;MACZkB,OAAO,GAAA+B,cAAA,GAAEpC,GAAG,CAACM,QAAQ,cAAA8B,cAAA,wBAAAC,mBAAA,GAAZD,cAAA,CAAcxC,IAAI,cAAAyC,mBAAA,uBAAlBA,mBAAA,CAAoB9B;IACjC,CAAC,CAAC,CAAC;EACP;AAEJ;AAEA,UAAU+B,mBAAmBA,CAAC;EAACnD,IAAI;EAACC;AAAO,CAAC,EAAC;EAEzC,IAAI;IACA,MAAMJ,GAAG,CAACH,cAAc,CAACQ,WAAW,CAACF,IAAI,CAAC,CAAC;IAC3C,MAAMH,GAAG,CAACH,cAAc,CAACS,YAAY,CAACH,IAAI,CAAC,CAAC;IAC3C,MAAMJ,IAAI,CAACJ,eAAe,CAAC4D,yBAAyB,EAACnD,OAAO,CAAC;IAE9DG,OAAO,CAACgC,GAAG,CAAC,yBAAyB,EAACnC,OAAO,CAAC8C,GAAG,CAAC;IAClD,MAAMzC,SAAS,GAAG,MAAMV,IAAI,CAACJ,eAAe,CAACe,sBAAsB,EAACN,OAAO,CAAC8B,IAAI,CAAC;IACjF,MAAMC,KAAK,GAAG,MAAM1B,SAAS,CAACI,IAAI,CAAC,CAAC;IACpC,MAAMb,GAAG,CAACJ,eAAe,CAACkB,0BAA0B,CAACqB,KAAK,CAAC,CAAC;IAC5D,MAAMnC,GAAG,CAACH,cAAc,CAACkB,WAAW,CAACZ,IAAI,CAAC,CAAC;EAC/C,CAAC,CAAC,OAAOa,GAAG,EAAE;IAAA,IAAAwC,cAAA,EAAAC,mBAAA;IACV,MAAMzD,GAAG,CAACH,cAAc,CAACkB,WAAW,CAACZ,IAAI,CAAC,CAAC;IAC3C,MAAMH,GAAG,CAACH,cAAc,CAACsB,QAAQ,CAAC;MAC9BC,MAAM,EAAEjB,IAAI;MACZkB,OAAO,GAAAmC,cAAA,GAAExC,GAAG,CAACM,QAAQ,cAAAkC,cAAA,wBAAAC,mBAAA,GAAZD,cAAA,CAAc5C,IAAI,cAAA6C,mBAAA,uBAAlBA,mBAAA,CAAoBlC;IACjC,CAAC,CAAC,CAAC;EACP;AAEJ;AAEA,UAAUmC,eAAeA,CAAC;EAACvD,IAAI;EAACC;AAAO,CAAC,EAAE;EACtC,IAAI;IACA,MAAMJ,GAAG,CAACH,cAAc,CAACQ,WAAW,CAACF,IAAI,CAAC,CAAC;IAC3C,MAAMH,GAAG,CAACH,cAAc,CAACS,YAAY,CAACH,IAAI,CAAC,CAAC;IAC3C,MAAMJ,IAAI,CAACJ,eAAe,CAACgE,qBAAqB,EAACvD,OAAO,CAAC;IAC1D;IACA,MAAMK,SAAS,GAAG,MAAMV,IAAI,CAACJ,eAAe,CAACe,sBAAsB,EAACN,OAAO,CAAC8B,IAAI,CAAC;IACjF,MAAMC,KAAK,GAAG,MAAM1B,SAAS,CAACI,IAAI,CAAC,CAAC;IACpC,MAAMb,GAAG,CAACJ,eAAe,CAACkB,0BAA0B,CAACqB,KAAK,CAAC,CAAC;IAC5D,MAAMnC,GAAG,CAACH,cAAc,CAACkB,WAAW,CAACZ,IAAI,CAAC,CAAC;EAC/C,CAAC,CAAC,OAAOa,GAAG,EAAE;IAAA,IAAA4C,cAAA,EAAAC,mBAAA;IACV,MAAM7D,GAAG,CAACH,cAAc,CAACkB,WAAW,CAACZ,IAAI,CAAC,CAAC;IAC3C,MAAMH,GAAG,CAACH,cAAc,CAACsB,QAAQ,CAAC;MAC9BC,MAAM,EAAEjB,IAAI;MACZkB,OAAO,GAAAuC,cAAA,GAAE5C,GAAG,CAACM,QAAQ,cAAAsC,cAAA,wBAAAC,mBAAA,GAAZD,cAAA,CAAchD,IAAI,cAAAiD,mBAAA,uBAAlBA,mBAAA,CAAoBtC;IACjC,CAAC,CAAC,CAAC;EACP;AACJ;AAEA,UAAUuC,aAAaA,CAAC;EAAC3D,IAAI;EAACC;AAAO,CAAC,EAAE;EACpC,IAAI;IACAG,OAAO,CAACgC,GAAG,CAAC,WAAW,CAAC;IACxB,MAAMvC,GAAG,CAACH,cAAc,CAACQ,WAAW,CAACF,IAAI,CAAC,CAAC;IAC3C,MAAMH,GAAG,CAACH,cAAc,CAACS,YAAY,CAACH,IAAI,CAAC,CAAC;IAC3C,MAAMJ,IAAI,CAACJ,eAAe,CAACoE,mBAAmB,EAAC3D,OAAO,CAAC;IAExD,MAAMK,SAAS,GAAG,MAAMV,IAAI,CAACJ,eAAe,CAACe,sBAAsB,EAACN,OAAO,CAAC8B,IAAI,CAAC;IACjF,MAAMC,KAAK,GAAG,MAAM1B,SAAS,CAACI,IAAI,CAAC,CAAC;IACpC,MAAMb,GAAG,CAACJ,eAAe,CAACkB,0BAA0B,CAACqB,KAAK,CAAC,CAAC;IAC5D,MAAMnC,GAAG,CAACH,cAAc,CAACkB,WAAW,CAACZ,IAAI,CAAC,CAAC;EAC/C,CAAC,CAAC,OAAOa,GAAG,EAAE;IAAA,IAAAgD,cAAA,EAAAC,mBAAA;IACV,MAAMjE,GAAG,CAACH,cAAc,CAACkB,WAAW,CAACZ,IAAI,CAAC,CAAC;IAC3C,MAAMH,GAAG,CAACH,cAAc,CAACsB,QAAQ,CAAC;MAC9BC,MAAM,EAAEjB,IAAI;MACZkB,OAAO,GAAA2C,cAAA,GAAEhD,GAAG,CAACM,QAAQ,cAAA0C,cAAA,wBAAAC,mBAAA,GAAZD,cAAA,CAAcpD,IAAI,cAAAqD,mBAAA,uBAAlBA,mBAAA,CAAoB1C;IACjC,CAAC,CAAC,CAAC;EACP;AAEJ;AAEA,UAAU2C,uBAAuBA,CAAC;EAAC/D,IAAI;EAACC;AAAO,CAAC,EAAE;EAC9C,IAAI;IACA,MAAMJ,GAAG,CAACH,cAAc,CAACQ,WAAW,CAACF,IAAI,CAAC,CAAC;IAC3C,MAAMH,GAAG,CAACH,cAAc,CAACS,YAAY,CAACH,IAAI,CAAC,CAAC;IAC3CI,OAAO,CAACgC,GAAG,CAAC,sBAAsB,EAACnC,OAAO,CAAC;IAC3C,MAAML,IAAI,CAACJ,eAAe,CAACwE,wBAAwB,EAAC/D,OAAO,CAAC;IAG7D,MAAMK,SAAS,GAAG,MAAMV,IAAI,CAACJ,eAAe,CAACe,sBAAsB,EAACN,OAAO,CAAC8B,IAAI,CAAC;IACjF,MAAMC,KAAK,GAAG,MAAM1B,SAAS,CAACI,IAAI,CAAC,CAAC;IACpC,MAAMb,GAAG,CAACJ,eAAe,CAACkB,0BAA0B,CAACqB,KAAK,CAAC,CAAC;IAC5D,MAAMnC,GAAG,CAACH,cAAc,CAACkB,WAAW,CAACZ,IAAI,CAAC,CAAC;EAC/C,CAAC,CAAC,OAAOa,GAAG,EAAE;IAAA,IAAAoD,cAAA,EAAAC,mBAAA;IACV,MAAMrE,GAAG,CAACH,cAAc,CAACkB,WAAW,CAACZ,IAAI,CAAC,CAAC;IAC3C,MAAMH,GAAG,CAACH,cAAc,CAACsB,QAAQ,CAAC;MAC9BC,MAAM,EAAEjB,IAAI;MACZkB,OAAO,GAAA+C,cAAA,GAAEpD,GAAG,CAACM,QAAQ,cAAA8C,cAAA,wBAAAC,mBAAA,GAAZD,cAAA,CAAcxD,IAAI,cAAAyD,mBAAA,uBAAlBA,mBAAA,CAAoB9C;IACjC,CAAC,CAAC,CAAC;EACP;AACJ;AAEA,UAAU+C,oBAAoBA,CAAC;EAACnE,IAAI;EAACC;AAAO,CAAC,EAAE;EAC3C,IAAI;IACA,MAAMJ,GAAG,CAACH,cAAc,CAACQ,WAAW,CAACF,IAAI,CAAC,CAAC;IAC3C,MAAMH,GAAG,CAACH,cAAc,CAACS,YAAY,CAACH,IAAI,CAAC,CAAC;IAC5C;IACD,MAAMJ,IAAI,CAACJ,eAAe,CAAC4E,0BAA0B,EAACnE,OAAO,CAAC;IAC7D,MAAMJ,GAAG,CAACH,cAAc,CAACkB,WAAW,CAACZ,IAAI,CAAC,CAAC;EAC/C,CAAC,CAAC,OAAOa,GAAG,EAAE;IAAA,IAAAwD,eAAA,EAAAC,oBAAA;IACV,MAAMzE,GAAG,CAACH,cAAc,CAACkB,WAAW,CAACZ,IAAI,CAAC,CAAC;IAC3C,MAAMH,GAAG,CAACH,cAAc,CAACsB,QAAQ,CAAC;MAC9BC,MAAM,EAAEjB,IAAI;MACZkB,OAAO,GAAAmD,eAAA,GAAExD,GAAG,CAACM,QAAQ,cAAAkD,eAAA,wBAAAC,oBAAA,GAAZD,eAAA,CAAc5D,IAAI,cAAA6D,oBAAA,uBAAlBA,oBAAA,CAAoBlD;IACjC,CAAC,CAAC,CAAC;EACP;AACJ;AAIA,OAAO,UAAUmD,eAAeA,CAAA,EAAG;EAC/B,MAAM5E,GAAG,CAAC,CACN,MAAMG,UAAU,CAACL,eAAe,CAACM,eAAe,CAACC,IAAI,EAAED,eAAe,CAAC,EACvE,MAAMD,UAAU,CAACL,eAAe,CAAC+B,eAAe,CAACxB,IAAI,EAAEO,sBAAsB,CAAC,EAC9E,MAAMT,UAAU,CAACL,eAAe,CAAC+E,oBAAoB,CAACxE,IAAI,EAAE2B,oBAAoB,CAAC,EACjF,MAAM7B,UAAU,CAACL,eAAe,CAACgF,aAAa,CAACzE,IAAI,EAAEmC,mBAAmB,CAAC,EACzE,MAAMrC,UAAU,CAACL,eAAe,CAACiF,WAAW,CAAC1E,IAAI,EAAEuC,oBAAoB,CAAC,EACxE,MAAMzC,UAAU,CAACL,eAAe,CAACkF,iBAAiB,CAAC3E,IAAI,EAAC2C,iBAAiB,CAAC,EAC1E,MAAM7C,UAAU,CAACL,eAAe,CAACmF,WAAW,CAAC5E,IAAI,EAAC8C,iBAAiB,CAAC,EACpE,MAAMhD,UAAU,CAACL,eAAe,CAACoF,aAAa,CAAC7E,IAAI,EAACmD,mBAAmB,CAAC,EACxE,MAAMrD,UAAU,CAACL,eAAe,CAACqF,YAAY,CAAC9E,IAAI,EAACuD,eAAe,CAAC,EACnE,MAAMzD,UAAU,CAACL,eAAe,CAACsF,UAAU,CAAC/E,IAAI,EAAC2D,aAAa,CAAC,EAC/D,MAAM7D,UAAU,CAACL,eAAe,CAACuF,iBAAiB,CAAChF,IAAI,EAACmE,oBAAoB,CAAC,EAC7E,MAAMrE,UAAU,CAACL,eAAe,CAACwF,qBAAqB,CAACjF,IAAI,EAAC+D,uBAAuB,CAAC,CAC1F,CAAC;AACH;AAACmB,EAAA,GAfgBX,eAAe;AAAA,IAAAW,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}