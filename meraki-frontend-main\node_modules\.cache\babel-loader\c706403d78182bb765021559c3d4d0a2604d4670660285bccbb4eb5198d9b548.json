{"ast": null, "code": "import { createSlice } from \"@reduxjs/toolkit\";\nexport const AuthSlice = createSlice({\n  name: \"auth\",\n  initialState: {\n    success: null,\n    loading: false,\n    error: null\n  },\n  reducers: {\n    login: () => {},\n    loginSuccess: (state, action) => {\n      state.success = action.payload;\n    },\n    loginFailure: (state, action) => {\n      state.error = action.payload;\n    },\n    logout: () => {\n      localStorage.clear();\n      sessionStorage.clear();\n    }\n  }\n});\nexport default AuthSlice;", "map": {"version": 3, "names": ["createSlice", "AuthSlice", "name", "initialState", "success", "loading", "error", "reducers", "login", "loginSuccess", "state", "action", "payload", "loginFailure", "logout", "localStorage", "clear", "sessionStorage"], "sources": ["E:/Suraj Patil/Organization_Management_System/meraki-frontend-main/src/slices/slice/AuthSlice.js"], "sourcesContent": ["import { createSlice } from \"@reduxjs/toolkit\";\r\n\r\n\r\nexport const AuthSlice = createSlice({\r\n    name: \"auth\",\r\n    initialState: {\r\n        success: null,\r\n        loading: false,\r\n        error: null,\r\n    },\r\n    reducers: {\r\n        login: () => {},\r\n        loginSuccess: (state, action) => {\r\n            state.success = action.payload;\r\n        },\r\n        loginFailure: (state, action) => {\r\n            state.error = action.payload;\r\n        },\r\n        logout: () => {\r\n            localStorage.clear();\r\n            sessionStorage.clear();\r\n        }\r\n    }\r\n});\r\n\r\nexport default AuthSlice;"], "mappings": "AAAA,SAASA,WAAW,QAAQ,kBAAkB;AAG9C,OAAO,MAAMC,SAAS,GAAGD,WAAW,CAAC;EACjCE,IAAI,EAAE,MAAM;EACZC,YAAY,EAAE;IACVC,OAAO,EAAE,IAAI;IACbC,OAAO,EAAE,KAAK;IACdC,KAAK,EAAE;EACX,CAAC;EACDC,QAAQ,EAAE;IACNC,KAAK,EAAEA,CAAA,KAAM,CAAC,CAAC;IACfC,YAAY,EAAEA,CAACC,KAAK,EAAEC,MAAM,KAAK;MAC7BD,KAAK,CAACN,OAAO,GAAGO,MAAM,CAACC,OAAO;IAClC,CAAC;IACDC,YAAY,EAAEA,CAACH,KAAK,EAAEC,MAAM,KAAK;MAC7BD,KAAK,CAACJ,KAAK,GAAGK,MAAM,CAACC,OAAO;IAChC,CAAC;IACDE,MAAM,EAAEA,CAAA,KAAM;MACVC,YAAY,CAACC,KAAK,CAAC,CAAC;MACpBC,cAAc,CAACD,KAAK,CAAC,CAAC;IAC1B;EACJ;AACJ,CAAC,CAAC;AAEF,eAAef,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}