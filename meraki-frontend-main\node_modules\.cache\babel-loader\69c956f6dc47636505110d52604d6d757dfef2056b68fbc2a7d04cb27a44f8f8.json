{"ast": null, "code": "import { createSelector } from \"@reduxjs/toolkit\";\nconst timelineSelector = state => state.timeline;\nconst getTimelineRequests = () => createSelector(timelineSelector, timeline => timeline.timelineArr);\nconst getPagination = () => createSelector(timelineSelector, timeline => timeline.pagination);\nconst getTimelineRequestsToday = () => createSelector(timelineSelector, timeline => timeline.timelineToday);\nexport const TimelineSelector = {\n  getTimelineRequests,\n  getPagination,\n  getTimelineRequestsToday\n};", "map": {"version": 3, "names": ["createSelector", "timelineSelector", "state", "timeline", "getTimelineRequests", "timelineArr", "getPagination", "pagination", "getTimelineRequestsToday", "timelineToday", "TimelineSelector"], "sources": ["E:/Suraj Patil/Organization_Management_System/meraki-frontend-main/src/selectors/TimelineSelector.js"], "sourcesContent": ["import {createSelector} from \"@reduxjs/toolkit\";\r\n\r\n\r\nconst timelineSelector = (state) => state.timeline;\r\n\r\nconst getTimelineRequests = () => createSelector(\r\n    timelineSelector,\r\n    timeline => timeline.timelineArr\r\n);\r\n\r\nconst getPagination = () => createSelector(\r\n    timelineSelector,\r\n    timeline => timeline.pagination\r\n)\r\nconst getTimelineRequestsToday = () => createSelector(\r\n    timelineSelector,\r\n    timeline => timeline.timelineToday\r\n)\r\n\r\nexport const TimelineSelector = {\r\n    getTimelineRequests,\r\n    getPagination,\r\n    getTimelineRequestsToday\r\n}"], "mappings": "AAAA,SAAQA,cAAc,QAAO,kBAAkB;AAG/C,MAAMC,gBAAgB,GAAIC,KAAK,IAAKA,KAAK,CAACC,QAAQ;AAElD,MAAMC,mBAAmB,GAAGA,CAAA,KAAMJ,cAAc,CAC5CC,gBAAgB,EAChBE,QAAQ,IAAIA,QAAQ,CAACE,WACzB,CAAC;AAED,MAAMC,aAAa,GAAGA,CAAA,KAAMN,cAAc,CACtCC,gBAAgB,EAChBE,QAAQ,IAAIA,QAAQ,CAACI,UACzB,CAAC;AACD,MAAMC,wBAAwB,GAAGA,CAAA,KAAMR,cAAc,CACjDC,gBAAgB,EAChBE,QAAQ,IAAIA,QAAQ,CAACM,aACzB,CAAC;AAED,OAAO,MAAMC,gBAAgB,GAAG;EAC5BN,mBAAmB;EACnBE,aAAa;EACbE;AACJ,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}