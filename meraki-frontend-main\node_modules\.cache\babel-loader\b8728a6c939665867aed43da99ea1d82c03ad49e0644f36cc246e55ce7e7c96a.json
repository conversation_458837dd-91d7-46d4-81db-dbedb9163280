{"ast": null, "code": "var _jsxFileName = \"E:\\\\Suraj Patil\\\\Organization_Management_System\\\\meraki-frontend-main\\\\src\\\\screens\\\\Profile\\\\components\\\\AccountSetting.js\",\n  _s = $RefreshSig$();\nimport React, { useState } from \"react\";\nimport { Card, Grid, IconButton, InputAdornment, Typography } from \"@mui/material\";\nimport Input from \"components/Input\";\nimport { Visibility, VisibilityOff } from \"@mui/icons-material\";\nimport PropTypes from \"prop-types\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nAccountSetting.propTypes = {\n  formik: PropTypes.object\n};\nexport default function AccountSetting(props) {\n  _s();\n  const {\n    formik\n  } = props;\n  const [showPassword, setShowPassword] = useState(false);\n  const handleClickShowPassword = () => {\n    setShowPassword(!showPassword);\n  };\n  const handleMouseDownPassword = event => {\n    event.preventDefault();\n  };\n  return /*#__PURE__*/_jsxDEV(Card, {\n    sx: {\n      mb: 3\n    },\n    children: [/*#__PURE__*/_jsxDEV(Typography, {\n      variant: \"h5\",\n      sx: {\n        mb: 4\n      },\n      children: \"Account Setting\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 28,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(Grid, {\n      container: true,\n      spacing: 3,\n      children: [/*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        lg: 6,\n        xs: 12,\n        children: /*#__PURE__*/_jsxDEV(Input, {\n          label: \"Email\",\n          type: \"email\",\n          name: \"email\",\n          value: formik.values.email,\n          onChange: formik.handleChange,\n          error: Boolean(formik.touched.email) && Boolean(formik.errors.email),\n          helpertext: formik.touched.email ? formik.errors.email : \"\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 31,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 30,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        lg: 6,\n        xs: 12,\n        children: /*#__PURE__*/_jsxDEV(Input, {\n          label: \"Password\",\n          inputProps: {\n            autoComplete: \"new-password\"\n          },\n          placeholder: \"\\u25CF\\u25CF\\u25CF\\u25CF\\u25CF\\u25CF\\u25CF\\u25CF\\u25CF\\u25CF\",\n          type: \"password\",\n          name: \"password\",\n          endAdornment: /*#__PURE__*/_jsxDEV(InputAdornment, {\n            position: \"end\",\n            children: /*#__PURE__*/_jsxDEV(IconButton, {\n              \"aria-label\": \"toggle password visibility\",\n              onClick: handleClickShowPassword,\n              onMouseDown: handleMouseDownPassword,\n              edge: \"end\",\n              children: !showPassword ? /*#__PURE__*/_jsxDEV(VisibilityOff, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 57,\n                columnNumber: 54\n              }, this) : /*#__PURE__*/_jsxDEV(Visibility, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 57,\n                columnNumber: 74\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 51,\n              columnNumber: 33\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 50,\n            columnNumber: 29\n          }, this),\n          value: formik.values.password,\n          onChange: formik.handleChange,\n          error: formik.touched.password && Boolean(formik.errors.password),\n          helpertext: formik.touched.password ? formik.errors.password : \"\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 41,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 40,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 29,\n      columnNumber: 13\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 27,\n    columnNumber: 9\n  }, this);\n}\n_s(AccountSetting, \"daguiRHWMFkqPgCh/ppD7CF5VuQ=\");\n_c = AccountSetting;\nvar _c;\n$RefreshReg$(_c, \"AccountSetting\");", "map": {"version": 3, "names": ["React", "useState", "Card", "Grid", "IconButton", "InputAdornment", "Typography", "Input", "Visibility", "VisibilityOff", "PropTypes", "jsxDEV", "_jsxDEV", "AccountSetting", "propTypes", "formik", "object", "props", "_s", "showPassword", "setShowPassword", "handleClickShowPassword", "handleMouseDownPassword", "event", "preventDefault", "sx", "mb", "children", "variant", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "container", "spacing", "item", "lg", "xs", "label", "type", "name", "value", "values", "email", "onChange", "handleChange", "error", "Boolean", "touched", "errors", "helpertext", "inputProps", "autoComplete", "placeholder", "endAdornment", "position", "onClick", "onMouseDown", "edge", "password", "_c", "$RefreshReg$"], "sources": ["E:/Suraj Patil/Organization_Management_System/meraki-frontend-main/src/screens/Profile/components/AccountSetting.js"], "sourcesContent": ["import React, {useState} from \"react\";\r\nimport {\r\n    Card, Grid, IconButton, InputAdornment, Typography\r\n} from \"@mui/material\";\r\nimport Input from \"components/Input\";\r\nimport {Visibility, VisibilityOff} from \"@mui/icons-material\";\r\nimport PropTypes from \"prop-types\";\r\n\r\nAccountSetting.propTypes = {\r\n    formik: PropTypes.object\r\n};\r\n\r\nexport default function AccountSetting(props) {\r\n    const { formik } = props;\r\n\r\n    const [showPassword, setShowPassword] = useState(false);\r\n\r\n    const handleClickShowPassword = () => {\r\n        setShowPassword(!showPassword);\r\n    };\r\n\r\n    const handleMouseDownPassword = (event) => {\r\n        event.preventDefault();\r\n    };\r\n\r\n    return (\r\n        <Card sx={{ mb: 3 }}>\r\n            <Typography variant='h5' sx={{ mb: 4 }}>Account Setting</Typography>\r\n            <Grid container spacing={3}>\r\n                <Grid item lg={6} xs={12}>\r\n                    <Input\r\n                        label=\"Email\"\r\n                        type=\"email\"\r\n                        name='email'\r\n                        value={formik.values.email}\r\n                        onChange={formik.handleChange}\r\n                        error={Bo<PERSON>an(formik.touched.email) && Boolean(formik.errors.email)}\r\n                        helpertext={formik.touched.email ? formik.errors.email : \"\"}/>\r\n                </Grid>\r\n                <Grid item lg={6} xs={12}>\r\n                    <Input\r\n                        label=\"Password\"\r\n                        inputProps={{\r\n                            autoComplete: \"new-password\"\r\n                        }}\r\n                        placeholder='●●●●●●●●●●'\r\n                        type=\"password\"\r\n                        name='password'\r\n                        endAdornment={\r\n                            <InputAdornment position=\"end\">\r\n                                <IconButton\r\n                                    aria-label=\"toggle password visibility\"\r\n                                    onClick={handleClickShowPassword}\r\n                                    onMouseDown={handleMouseDownPassword}\r\n                                    edge=\"end\"\r\n                                >\r\n                                    {!showPassword ? <VisibilityOff /> : <Visibility />}\r\n                                </IconButton>\r\n                            </InputAdornment>\r\n                        }\r\n                        value={formik.values.password}\r\n                        onChange={formik.handleChange}\r\n                        error={formik.touched.password && Boolean(formik.errors.password)}\r\n                        helpertext={formik.touched.password ? formik.errors.password : \"\"}/>\r\n                </Grid>\r\n            </Grid>\r\n        </Card>\r\n    )\r\n}"], "mappings": ";;AAAA,OAAOA,KAAK,IAAGC,QAAQ,QAAO,OAAO;AACrC,SACIC,IAAI,EAAEC,IAAI,EAAEC,UAAU,EAAEC,cAAc,EAAEC,UAAU,QAC/C,eAAe;AACtB,OAAOC,KAAK,MAAM,kBAAkB;AACpC,SAAQC,UAAU,EAAEC,aAAa,QAAO,qBAAqB;AAC7D,OAAOC,SAAS,MAAM,YAAY;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEnCC,cAAc,CAACC,SAAS,GAAG;EACvBC,MAAM,EAAEL,SAAS,CAACM;AACtB,CAAC;AAED,eAAe,SAASH,cAAcA,CAACI,KAAK,EAAE;EAAAC,EAAA;EAC1C,MAAM;IAAEH;EAAO,CAAC,GAAGE,KAAK;EAExB,MAAM,CAACE,YAAY,EAAEC,eAAe,CAAC,GAAGnB,QAAQ,CAAC,KAAK,CAAC;EAEvD,MAAMoB,uBAAuB,GAAGA,CAAA,KAAM;IAClCD,eAAe,CAAC,CAACD,YAAY,CAAC;EAClC,CAAC;EAED,MAAMG,uBAAuB,GAAIC,KAAK,IAAK;IACvCA,KAAK,CAACC,cAAc,CAAC,CAAC;EAC1B,CAAC;EAED,oBACIZ,OAAA,CAACV,IAAI;IAACuB,EAAE,EAAE;MAAEC,EAAE,EAAE;IAAE,CAAE;IAAAC,QAAA,gBAChBf,OAAA,CAACN,UAAU;MAACsB,OAAO,EAAC,IAAI;MAACH,EAAE,EAAE;QAAEC,EAAE,EAAE;MAAE,CAAE;MAAAC,QAAA,EAAC;IAAe;MAAAE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAY,CAAC,eACpEpB,OAAA,CAACT,IAAI;MAAC8B,SAAS;MAACC,OAAO,EAAE,CAAE;MAAAP,QAAA,gBACvBf,OAAA,CAACT,IAAI;QAACgC,IAAI;QAACC,EAAE,EAAE,CAAE;QAACC,EAAE,EAAE,EAAG;QAAAV,QAAA,eACrBf,OAAA,CAACL,KAAK;UACF+B,KAAK,EAAC,OAAO;UACbC,IAAI,EAAC,OAAO;UACZC,IAAI,EAAC,OAAO;UACZC,KAAK,EAAE1B,MAAM,CAAC2B,MAAM,CAACC,KAAM;UAC3BC,QAAQ,EAAE7B,MAAM,CAAC8B,YAAa;UAC9BC,KAAK,EAAEC,OAAO,CAAChC,MAAM,CAACiC,OAAO,CAACL,KAAK,CAAC,IAAII,OAAO,CAAChC,MAAM,CAACkC,MAAM,CAACN,KAAK,CAAE;UACrEO,UAAU,EAAEnC,MAAM,CAACiC,OAAO,CAACL,KAAK,GAAG5B,MAAM,CAACkC,MAAM,CAACN,KAAK,GAAG;QAAG;UAAAd,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAChE,CAAC,eACPpB,OAAA,CAACT,IAAI;QAACgC,IAAI;QAACC,EAAE,EAAE,CAAE;QAACC,EAAE,EAAE,EAAG;QAAAV,QAAA,eACrBf,OAAA,CAACL,KAAK;UACF+B,KAAK,EAAC,UAAU;UAChBa,UAAU,EAAE;YACRC,YAAY,EAAE;UAClB,CAAE;UACFC,WAAW,EAAC,8DAAY;UACxBd,IAAI,EAAC,UAAU;UACfC,IAAI,EAAC,UAAU;UACfc,YAAY,eACR1C,OAAA,CAACP,cAAc;YAACkD,QAAQ,EAAC,KAAK;YAAA5B,QAAA,eAC1Bf,OAAA,CAACR,UAAU;cACP,cAAW,4BAA4B;cACvCoD,OAAO,EAAEnC,uBAAwB;cACjCoC,WAAW,EAAEnC,uBAAwB;cACrCoC,IAAI,EAAC,KAAK;cAAA/B,QAAA,EAET,CAACR,YAAY,gBAAGP,OAAA,CAACH,aAAa;gBAAAoB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,gBAAGpB,OAAA,CAACJ,UAAU;gBAAAqB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3C;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CACnB;UACDS,KAAK,EAAE1B,MAAM,CAAC2B,MAAM,CAACiB,QAAS;UAC9Bf,QAAQ,EAAE7B,MAAM,CAAC8B,YAAa;UAC9BC,KAAK,EAAE/B,MAAM,CAACiC,OAAO,CAACW,QAAQ,IAAIZ,OAAO,CAAChC,MAAM,CAACkC,MAAM,CAACU,QAAQ,CAAE;UAClET,UAAU,EAAEnC,MAAM,CAACiC,OAAO,CAACW,QAAQ,GAAG5C,MAAM,CAACkC,MAAM,CAACU,QAAQ,GAAG;QAAG;UAAA9B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACtE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACL,CAAC;AAEf;AAACd,EAAA,CAxDuBL,cAAc;AAAA+C,EAAA,GAAd/C,cAAc;AAAA,IAAA+C,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}