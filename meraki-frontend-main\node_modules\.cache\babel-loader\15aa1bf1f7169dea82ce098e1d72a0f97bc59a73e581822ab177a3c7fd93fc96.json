{"ast": null, "code": "/**\r\n * Utility functions for logging and debugging permissions\r\n */\n\n/**\r\n * Logs all permissions for a user\r\n * @param {Object} user - The user object with permissions\r\n */\nexport const logUserPermissions = user => {\n  if (!user) {\n    // console.log('Permission Log: No user provided');\n    return;\n  }\n\n  // console.group('User Permission Log');\n  // console.log('User:', user.name);\n  // console.log('User ID:', user._id);\n  // console.log('Roles:', user.role || []);\n\n  if (user.permissions && Array.isArray(user.permissions)) {\n    // console.log('Permissions Count:', user.permissions.length);\n\n    // Group permissions by feature\n    const permissionsByFeature = {};\n    user.permissions.forEach(p => {\n      permissionsByFeature[p.feat] = p.acts;\n    });\n\n    // console.table(permissionsByFeature);\n\n    // Log detailed permissions\n    // console.group('Detailed Permissions');\n    // user.permissions.forEach(p => {\n    //   console.log(`Feature: ${p.feat}`);\n    //   console.log(`Actions: ${p.acts.join(', ')}`);\n    //   console.log('---');\n    // });\n    console.groupEnd();\n  } else {\n    console.log('No permissions array found on user');\n  }\n  console.groupEnd();\n};\n\n/**\r\n * Logs all permission checks for a specific route\r\n * @param {string} path - The route path\r\n * @param {Object} permission - The permission object with feat and act properties\r\n * @param {boolean} hasPermission - Whether the user has the permission\r\n * @param {string} reason - The reason for the permission decision\r\n */\nexport const logRoutePermission = (path, permission, hasPermission, reason = '') => {\n  // console.group(`Route Permission Check: ${path}`);\n  // console.log(`Feature: ${permission?.feat || 'None'}`);\n  // console.log(`Action: ${permission?.act || 'None'}`);\n  // console.log(`Result: ${hasPermission ? 'GRANTED' : 'DENIED'}`);\n  if (reason) {\n    //console.log(`Reason: ${reason}`);\n  }\n  console.groupEnd();\n};\nexport default {\n  logUserPermissions,\n  logRoutePermission\n};", "map": {"version": 3, "names": ["logUserPermissions", "user", "permissions", "Array", "isArray", "permissionsByFeature", "for<PERSON>ach", "p", "feat", "acts", "console", "groupEnd", "log", "logRoutePermission", "path", "permission", "hasPermission", "reason"], "sources": ["E:/Suraj Patil/Organization_Management_System/meraki-frontend-main/src/utils/permissionLogger.js"], "sourcesContent": ["/**\r\n * Utility functions for logging and debugging permissions\r\n */\r\n\r\n/**\r\n * Logs all permissions for a user\r\n * @param {Object} user - The user object with permissions\r\n */\r\nexport const logUserPermissions = (user) => {\r\n  if (!user) {\r\n    // console.log('Permission Log: No user provided');\r\n    return;\r\n  }\r\n\r\n  // console.group('User Permission Log');\r\n  // console.log('User:', user.name);\r\n  // console.log('User ID:', user._id);\r\n  // console.log('Roles:', user.role || []);\r\n\r\n  if (user.permissions && Array.isArray(user.permissions)) {\r\n    // console.log('Permissions Count:', user.permissions.length);\r\n\r\n    // Group permissions by feature\r\n    const permissionsByFeature = {};\r\n    user.permissions.forEach(p => {\r\n      permissionsByFeature[p.feat] = p.acts;\r\n    });\r\n\r\n    // console.table(permissionsByFeature);\r\n\r\n    // Log detailed permissions\r\n    // console.group('Detailed Permissions');\r\n    // user.permissions.forEach(p => {\r\n    //   console.log(`Feature: ${p.feat}`);\r\n    //   console.log(`Actions: ${p.acts.join(', ')}`);\r\n    //   console.log('---');\r\n    // });\r\n    console.groupEnd();\r\n  } else {\r\n    console.log('No permissions array found on user');\r\n  }\r\n\r\n  console.groupEnd();\r\n};\r\n\r\n/**\r\n * Logs all permission checks for a specific route\r\n * @param {string} path - The route path\r\n * @param {Object} permission - The permission object with feat and act properties\r\n * @param {boolean} hasPermission - Whether the user has the permission\r\n * @param {string} reason - The reason for the permission decision\r\n */\r\nexport const logRoutePermission = (path, permission, hasPermission, reason = '') => {\r\n  // console.group(`Route Permission Check: ${path}`);\r\n  // console.log(`Feature: ${permission?.feat || 'None'}`);\r\n  // console.log(`Action: ${permission?.act || 'None'}`);\r\n  // console.log(`Result: ${hasPermission ? 'GRANTED' : 'DENIED'}`);\r\n  if (reason) {\r\n    //console.log(`Reason: ${reason}`);\r\n  }\r\n  console.groupEnd();\r\n};\r\n\r\nexport default {\r\n  logUserPermissions,\r\n  logRoutePermission\r\n};\r\n"], "mappings": "AAAA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,OAAO,MAAMA,kBAAkB,GAAIC,IAAI,IAAK;EAC1C,IAAI,CAACA,IAAI,EAAE;IACT;IACA;EACF;;EAEA;EACA;EACA;EACA;;EAEA,IAAIA,IAAI,CAACC,WAAW,IAAIC,KAAK,CAACC,OAAO,CAACH,IAAI,CAACC,WAAW,CAAC,EAAE;IACvD;;IAEA;IACA,MAAMG,oBAAoB,GAAG,CAAC,CAAC;IAC/BJ,IAAI,CAACC,WAAW,CAACI,OAAO,CAACC,CAAC,IAAI;MAC5BF,oBAAoB,CAACE,CAAC,CAACC,IAAI,CAAC,GAAGD,CAAC,CAACE,IAAI;IACvC,CAAC,CAAC;;IAEF;;IAEA;IACA;IACA;IACA;IACA;IACA;IACA;IACAC,OAAO,CAACC,QAAQ,CAAC,CAAC;EACpB,CAAC,MAAM;IACLD,OAAO,CAACE,GAAG,CAAC,oCAAoC,CAAC;EACnD;EAEAF,OAAO,CAACC,QAAQ,CAAC,CAAC;AACpB,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,MAAME,kBAAkB,GAAGA,CAACC,IAAI,EAAEC,UAAU,EAAEC,aAAa,EAAEC,MAAM,GAAG,EAAE,KAAK;EAClF;EACA;EACA;EACA;EACA,IAAIA,MAAM,EAAE;IACV;EAAA;EAEFP,OAAO,CAACC,QAAQ,CAAC,CAAC;AACpB,CAAC;AAED,eAAe;EACbX,kBAAkB;EAClBa;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}