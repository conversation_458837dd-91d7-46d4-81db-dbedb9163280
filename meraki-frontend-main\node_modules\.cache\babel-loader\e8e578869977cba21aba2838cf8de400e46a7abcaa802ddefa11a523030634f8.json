{"ast": null, "code": "var _jsxFileName = \"E:\\\\Suraj Patil\\\\Organization_Management_System\\\\meraki-frontend-main\\\\src\\\\screens\\\\Setting\\\\index.js\",\n  _s2 = $RefreshSig$();\nimport React, { useEffect } from \"react\";\nimport { Box, Button, Card, FormControl, Grid, InputBase, Typography, useTheme } from \"@mui/material\";\nimport PageTitle from \"../../components/PageTitle\";\nimport Input from \"../../components/Input\";\nimport { useFormik } from \"formik\";\nimport { useDispatch, useSelector } from \"react-redux\";\nimport { GeneralActions, SettingActions } from \"../../slices/actions\";\nimport { SettingSelector } from \"../../selectors/SettingSelector\";\nimport { UserSelector } from \"../../selectors/UserSelector\";\nimport Can from \"../../utils/can\";\nimport { Autocomplete } from \"@mui/lab\";\nimport COUNTRIES from \"../../constants/countries\";\nimport { toast } from \"react-toastify\";\nimport { GeneralSelector } from \"../../selectors\";\nimport FormSkeleton from \"../../components/Skeleton/FormSkeleton\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nexport default function Setting() {\n  _s2();\n  var _setting$name,\n    _setting$address,\n    _setting$city,\n    _setting$email,\n    _setting$phone,\n    _setting$leaveLimit,\n    _setting$day,\n    _s = $RefreshSig$();\n  const dispatch = useDispatch();\n  const theme = useTheme();\n  const setting = useSelector(state => state.setting.setting);\n  const loading = useSelector(state => state.setting.loading);\n  const profile = useSelector(UserSelector.profile()) || {};\n  const countries = COUNTRIES.map(item => ({\n    id: item.id,\n    name: item.name,\n    phoneCode: item.phoneCode,\n    flag: item.flag\n  }));\n\n  // Check if user has permission to update settings\n  // This uses the Can utility function which checks for both admin role and specific permissions\n  const canUpdateSettings = Can('update', 'Setting');\n\n  // Debug user permissions\n  console.log('User profile:', profile);\n  console.log('Can update settings?', canUpdateSettings);\n  const success = useSelector(GeneralSelector.success(SettingActions.updateSetting.type));\n  const error = useSelector(GeneralSelector.error(SettingActions.updateSetting.type));\n  useEffect(() => {\n    dispatch(SettingActions.getSetting());\n  }, []);\n  useEffect(() => {\n    if (success) {\n      var _success$message;\n      toast.success(`${(_success$message = success === null || success === void 0 ? void 0 : success.message) !== null && _success$message !== void 0 ? _success$message : \"Success\"}`, {\n        position: \"top-right\",\n        autoClose: 3000,\n        closeOnClick: true,\n        pauseOnHover: false\n      });\n      dispatch(GeneralActions.removeSuccess(SettingActions.updateSetting.type));\n      -dispatch(SettingActions.getSetting());\n    }\n  }, [success]);\n\n  // Handle errors from API calls\n  useEffect(() => {\n    if (error) {\n      // Check if it's a permission error (403)\n      if (error.message && error.message.includes('Forbidden')) {\n        toast.error('You need permission to update settings', {\n          position: \"top-right\",\n          autoClose: 5000,\n          closeOnClick: true,\n          pauseOnHover: true\n        });\n      } else {\n        toast.error(`${error.message || \"Failed to update settings\"}`, {\n          position: \"top-right\",\n          autoClose: 3000,\n          closeOnClick: true,\n          pauseOnHover: false\n        });\n      }\n      dispatch(GeneralActions.removeError(SettingActions.updateSetting.type));\n    }\n  }, [error]);\n  const formik = useFormik({\n    initialValues: {\n      name: (_setting$name = setting === null || setting === void 0 ? void 0 : setting.name) !== null && _setting$name !== void 0 ? _setting$name : '',\n      address: (_setting$address = setting === null || setting === void 0 ? void 0 : setting.address) !== null && _setting$address !== void 0 ? _setting$address : '',\n      city: (_setting$city = setting === null || setting === void 0 ? void 0 : setting.city) !== null && _setting$city !== void 0 ? _setting$city : '',\n      country: '',\n      email: (_setting$email = setting === null || setting === void 0 ? void 0 : setting.email) !== null && _setting$email !== void 0 ? _setting$email : '',\n      phone: (_setting$phone = setting === null || setting === void 0 ? void 0 : setting.phone) !== null && _setting$phone !== void 0 ? _setting$phone : '',\n      phoneCode: '',\n      // ✅ add this\n      phoneNumber: '',\n      // ✅ and this\n      leaveLimit: (_setting$leaveLimit = setting === null || setting === void 0 ? void 0 : setting.leaveLimit) !== null && _setting$leaveLimit !== void 0 ? _setting$leaveLimit : 0,\n      day: (_setting$day = setting === null || setting === void 0 ? void 0 : setting.day) !== null && _setting$day !== void 0 ? _setting$day : 0\n    },\n    enableReinitialize: true,\n    onSubmit: values => {\n      handleSubmit(values);\n    }\n  });\n  useEffect(() => {\n    var _formik$values$countr;\n    const code = (_formik$values$countr = formik.values.country) === null || _formik$values$countr === void 0 ? void 0 : _formik$values$countr.phoneCode;\n    const phone = formik.values.phone;\n    formik.setFieldValue('phoneCode', code !== null && code !== void 0 ? code : '');\n    formik.setFieldValue('phone', phone);\n  }, [formik.values.country]);\n  useEffect(() => {\n    var _formik$values$countr2;\n    if (!setting) {\n      return;\n    }\n    const phone = setting.phone || '';\n    const country = countries.find(e => e.name === (setting === null || setting === void 0 ? void 0 : setting.country));\n    if (country && ((_formik$values$countr2 = formik.values.country) === null || _formik$values$countr2 === void 0 ? void 0 : _formik$values$countr2.name) !== country.name) {\n      formik.setFieldValue('country', country);\n    }\n    if (phone && country) {\n      var _formik$values$phoneC, _formik$values$phoneN;\n      const code = country.phoneCode || '';\n      const currentCode = (_formik$values$phoneC = formik.values.phoneCode) !== null && _formik$values$phoneC !== void 0 ? _formik$values$phoneC : '';\n      const currentPhone = (_formik$values$phoneN = formik.values.phoneNumber) !== null && _formik$values$phoneN !== void 0 ? _formik$values$phoneN : '';\n      if (currentCode !== code || currentPhone !== phone.substring(code.length)) {\n        formik.setFieldValue('phoneCode', code);\n        formik.setFieldValue('phoneNumber', phone.substring(code.length));\n      }\n    }\n  }, [setting, countries]);\n  const handleSubmit = values => {\n    var _values$country$name, _values$country;\n    if (!setting || !setting._id) {\n      toast.error('Settings not loaded yet. Please wait a moment and try again.');\n      return;\n    }\n    if (!canUpdateSettings) {\n      toast.error('You need permission to update settings');\n      return;\n    }\n    const params = {\n      id: setting._id,\n      name: values.name,\n      address: values.address,\n      city: values.city,\n      country: (_values$country$name = (_values$country = values.country) === null || _values$country === void 0 ? void 0 : _values$country.name) !== null && _values$country$name !== void 0 ? _values$country$name : '',\n      email: values.email,\n      phone: (values.phoneCode || '') + (values.phoneNumber || ''),\n      leaveLimit: values.leaveLimit,\n      day: values.day\n    };\n    dispatch(SettingActions.updateSetting(params));\n  };\n  return /*#__PURE__*/_jsxDEV(Box, {\n    children: [/*#__PURE__*/_jsxDEV(PageTitle, {\n      title: \"Company Setting\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 165,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(Card, {\n      children: _s(() => {\n        _s();\n        const hasError = useSelector(state => state.general.errors.find(e => e.action === SettingActions.getSetting.type));\n        if (loading) {\n          return /*#__PURE__*/_jsxDEV(FormSkeleton, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 175,\n            columnNumber: 32\n          }, this);\n        }\n        if (hasError) {\n          return /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              p: 3,\n              textAlign: 'center',\n              border: '1px solid #f0f0f0',\n              borderRadius: 1,\n              bgcolor: '#fff9f9',\n              my: 2\n            },\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              color: \"error\",\n              variant: \"h6\",\n              gutterBottom: true,\n              children: \"Access Denied\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 188,\n              columnNumber: 33\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body1\",\n              children: \"You do not have permission to access settings\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 191,\n              columnNumber: 33\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              color: \"textSecondary\",\n              sx: {\n                mt: 1\n              },\n              children: \"Please contact your administrator if you believe you should have access.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 194,\n              columnNumber: 33\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 180,\n            columnNumber: 29\n          }, this);\n        }\n        return /*#__PURE__*/_jsxDEV(\"form\", {\n          onSubmit: formik.handleSubmit,\n          children: [!canUpdateSettings && /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              p: 2,\n              mb: 3,\n              border: '1px solid #ffcc80',\n              borderRadius: 1,\n              bgcolor: '#fff8e1'\n            },\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              color: \"warning.main\",\n              variant: \"subtitle1\",\n              fontWeight: \"medium\",\n              children: \"View-Only Mode\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 211,\n              columnNumber: 37\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              children: \"You need permission to update settings. You can view the settings but cannot make changes.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 214,\n              columnNumber: 37\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 204,\n            columnNumber: 33\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            container: true,\n            spacing: 3,\n            children: [/*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              lg: 6,\n              sm: 12,\n              xs: 12,\n              children: /*#__PURE__*/_jsxDEV(Input, {\n                label: \"Name\",\n                name: \"name\",\n                value: formik.values.name,\n                onChange: formik.handleChange,\n                error: formik.touched.name && Boolean(formik.errors.name),\n                helpertext: formik.touched.name ? formik.errors.name : \"\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 221,\n                columnNumber: 33\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 220,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              lg: 6,\n              sm: 12,\n              xs: 12,\n              children: /*#__PURE__*/_jsxDEV(Input, {\n                label: \"Email\",\n                name: \"email\",\n                value: formik.values.email,\n                onChange: formik.handleChange\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 230,\n                columnNumber: 33\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 229,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              lg: 6,\n              sm: 12,\n              xs: 12,\n              children: /*#__PURE__*/_jsxDEV(FormControl, {\n                fullWidth: true,\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"caption\",\n                  children: \"Country\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 238,\n                  columnNumber: 37\n                }, this), /*#__PURE__*/_jsxDEV(Autocomplete, {\n                  disablePortal: true,\n                  name: \"country\",\n                  options: countries,\n                  value: formik.values.country,\n                  onChange: (e, val) => {\n                    formik.setFieldValue('country', val);\n                  },\n                  getOptionLabel: option => {\n                    var _option$name;\n                    return (_option$name = option.name) !== null && _option$name !== void 0 ? _option$name : '';\n                  },\n                  renderOption: (props, option) => /*#__PURE__*/_jsxDEV(Box, {\n                    component: \"li\",\n                    sx: {\n                      '& > img': {\n                        mr: 2,\n                        flexShrink: 0\n                      }\n                    },\n                    ...props,\n                    children: [option.flag, \" \", option.name]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 249,\n                    columnNumber: 45\n                  }, this),\n                  renderInput: params => /*#__PURE__*/_jsxDEV(InputBase, {\n                    ...params.InputProps,\n                    ...params\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 253,\n                    columnNumber: 66\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 239,\n                  columnNumber: 37\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 237,\n                columnNumber: 33\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 236,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              lg: 6,\n              sm: 12,\n              xs: 12,\n              children: /*#__PURE__*/_jsxDEV(FormControl, {\n                fullWidth: true,\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"caption\",\n                  children: \"Phone Number\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 259,\n                  columnNumber: 37\n                }, this), /*#__PURE__*/_jsxDEV(Box, {\n                  sx: {\n                    display: 'flex',\n                    gap: 1.5\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(Box, {\n                    sx: {\n                      width: 100\n                    },\n                    children: /*#__PURE__*/_jsxDEV(Input, {\n                      sx: {\n                        textAlign: 'center',\n                        '& .Mui-disabled': {\n                          fillColor: theme.palette.common.black\n                        }\n                      },\n                      autoComplete: \"new-password\",\n                      name: \"phoneCode\",\n                      startAdornment: \"+\",\n                      type: \"number\",\n                      value: formik.values.phoneCode,\n                      onChange: formik.handleChange\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 265,\n                      columnNumber: 45\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 264,\n                    columnNumber: 41\n                  }, this), /*#__PURE__*/_jsxDEV(Input, {\n                    name: \"phoneNumber\",\n                    value: formik.values.phoneNumber,\n                    onChange: formik.handleChange\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 279,\n                    columnNumber: 41\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 260,\n                  columnNumber: 37\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 258,\n                columnNumber: 33\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 257,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              lg: 6,\n              sm: 12,\n              xs: 12,\n              children: /*#__PURE__*/_jsxDEV(Input, {\n                label: \"City\",\n                name: \"city\",\n                value: formik.values.city,\n                onChange: formik.handleChange\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 287,\n                columnNumber: 33\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 286,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              lg: 6,\n              sm: 12,\n              xs: 12,\n              children: /*#__PURE__*/_jsxDEV(Input, {\n                label: \"Address\",\n                name: \"address\",\n                value: formik.values.address,\n                onChange: formik.handleChange\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 294,\n                columnNumber: 33\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 293,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              lg: 6,\n              sm: 12,\n              xs: 12,\n              children: /*#__PURE__*/_jsxDEV(Input, {\n                label: \"Day\",\n                name: \"day\",\n                value: formik.values.day,\n                onChange: formik.handleChange\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 301,\n                columnNumber: 33\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 300,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              lg: 12,\n              sm: 12,\n              xs: 12,\n              children: /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"h6\",\n                children: \"HR Setting\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 308,\n                columnNumber: 33\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 307,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              lg: 6,\n              xs: 12,\n              children: /*#__PURE__*/_jsxDEV(Input, {\n                type: \"number\",\n                label: \"Leave Quote\",\n                name: \"leaveLimit\",\n                value: formik.values.leaveLimit,\n                onChange: formik.handleChange\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 311,\n                columnNumber: 33\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 310,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              lg: 12,\n              container: true,\n              justifyContent: \"flex-end\",\n              children: /*#__PURE__*/_jsxDEV(Button, {\n                type: \"submit\",\n                variant: \"contained\",\n                color: \"primary\",\n                disabled: !canUpdateSettings || !(setting !== null && setting !== void 0 && setting._id),\n                title: !canUpdateSettings ? 'You need permission to update settings' : 'Save settings',\n                children: canUpdateSettings ? 'Submit' : 'No Permission'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 319,\n                columnNumber: 30\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 318,\n              columnNumber: 29\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 219,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 202,\n          columnNumber: 25\n        }, this);\n      }, \"xUIWvyofBxLjQsbprvvQ3C6/u4g=\", false, function () {\n        return [useSelector];\n      })()\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 167,\n      columnNumber: 13\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 164,\n    columnNumber: 9\n  }, this);\n}\n_s2(Setting, \"bZpRXIWHCWw2rpo9LkRMmD9DNQM=\", false, function () {\n  return [useDispatch, useTheme, useSelector, useSelector, useSelector, useSelector, useSelector, useFormik];\n});\n_c = Setting;\nvar _c;\n$RefreshReg$(_c, \"Setting\");", "map": {"version": 3, "names": ["React", "useEffect", "Box", "<PERSON><PERSON>", "Card", "FormControl", "Grid", "InputBase", "Typography", "useTheme", "Page<PERSON><PERSON>le", "Input", "useFormik", "useDispatch", "useSelector", "GeneralActions", "SettingActions", "SettingSelector", "UserSelector", "Can", "Autocomplete", "COUNTRIES", "toast", "GeneralSelector", "FormSkeleton", "jsxDEV", "_jsxDEV", "Setting", "_s2", "_setting$name", "_setting$address", "_setting$city", "_setting$email", "_setting$phone", "_setting$leaveLimit", "_setting$day", "_s", "$RefreshSig$", "dispatch", "theme", "setting", "state", "loading", "profile", "countries", "map", "item", "id", "name", "phoneCode", "flag", "canUpdateSettings", "console", "log", "success", "updateSetting", "type", "error", "getSetting", "_success$message", "message", "position", "autoClose", "closeOnClick", "pauseOnHover", "removeSuccess", "includes", "removeError", "formik", "initialValues", "address", "city", "country", "email", "phone", "phoneNumber", "leaveLimit", "day", "enableReinitialize", "onSubmit", "values", "handleSubmit", "_formik$values$countr", "code", "setFieldValue", "_formik$values$countr2", "find", "e", "_formik$values$phoneC", "_formik$values$phoneN", "currentCode", "currentPhone", "substring", "length", "_values$country$name", "_values$country", "_id", "params", "children", "title", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "<PERSON><PERSON><PERSON><PERSON>", "general", "errors", "action", "sx", "p", "textAlign", "border", "borderRadius", "bgcolor", "my", "color", "variant", "gutterBottom", "mt", "mb", "fontWeight", "container", "spacing", "lg", "sm", "xs", "label", "value", "onChange", "handleChange", "touched", "Boolean", "helpertext", "fullWidth", "disable<PERSON><PERSON><PERSON>", "options", "val", "getOptionLabel", "option", "_option$name", "renderOption", "props", "component", "mr", "flexShrink", "renderInput", "InputProps", "display", "gap", "width", "fillColor", "palette", "common", "black", "autoComplete", "startAdornment", "justifyContent", "disabled", "_c", "$RefreshReg$"], "sources": ["E:/Suraj Patil/Organization_Management_System/meraki-frontend-main/src/screens/Setting/index.js"], "sourcesContent": ["import React, {useEffect} from \"react\";\r\nimport {Box, Button, Card, FormControl, Grid, InputBase, Typography, useTheme} from \"@mui/material\";\r\nimport PageTitle from \"../../components/PageTitle\";\r\nimport Input from \"../../components/Input\";\r\nimport {useFormik} from \"formik\";\r\nimport {useDispatch, useSelector} from \"react-redux\";\r\nimport {GeneralActions, SettingActions} from \"../../slices/actions\";\r\nimport {SettingSelector} from \"../../selectors/SettingSelector\";\r\nimport {UserSelector} from \"../../selectors/UserSelector\";\r\nimport Can from \"../../utils/can\";\r\nimport {Autocomplete} from \"@mui/lab\";\r\nimport COUNTRIES from \"../../constants/countries\";\r\nimport {toast} from \"react-toastify\";\r\nimport {GeneralSelector} from \"../../selectors\";\r\nimport FormSkeleton from \"../../components/Skeleton/FormSkeleton\";\r\n\r\nexport default function Setting() {\r\n    const dispatch = useDispatch();\r\n    const theme = useTheme();\r\nconst setting = useSelector(state => state.setting.setting);\r\nconst loading = useSelector(state => state.setting.loading);\r\n    const profile = useSelector(UserSelector.profile()) || {};\r\n    const countries = COUNTRIES.map(item => ({\r\n        id: item.id,\r\n        name: item.name,\r\n        phoneCode: item.phoneCode,\r\n        flag: item.flag\r\n    }));\r\n\r\n    // Check if user has permission to update settings\r\n    // This uses the Can utility function which checks for both admin role and specific permissions\r\n    const canUpdateSettings = Can('update', 'Setting');\r\n\r\n    // Debug user permissions\r\n    console.log('User profile:', profile);\r\n    console.log('Can update settings?', canUpdateSettings);\r\n\r\n    const success = useSelector(GeneralSelector.success(SettingActions.updateSetting.type));\r\n    const error = useSelector(GeneralSelector.error(SettingActions.updateSetting.type));\r\n\r\n    useEffect(() => {\r\n        dispatch(SettingActions.getSetting());\r\n    }, []);\r\n\r\n  useEffect(() => {\r\n    if (success) {\r\n        toast.success(`${success?.message ?? \"Success\"}`, {\r\n            position: \"top-right\",\r\n            autoClose: 3000,\r\n            closeOnClick: true,\r\n            pauseOnHover: false\r\n        });\r\n\r\n        dispatch(GeneralActions.removeSuccess(SettingActions.updateSetting.type));\r\n-       dispatch(SettingActions.getSetting());\r\n    }\r\n}, [success]);\r\n\r\n\r\n    // Handle errors from API calls\r\n    useEffect(() => {\r\n        if (error) {\r\n            // Check if it's a permission error (403)\r\n            if (error.message && error.message.includes('Forbidden')) {\r\n                toast.error('You need permission to update settings', {\r\n                    position: \"top-right\",\r\n                    autoClose: 5000,\r\n                    closeOnClick: true,\r\n                    pauseOnHover: true\r\n                });\r\n            } else {\r\n                toast.error(`${error.message || \"Failed to update settings\"}`, {\r\n                    position: \"top-right\",\r\n                    autoClose: 3000,\r\n                    closeOnClick: true,\r\n                    pauseOnHover: false\r\n                });\r\n            }\r\n\r\n            dispatch(GeneralActions.removeError(SettingActions.updateSetting.type));\r\n        }\r\n    }, [error]);\r\n\r\nconst formik = useFormik({\r\n    initialValues: {\r\n        name: setting?.name ?? '',\r\n        address: setting?.address ?? '',\r\n        city: setting?.city ?? '',\r\n        country: '',\r\n        email: setting?.email ?? '',\r\n        phone: setting?.phone ?? '',\r\n        phoneCode: '', // ✅ add this\r\n        phoneNumber: '', // ✅ and this\r\n        leaveLimit: setting?.leaveLimit ?? 0,\r\n        day: setting?.day ?? 0\r\n    },\r\n    enableReinitialize: true,\r\n    onSubmit: (values) => {\r\n        handleSubmit(values);\r\n    }\r\n});\r\n\r\n    useEffect(() => {\r\n        const code = formik.values.country?.phoneCode;\r\n        const phone = formik.values.phone;\r\n\r\n        formik.setFieldValue('phoneCode', code ?? '');\r\n        formik.setFieldValue('phone', phone);\r\n    }, [formik.values.country]);\r\n\r\n\r\nuseEffect(() => {\r\n    if (!setting) { return }\r\n\r\n    const phone = setting.phone || '';\r\n    const country = countries.find(e => e.name === setting?.country);\r\n\r\n    if (country && formik.values.country?.name !== country.name) {\r\n        formik.setFieldValue('country', country);\r\n    }\r\n\r\n    if (phone && country) {\r\n        const code = country.phoneCode || '';\r\n        const currentCode = formik.values.phoneCode ?? '';\r\n        const currentPhone = formik.values.phoneNumber ?? '';\r\n\r\n        if (currentCode !== code || currentPhone !== phone.substring(code.length)) {\r\n            formik.setFieldValue('phoneCode', code);\r\n            formik.setFieldValue('phoneNumber', phone.substring(code.length));\r\n        }\r\n    }\r\n}, [setting, countries]);\r\n\r\n\r\n\r\n  const handleSubmit = (values) => {\r\n    if (!setting || !setting._id) {\r\n        toast.error('Settings not loaded yet. Please wait a moment and try again.');\r\n        return;\r\n    }\r\n\r\n    if (!canUpdateSettings) {\r\n        toast.error('You need permission to update settings');\r\n        return;\r\n    }\r\n\r\n    const params = {\r\n        id: setting._id,\r\n        name: values.name,\r\n        address: values.address,\r\n        city: values.city,\r\n        country: values.country?.name ?? '',\r\n        email: values.email,\r\n        phone: (values.phoneCode || '') + (values.phoneNumber || ''),\r\n        leaveLimit: values.leaveLimit,\r\n        day: values.day\r\n    };\r\n\r\n    dispatch(SettingActions.updateSetting(params));\r\n};\r\n\r\n\r\n    return (\r\n        <Box>\r\n            <PageTitle title='Company Setting'/>\r\n\r\n            <Card>\r\n                {/* Extract the error check to a variable for cleaner code */}\r\n                {(() => {\r\n                    const hasError = useSelector(state =>\r\n                        state.general.errors.find(e => e.action === SettingActions.getSetting.type)\r\n                    );\r\n\r\n                    if (loading) {\r\n                        return <FormSkeleton />;\r\n                    }\r\n\r\n                    if (hasError) {\r\n                        return (\r\n                            <Box sx={{\r\n                                p: 3,\r\n                                textAlign: 'center',\r\n                                border: '1px solid #f0f0f0',\r\n                                borderRadius: 1,\r\n                                bgcolor: '#fff9f9',\r\n                                my: 2\r\n                            }}>\r\n                                <Typography color=\"error\" variant=\"h6\" gutterBottom>\r\n                                    Access Denied\r\n                                </Typography>\r\n                                <Typography variant=\"body1\">\r\n                                    You do not have permission to access settings\r\n                                </Typography>\r\n                                <Typography variant=\"body2\" color=\"textSecondary\" sx={{ mt: 1 }}>\r\n                                    Please contact your administrator if you believe you should have access.\r\n                                </Typography>\r\n                            </Box>\r\n                        );\r\n                    }\r\n\r\n                    return (\r\n                        <form onSubmit={formik.handleSubmit}>\r\n                            {!canUpdateSettings && (\r\n                                <Box sx={{\r\n                                    p: 2,\r\n                                    mb: 3,\r\n                                    border: '1px solid #ffcc80',\r\n                                    borderRadius: 1,\r\n                                    bgcolor: '#fff8e1',\r\n                                }}>\r\n                                    <Typography color=\"warning.main\" variant=\"subtitle1\" fontWeight=\"medium\">\r\n                                        View-Only Mode\r\n                                    </Typography>\r\n                                    <Typography variant=\"body2\">\r\n                                        You need permission to update settings. You can view the settings but cannot make changes.\r\n                                    </Typography>\r\n                                </Box>\r\n                            )}\r\n                        <Grid container spacing={3}>\r\n                            <Grid item lg={6} sm={12} xs={12}>\r\n                                <Input\r\n                                    label='Name'\r\n                                    name='name'\r\n                                    value={formik.values.name}\r\n                                    onChange={formik.handleChange}\r\n                                    error={formik.touched.name && Boolean(formik.errors.name)}\r\n                                   helpertext={formik.touched.name ? formik.errors.name : \"\"}/>\r\n                            </Grid>\r\n                            <Grid item lg={6} sm={12} xs={12}>\r\n                                <Input\r\n                                    label='Email'\r\n                                    name='email'\r\n                                    value={formik.values.email}\r\n                                    onChange={formik.handleChange}/>\r\n                            </Grid>\r\n                            <Grid item lg={6} sm={12} xs={12}>\r\n                                <FormControl fullWidth>\r\n                                    <Typography variant='caption'>Country</Typography>\r\n                                    <Autocomplete\r\n                                        disablePortal\r\n                                        name='country'\r\n                                        options={countries}\r\n                                        value={formik.values.country}\r\n                                        onChange={(e, val) => {\r\n                                            formik.setFieldValue('country', val);\r\n                                        }}\r\n                                        getOptionLabel={(option) => option.name ?? ''}\r\n                                        renderOption={(props, option) => (\r\n                                            <Box component=\"li\" sx={{ '& > img': { mr: 2, flexShrink: 0 } }} {...props}>\r\n                                                {option.flag} {option.name}\r\n                                            </Box>\r\n                                        )}\r\n                                        renderInput={(params) => <InputBase {...params.InputProps} {...params} />}\r\n                                    />\r\n                                </FormControl>\r\n                            </Grid>\r\n                            <Grid item lg={6} sm={12} xs={12}>\r\n                                <FormControl fullWidth>\r\n                                    <Typography variant='caption'>Phone Number</Typography>\r\n                                    <Box sx={{\r\n                                        display: 'flex',\r\n                                        gap: 1.5\r\n                                    }}>\r\n                                        <Box sx={{ width: 100 }}>\r\n                                            <Input\r\n                                                sx={{\r\n                                                    textAlign: 'center',\r\n                                                    '& .Mui-disabled': {\r\n                                                        fillColor: theme.palette.common.black\r\n                                                    }\r\n                                                }}\r\n                                                autoComplete='new-password'\r\n                                                name='phoneCode'\r\n                                                startAdornment='+'\r\n                                                type='number'\r\n                                                value={formik.values.phoneCode}\r\n                                                onChange={formik.handleChange}/>\r\n                                        </Box>\r\n                                        <Input\r\n                                            name='phoneNumber'\r\n                                            value={formik.values.phoneNumber}\r\n                                            onChange={formik.handleChange}/>\r\n                                    </Box>\r\n                                </FormControl>\r\n                            </Grid>\r\n                            <Grid item lg={6} sm={12} xs={12}>\r\n                                <Input\r\n                                    label=\"City\"\r\n                                    name='city'\r\n                                    value={formik.values.city}\r\n                                    onChange={formik.handleChange}/>\r\n                            </Grid>\r\n                            <Grid item lg={6} sm={12} xs={12}>\r\n                                <Input\r\n                                    label=\"Address\"\r\n                                    name='address'\r\n                                    value={formik.values.address}\r\n                                    onChange={formik.handleChange}/>\r\n                            </Grid>\r\n                            <Grid item lg={6} sm={12} xs={12}>\r\n                                <Input\r\n                                    label=\"Day\"\r\n                                    name='day'\r\n                                    value={formik.values.day}\r\n                                    onChange={formik.handleChange}/>\r\n                            </Grid>\r\n                            <Grid item lg={12} sm={12} xs={12}>\r\n                                <Typography variant='h6'>HR Setting</Typography>\r\n                            </Grid>\r\n                            <Grid item lg={6} xs={12}>\r\n                                <Input\r\n                                    type='number'\r\n                                    label=\"Leave Quote\"\r\n                                    name='leaveLimit'\r\n                                    value={formik.values.leaveLimit}\r\n                                    onChange={formik.handleChange}/>\r\n                            </Grid>\r\n                            <Grid item lg={12} container justifyContent='flex-end'>\r\n                             <Button\r\n    type='submit'\r\n    variant='contained'\r\n    color='primary'\r\n    disabled={!canUpdateSettings || !setting?._id}\r\n    title={!canUpdateSettings ? 'You need permission to update settings' : 'Save settings'}\r\n>\r\n    {canUpdateSettings ? 'Submit' : 'No Permission'}\r\n</Button>\r\n\r\n                            </Grid>\r\n                        </Grid>\r\n                    </form>\r\n                    );\r\n                })()}\r\n            </Card>\r\n        </Box>\r\n    )\r\n}"], "mappings": ";;AAAA,OAAOA,KAAK,IAAGC,SAAS,QAAO,OAAO;AACtC,SAAQC,GAAG,EAAEC,MAAM,EAAEC,IAAI,EAAEC,WAAW,EAAEC,IAAI,EAAEC,SAAS,EAAEC,UAAU,EAAEC,QAAQ,QAAO,eAAe;AACnG,OAAOC,SAAS,MAAM,4BAA4B;AAClD,OAAOC,KAAK,MAAM,wBAAwB;AAC1C,SAAQC,SAAS,QAAO,QAAQ;AAChC,SAAQC,WAAW,EAAEC,WAAW,QAAO,aAAa;AACpD,SAAQC,cAAc,EAAEC,cAAc,QAAO,sBAAsB;AACnE,SAAQC,eAAe,QAAO,iCAAiC;AAC/D,SAAQC,YAAY,QAAO,8BAA8B;AACzD,OAAOC,GAAG,MAAM,iBAAiB;AACjC,SAAQC,YAAY,QAAO,UAAU;AACrC,OAAOC,SAAS,MAAM,2BAA2B;AACjD,SAAQC,KAAK,QAAO,gBAAgB;AACpC,SAAQC,eAAe,QAAO,iBAAiB;AAC/C,OAAOC,YAAY,MAAM,wCAAwC;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAElE,eAAe,SAASC,OAAOA,CAAA,EAAG;EAAAC,GAAA;EAAA,IAAAC,aAAA;IAAAC,gBAAA;IAAAC,aAAA;IAAAC,cAAA;IAAAC,cAAA;IAAAC,mBAAA;IAAAC,YAAA;IAAAC,EAAA,GAAAC,YAAA;EAC9B,MAAMC,QAAQ,GAAGzB,WAAW,CAAC,CAAC;EAC9B,MAAM0B,KAAK,GAAG9B,QAAQ,CAAC,CAAC;EAC5B,MAAM+B,OAAO,GAAG1B,WAAW,CAAC2B,KAAK,IAAIA,KAAK,CAACD,OAAO,CAACA,OAAO,CAAC;EAC3D,MAAME,OAAO,GAAG5B,WAAW,CAAC2B,KAAK,IAAIA,KAAK,CAACD,OAAO,CAACE,OAAO,CAAC;EACvD,MAAMC,OAAO,GAAG7B,WAAW,CAACI,YAAY,CAACyB,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC;EACzD,MAAMC,SAAS,GAAGvB,SAAS,CAACwB,GAAG,CAACC,IAAI,KAAK;IACrCC,EAAE,EAAED,IAAI,CAACC,EAAE;IACXC,IAAI,EAAEF,IAAI,CAACE,IAAI;IACfC,SAAS,EAAEH,IAAI,CAACG,SAAS;IACzBC,IAAI,EAAEJ,IAAI,CAACI;EACf,CAAC,CAAC,CAAC;;EAEH;EACA;EACA,MAAMC,iBAAiB,GAAGhC,GAAG,CAAC,QAAQ,EAAE,SAAS,CAAC;;EAElD;EACAiC,OAAO,CAACC,GAAG,CAAC,eAAe,EAAEV,OAAO,CAAC;EACrCS,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAEF,iBAAiB,CAAC;EAEtD,MAAMG,OAAO,GAAGxC,WAAW,CAACS,eAAe,CAAC+B,OAAO,CAACtC,cAAc,CAACuC,aAAa,CAACC,IAAI,CAAC,CAAC;EACvF,MAAMC,KAAK,GAAG3C,WAAW,CAACS,eAAe,CAACkC,KAAK,CAACzC,cAAc,CAACuC,aAAa,CAACC,IAAI,CAAC,CAAC;EAEnFvD,SAAS,CAAC,MAAM;IACZqC,QAAQ,CAACtB,cAAc,CAAC0C,UAAU,CAAC,CAAC,CAAC;EACzC,CAAC,EAAE,EAAE,CAAC;EAERzD,SAAS,CAAC,MAAM;IACd,IAAIqD,OAAO,EAAE;MAAA,IAAAK,gBAAA;MACTrC,KAAK,CAACgC,OAAO,CAAC,IAAAK,gBAAA,GAAGL,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEM,OAAO,cAAAD,gBAAA,cAAAA,gBAAA,GAAI,SAAS,EAAE,EAAE;QAC9CE,QAAQ,EAAE,WAAW;QACrBC,SAAS,EAAE,IAAI;QACfC,YAAY,EAAE,IAAI;QAClBC,YAAY,EAAE;MAClB,CAAC,CAAC;MAEF1B,QAAQ,CAACvB,cAAc,CAACkD,aAAa,CAACjD,cAAc,CAACuC,aAAa,CAACC,IAAI,CAAC,CAAC;MACjF,CAAQlB,QAAQ,CAACtB,cAAc,CAAC0C,UAAU,CAAC,CAAC,CAAC;IACzC;EACJ,CAAC,EAAE,CAACJ,OAAO,CAAC,CAAC;;EAGT;EACArD,SAAS,CAAC,MAAM;IACZ,IAAIwD,KAAK,EAAE;MACP;MACA,IAAIA,KAAK,CAACG,OAAO,IAAIH,KAAK,CAACG,OAAO,CAACM,QAAQ,CAAC,WAAW,CAAC,EAAE;QACtD5C,KAAK,CAACmC,KAAK,CAAC,wCAAwC,EAAE;UAClDI,QAAQ,EAAE,WAAW;UACrBC,SAAS,EAAE,IAAI;UACfC,YAAY,EAAE,IAAI;UAClBC,YAAY,EAAE;QAClB,CAAC,CAAC;MACN,CAAC,MAAM;QACH1C,KAAK,CAACmC,KAAK,CAAC,GAAGA,KAAK,CAACG,OAAO,IAAI,2BAA2B,EAAE,EAAE;UAC3DC,QAAQ,EAAE,WAAW;UACrBC,SAAS,EAAE,IAAI;UACfC,YAAY,EAAE,IAAI;UAClBC,YAAY,EAAE;QAClB,CAAC,CAAC;MACN;MAEA1B,QAAQ,CAACvB,cAAc,CAACoD,WAAW,CAACnD,cAAc,CAACuC,aAAa,CAACC,IAAI,CAAC,CAAC;IAC3E;EACJ,CAAC,EAAE,CAACC,KAAK,CAAC,CAAC;EAEf,MAAMW,MAAM,GAAGxD,SAAS,CAAC;IACrByD,aAAa,EAAE;MACXrB,IAAI,GAAAnB,aAAA,GAAEW,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEQ,IAAI,cAAAnB,aAAA,cAAAA,aAAA,GAAI,EAAE;MACzByC,OAAO,GAAAxC,gBAAA,GAAEU,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAE8B,OAAO,cAAAxC,gBAAA,cAAAA,gBAAA,GAAI,EAAE;MAC/ByC,IAAI,GAAAxC,aAAA,GAAES,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAE+B,IAAI,cAAAxC,aAAA,cAAAA,aAAA,GAAI,EAAE;MACzByC,OAAO,EAAE,EAAE;MACXC,KAAK,GAAAzC,cAAA,GAAEQ,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEiC,KAAK,cAAAzC,cAAA,cAAAA,cAAA,GAAI,EAAE;MAC3B0C,KAAK,GAAAzC,cAAA,GAAEO,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEkC,KAAK,cAAAzC,cAAA,cAAAA,cAAA,GAAI,EAAE;MAC3BgB,SAAS,EAAE,EAAE;MAAE;MACf0B,WAAW,EAAE,EAAE;MAAE;MACjBC,UAAU,GAAA1C,mBAAA,GAAEM,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEoC,UAAU,cAAA1C,mBAAA,cAAAA,mBAAA,GAAI,CAAC;MACpC2C,GAAG,GAAA1C,YAAA,GAAEK,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEqC,GAAG,cAAA1C,YAAA,cAAAA,YAAA,GAAI;IACzB,CAAC;IACD2C,kBAAkB,EAAE,IAAI;IACxBC,QAAQ,EAAGC,MAAM,IAAK;MAClBC,YAAY,CAACD,MAAM,CAAC;IACxB;EACJ,CAAC,CAAC;EAEE/E,SAAS,CAAC,MAAM;IAAA,IAAAiF,qBAAA;IACZ,MAAMC,IAAI,IAAAD,qBAAA,GAAGd,MAAM,CAACY,MAAM,CAACR,OAAO,cAAAU,qBAAA,uBAArBA,qBAAA,CAAuBjC,SAAS;IAC7C,MAAMyB,KAAK,GAAGN,MAAM,CAACY,MAAM,CAACN,KAAK;IAEjCN,MAAM,CAACgB,aAAa,CAAC,WAAW,EAAED,IAAI,aAAJA,IAAI,cAAJA,IAAI,GAAI,EAAE,CAAC;IAC7Cf,MAAM,CAACgB,aAAa,CAAC,OAAO,EAAEV,KAAK,CAAC;EACxC,CAAC,EAAE,CAACN,MAAM,CAACY,MAAM,CAACR,OAAO,CAAC,CAAC;EAG/BvE,SAAS,CAAC,MAAM;IAAA,IAAAoF,sBAAA;IACZ,IAAI,CAAC7C,OAAO,EAAE;MAAE;IAAO;IAEvB,MAAMkC,KAAK,GAAGlC,OAAO,CAACkC,KAAK,IAAI,EAAE;IACjC,MAAMF,OAAO,GAAG5B,SAAS,CAAC0C,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACvC,IAAI,MAAKR,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEgC,OAAO,EAAC;IAEhE,IAAIA,OAAO,IAAI,EAAAa,sBAAA,GAAAjB,MAAM,CAACY,MAAM,CAACR,OAAO,cAAAa,sBAAA,uBAArBA,sBAAA,CAAuBrC,IAAI,MAAKwB,OAAO,CAACxB,IAAI,EAAE;MACzDoB,MAAM,CAACgB,aAAa,CAAC,SAAS,EAAEZ,OAAO,CAAC;IAC5C;IAEA,IAAIE,KAAK,IAAIF,OAAO,EAAE;MAAA,IAAAgB,qBAAA,EAAAC,qBAAA;MAClB,MAAMN,IAAI,GAAGX,OAAO,CAACvB,SAAS,IAAI,EAAE;MACpC,MAAMyC,WAAW,IAAAF,qBAAA,GAAGpB,MAAM,CAACY,MAAM,CAAC/B,SAAS,cAAAuC,qBAAA,cAAAA,qBAAA,GAAI,EAAE;MACjD,MAAMG,YAAY,IAAAF,qBAAA,GAAGrB,MAAM,CAACY,MAAM,CAACL,WAAW,cAAAc,qBAAA,cAAAA,qBAAA,GAAI,EAAE;MAEpD,IAAIC,WAAW,KAAKP,IAAI,IAAIQ,YAAY,KAAKjB,KAAK,CAACkB,SAAS,CAACT,IAAI,CAACU,MAAM,CAAC,EAAE;QACvEzB,MAAM,CAACgB,aAAa,CAAC,WAAW,EAAED,IAAI,CAAC;QACvCf,MAAM,CAACgB,aAAa,CAAC,aAAa,EAAEV,KAAK,CAACkB,SAAS,CAACT,IAAI,CAACU,MAAM,CAAC,CAAC;MACrE;IACJ;EACJ,CAAC,EAAE,CAACrD,OAAO,EAAEI,SAAS,CAAC,CAAC;EAItB,MAAMqC,YAAY,GAAID,MAAM,IAAK;IAAA,IAAAc,oBAAA,EAAAC,eAAA;IAC/B,IAAI,CAACvD,OAAO,IAAI,CAACA,OAAO,CAACwD,GAAG,EAAE;MAC1B1E,KAAK,CAACmC,KAAK,CAAC,8DAA8D,CAAC;MAC3E;IACJ;IAEA,IAAI,CAACN,iBAAiB,EAAE;MACpB7B,KAAK,CAACmC,KAAK,CAAC,wCAAwC,CAAC;MACrD;IACJ;IAEA,MAAMwC,MAAM,GAAG;MACXlD,EAAE,EAAEP,OAAO,CAACwD,GAAG;MACfhD,IAAI,EAAEgC,MAAM,CAAChC,IAAI;MACjBsB,OAAO,EAAEU,MAAM,CAACV,OAAO;MACvBC,IAAI,EAAES,MAAM,CAACT,IAAI;MACjBC,OAAO,GAAAsB,oBAAA,IAAAC,eAAA,GAAEf,MAAM,CAACR,OAAO,cAAAuB,eAAA,uBAAdA,eAAA,CAAgB/C,IAAI,cAAA8C,oBAAA,cAAAA,oBAAA,GAAI,EAAE;MACnCrB,KAAK,EAAEO,MAAM,CAACP,KAAK;MACnBC,KAAK,EAAE,CAACM,MAAM,CAAC/B,SAAS,IAAI,EAAE,KAAK+B,MAAM,CAACL,WAAW,IAAI,EAAE,CAAC;MAC5DC,UAAU,EAAEI,MAAM,CAACJ,UAAU;MAC7BC,GAAG,EAAEG,MAAM,CAACH;IAChB,CAAC;IAEDvC,QAAQ,CAACtB,cAAc,CAACuC,aAAa,CAAC0C,MAAM,CAAC,CAAC;EAClD,CAAC;EAGG,oBACIvE,OAAA,CAACxB,GAAG;IAAAgG,QAAA,gBACAxE,OAAA,CAAChB,SAAS;MAACyF,KAAK,EAAC;IAAiB;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAC,CAAC,eAEpC7E,OAAA,CAACtB,IAAI;MAAA8F,QAAA,EAEA9D,EAAA,CAAC,MAAM;QAAAA,EAAA;QACJ,MAAMoE,QAAQ,GAAG1F,WAAW,CAAC2B,KAAK,IAC9BA,KAAK,CAACgE,OAAO,CAACC,MAAM,CAACpB,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACoB,MAAM,KAAK3F,cAAc,CAAC0C,UAAU,CAACF,IAAI,CAC9E,CAAC;QAED,IAAId,OAAO,EAAE;UACT,oBAAOhB,OAAA,CAACF,YAAY;YAAA4E,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC;QAC3B;QAEA,IAAIC,QAAQ,EAAE;UACV,oBACI9E,OAAA,CAACxB,GAAG;YAAC0G,EAAE,EAAE;cACLC,CAAC,EAAE,CAAC;cACJC,SAAS,EAAE,QAAQ;cACnBC,MAAM,EAAE,mBAAmB;cAC3BC,YAAY,EAAE,CAAC;cACfC,OAAO,EAAE,SAAS;cAClBC,EAAE,EAAE;YACR,CAAE;YAAAhB,QAAA,gBACExE,OAAA,CAAClB,UAAU;cAAC2G,KAAK,EAAC,OAAO;cAACC,OAAO,EAAC,IAAI;cAACC,YAAY;cAAAnB,QAAA,EAAC;YAEpD;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACb7E,OAAA,CAAClB,UAAU;cAAC4G,OAAO,EAAC,OAAO;cAAAlB,QAAA,EAAC;YAE5B;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACb7E,OAAA,CAAClB,UAAU;cAAC4G,OAAO,EAAC,OAAO;cAACD,KAAK,EAAC,eAAe;cAACP,EAAE,EAAE;gBAAEU,EAAE,EAAE;cAAE,CAAE;cAAApB,QAAA,EAAC;YAEjE;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACZ,CAAC;QAEd;QAEA,oBACI7E,OAAA;UAAMqD,QAAQ,EAAEX,MAAM,CAACa,YAAa;UAAAiB,QAAA,GAC/B,CAAC/C,iBAAiB,iBACfzB,OAAA,CAACxB,GAAG;YAAC0G,EAAE,EAAE;cACLC,CAAC,EAAE,CAAC;cACJU,EAAE,EAAE,CAAC;cACLR,MAAM,EAAE,mBAAmB;cAC3BC,YAAY,EAAE,CAAC;cACfC,OAAO,EAAE;YACb,CAAE;YAAAf,QAAA,gBACExE,OAAA,CAAClB,UAAU;cAAC2G,KAAK,EAAC,cAAc;cAACC,OAAO,EAAC,WAAW;cAACI,UAAU,EAAC,QAAQ;cAAAtB,QAAA,EAAC;YAEzE;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACb7E,OAAA,CAAClB,UAAU;cAAC4G,OAAO,EAAC,OAAO;cAAAlB,QAAA,EAAC;YAE5B;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACZ,CACR,eACL7E,OAAA,CAACpB,IAAI;YAACmH,SAAS;YAACC,OAAO,EAAE,CAAE;YAAAxB,QAAA,gBACvBxE,OAAA,CAACpB,IAAI;cAACwC,IAAI;cAAC6E,EAAE,EAAE,CAAE;cAACC,EAAE,EAAE,EAAG;cAACC,EAAE,EAAE,EAAG;cAAA3B,QAAA,eAC7BxE,OAAA,CAACf,KAAK;gBACFmH,KAAK,EAAC,MAAM;gBACZ9E,IAAI,EAAC,MAAM;gBACX+E,KAAK,EAAE3D,MAAM,CAACY,MAAM,CAAChC,IAAK;gBAC1BgF,QAAQ,EAAE5D,MAAM,CAAC6D,YAAa;gBAC9BxE,KAAK,EAAEW,MAAM,CAAC8D,OAAO,CAAClF,IAAI,IAAImF,OAAO,CAAC/D,MAAM,CAACsC,MAAM,CAAC1D,IAAI,CAAE;gBAC3DoF,UAAU,EAAEhE,MAAM,CAAC8D,OAAO,CAAClF,IAAI,GAAGoB,MAAM,CAACsC,MAAM,CAAC1D,IAAI,GAAG;cAAG;gBAAAoD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC7D,CAAC,eACP7E,OAAA,CAACpB,IAAI;cAACwC,IAAI;cAAC6E,EAAE,EAAE,CAAE;cAACC,EAAE,EAAE,EAAG;cAACC,EAAE,EAAE,EAAG;cAAA3B,QAAA,eAC7BxE,OAAA,CAACf,KAAK;gBACFmH,KAAK,EAAC,OAAO;gBACb9E,IAAI,EAAC,OAAO;gBACZ+E,KAAK,EAAE3D,MAAM,CAACY,MAAM,CAACP,KAAM;gBAC3BuD,QAAQ,EAAE5D,MAAM,CAAC6D;cAAa;gBAAA7B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClC,CAAC,eACP7E,OAAA,CAACpB,IAAI;cAACwC,IAAI;cAAC6E,EAAE,EAAE,CAAE;cAACC,EAAE,EAAE,EAAG;cAACC,EAAE,EAAE,EAAG;cAAA3B,QAAA,eAC7BxE,OAAA,CAACrB,WAAW;gBAACgI,SAAS;gBAAAnC,QAAA,gBAClBxE,OAAA,CAAClB,UAAU;kBAAC4G,OAAO,EAAC,SAAS;kBAAAlB,QAAA,EAAC;gBAAO;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eAClD7E,OAAA,CAACN,YAAY;kBACTkH,aAAa;kBACbtF,IAAI,EAAC,SAAS;kBACduF,OAAO,EAAE3F,SAAU;kBACnBmF,KAAK,EAAE3D,MAAM,CAACY,MAAM,CAACR,OAAQ;kBAC7BwD,QAAQ,EAAEA,CAACzC,CAAC,EAAEiD,GAAG,KAAK;oBAClBpE,MAAM,CAACgB,aAAa,CAAC,SAAS,EAAEoD,GAAG,CAAC;kBACxC,CAAE;kBACFC,cAAc,EAAGC,MAAM;oBAAA,IAAAC,YAAA;oBAAA,QAAAA,YAAA,GAAKD,MAAM,CAAC1F,IAAI,cAAA2F,YAAA,cAAAA,YAAA,GAAI,EAAE;kBAAA,CAAC;kBAC9CC,YAAY,EAAEA,CAACC,KAAK,EAAEH,MAAM,kBACxBhH,OAAA,CAACxB,GAAG;oBAAC4I,SAAS,EAAC,IAAI;oBAAClC,EAAE,EAAE;sBAAE,SAAS,EAAE;wBAAEmC,EAAE,EAAE,CAAC;wBAAEC,UAAU,EAAE;sBAAE;oBAAE,CAAE;oBAAA,GAAKH,KAAK;oBAAA3C,QAAA,GACrEwC,MAAM,CAACxF,IAAI,EAAC,GAAC,EAACwF,MAAM,CAAC1F,IAAI;kBAAA;oBAAAoD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACzB,CACP;kBACF0C,WAAW,EAAGhD,MAAM,iBAAKvE,OAAA,CAACnB,SAAS;oBAAA,GAAK0F,MAAM,CAACiD,UAAU;oBAAA,GAAMjD;kBAAM;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAG;gBAAE;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC7E,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACO;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACZ,CAAC,eACP7E,OAAA,CAACpB,IAAI;cAACwC,IAAI;cAAC6E,EAAE,EAAE,CAAE;cAACC,EAAE,EAAE,EAAG;cAACC,EAAE,EAAE,EAAG;cAAA3B,QAAA,eAC7BxE,OAAA,CAACrB,WAAW;gBAACgI,SAAS;gBAAAnC,QAAA,gBAClBxE,OAAA,CAAClB,UAAU;kBAAC4G,OAAO,EAAC,SAAS;kBAAAlB,QAAA,EAAC;gBAAY;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACvD7E,OAAA,CAACxB,GAAG;kBAAC0G,EAAE,EAAE;oBACLuC,OAAO,EAAE,MAAM;oBACfC,GAAG,EAAE;kBACT,CAAE;kBAAAlD,QAAA,gBACExE,OAAA,CAACxB,GAAG;oBAAC0G,EAAE,EAAE;sBAAEyC,KAAK,EAAE;oBAAI,CAAE;oBAAAnD,QAAA,eACpBxE,OAAA,CAACf,KAAK;sBACFiG,EAAE,EAAE;wBACAE,SAAS,EAAE,QAAQ;wBACnB,iBAAiB,EAAE;0BACfwC,SAAS,EAAE/G,KAAK,CAACgH,OAAO,CAACC,MAAM,CAACC;wBACpC;sBACJ,CAAE;sBACFC,YAAY,EAAC,cAAc;sBAC3B1G,IAAI,EAAC,WAAW;sBAChB2G,cAAc,EAAC,GAAG;sBAClBnG,IAAI,EAAC,QAAQ;sBACbuE,KAAK,EAAE3D,MAAM,CAACY,MAAM,CAAC/B,SAAU;sBAC/B+E,QAAQ,EAAE5D,MAAM,CAAC6D;oBAAa;sBAAA7B,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAC;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACnC,CAAC,eACN7E,OAAA,CAACf,KAAK;oBACFqC,IAAI,EAAC,aAAa;oBAClB+E,KAAK,EAAE3D,MAAM,CAACY,MAAM,CAACL,WAAY;oBACjCqD,QAAQ,EAAE5D,MAAM,CAAC6D;kBAAa;oBAAA7B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAC,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACnC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACG;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACZ,CAAC,eACP7E,OAAA,CAACpB,IAAI;cAACwC,IAAI;cAAC6E,EAAE,EAAE,CAAE;cAACC,EAAE,EAAE,EAAG;cAACC,EAAE,EAAE,EAAG;cAAA3B,QAAA,eAC7BxE,OAAA,CAACf,KAAK;gBACFmH,KAAK,EAAC,MAAM;gBACZ9E,IAAI,EAAC,MAAM;gBACX+E,KAAK,EAAE3D,MAAM,CAACY,MAAM,CAACT,IAAK;gBAC1ByD,QAAQ,EAAE5D,MAAM,CAAC6D;cAAa;gBAAA7B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClC,CAAC,eACP7E,OAAA,CAACpB,IAAI;cAACwC,IAAI;cAAC6E,EAAE,EAAE,CAAE;cAACC,EAAE,EAAE,EAAG;cAACC,EAAE,EAAE,EAAG;cAAA3B,QAAA,eAC7BxE,OAAA,CAACf,KAAK;gBACFmH,KAAK,EAAC,SAAS;gBACf9E,IAAI,EAAC,SAAS;gBACd+E,KAAK,EAAE3D,MAAM,CAACY,MAAM,CAACV,OAAQ;gBAC7B0D,QAAQ,EAAE5D,MAAM,CAAC6D;cAAa;gBAAA7B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClC,CAAC,eACP7E,OAAA,CAACpB,IAAI;cAACwC,IAAI;cAAC6E,EAAE,EAAE,CAAE;cAACC,EAAE,EAAE,EAAG;cAACC,EAAE,EAAE,EAAG;cAAA3B,QAAA,eAC7BxE,OAAA,CAACf,KAAK;gBACFmH,KAAK,EAAC,KAAK;gBACX9E,IAAI,EAAC,KAAK;gBACV+E,KAAK,EAAE3D,MAAM,CAACY,MAAM,CAACH,GAAI;gBACzBmD,QAAQ,EAAE5D,MAAM,CAAC6D;cAAa;gBAAA7B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClC,CAAC,eACP7E,OAAA,CAACpB,IAAI;cAACwC,IAAI;cAAC6E,EAAE,EAAE,EAAG;cAACC,EAAE,EAAE,EAAG;cAACC,EAAE,EAAE,EAAG;cAAA3B,QAAA,eAC9BxE,OAAA,CAAClB,UAAU;gBAAC4G,OAAO,EAAC,IAAI;gBAAAlB,QAAA,EAAC;cAAU;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9C,CAAC,eACP7E,OAAA,CAACpB,IAAI;cAACwC,IAAI;cAAC6E,EAAE,EAAE,CAAE;cAACE,EAAE,EAAE,EAAG;cAAA3B,QAAA,eACrBxE,OAAA,CAACf,KAAK;gBACF6C,IAAI,EAAC,QAAQ;gBACbsE,KAAK,EAAC,aAAa;gBACnB9E,IAAI,EAAC,YAAY;gBACjB+E,KAAK,EAAE3D,MAAM,CAACY,MAAM,CAACJ,UAAW;gBAChCoD,QAAQ,EAAE5D,MAAM,CAAC6D;cAAa;gBAAA7B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClC,CAAC,eACP7E,OAAA,CAACpB,IAAI;cAACwC,IAAI;cAAC6E,EAAE,EAAE,EAAG;cAACF,SAAS;cAACmC,cAAc,EAAC,UAAU;cAAA1D,QAAA,eACrDxE,OAAA,CAACvB,MAAM;gBAChCqD,IAAI,EAAC,QAAQ;gBACb4D,OAAO,EAAC,WAAW;gBACnBD,KAAK,EAAC,SAAS;gBACf0C,QAAQ,EAAE,CAAC1G,iBAAiB,IAAI,EAACX,OAAO,aAAPA,OAAO,eAAPA,OAAO,CAAEwD,GAAG,CAAC;gBAC9CG,KAAK,EAAE,CAAChD,iBAAiB,GAAG,wCAAwC,GAAG,eAAgB;gBAAA+C,QAAA,EAEtF/C,iBAAiB,GAAG,QAAQ,GAAG;cAAe;gBAAAiD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC3C;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAEyB,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC;MAEX,CAAC;QAAA,QAnKoBzF,WAAW;MAAA,GAmK7B;IAAC;MAAAsF,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACN,CAAC;AAEd;AAAC3E,GAAA,CAhUuBD,OAAO;EAAA,QACVd,WAAW,EACdJ,QAAQ,EACVK,WAAW,EACXA,WAAW,EACPA,WAAW,EAgBXA,WAAW,EACbA,WAAW,EA6CdF,SAAS;AAAA;AAAAkJ,EAAA,GAnEAnI,OAAO;AAAA,IAAAmI,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}