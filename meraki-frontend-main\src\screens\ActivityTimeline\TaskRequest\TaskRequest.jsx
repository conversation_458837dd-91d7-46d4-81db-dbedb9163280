import React, { useState, useMemo, useEffect } from "react";
import {
  Box,
  Card,
  CardContent,
  Typography,
  Tabs,
  Tab,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Avatar,
  IconButton,
  Pagination,
  Select,
  MenuItem,
  FormControl,
  InputLabel,
} from "@mui/material";
import { CheckCircle, Cancel, Delete } from "@mui/icons-material";
import { TimelineSelector } from "selectors";
import { useDispatch, useSelector } from "react-redux";
import { TimelineActions } from "slices/actions";

const TaskRequests = () => {

  const timelineRequests = useSelector(TimelineSelector.getTimelineRequests());
  const [tabIndex, setTabIndex] = useState(0);
  const [team, setTeam] = useState("All Team");
  const dispatch = useDispatch();

  const handleTabChange = (event, newValue) => {
    setTabIndex(newValue);
  };
  const [filter, setFilter] = useState({
      sort: "toTime,-1",
      page: 1,
    });
    const handleChangePagination = (e, val) => {
      setFilter({
        ...filter,
        page: val,
      });
      console.log(" PAGINATION CALLED ",val)
    };
  
    const pagination = useSelector(TimelineSelector.getPagination());
  
    useEffect(() => {
      dispatch(TimelineActions.getTimelineRequests(filter))
     
      console.log("PAGINATION TIME REQUEST ",pagination)
    }, [filter]);

  const filteredRequests = useMemo(() => {
  return (timelineRequests || []).filter(request => {
    if (!request.taskDetails) { return false; }
    if (tabIndex === 0) { return request.taskDetails.status === "pending"; }
    if (tabIndex === 1) { return request.taskDetails.status === "approved"; }
    if (tabIndex === 2) { return request.taskDetails.status === "rejected"; }
    return true;
  });
}, [timelineRequests, tabIndex]);


  const timeFormat = (toTime, fromTime) => {
    const diff = new Date(toTime) - new Date(fromTime);
    const hours = Math.floor(diff / (1000 * 60 * 60));
    const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60));
    return `${hours}h ${minutes}m`;
  };

  const approveTask = (id,taskId) => {
    dispatch(
      TimelineActions.updateTaskTimelineRequest({
        id,
        taskId,
        body: { status: "approved" },
      })
    );
  };

  const rejectTask = (id,taskId) => {
    dispatch(
      TimelineActions.updateTaskTimelineRequest({
        id,
        taskId,
        body: { status: "rejected" },
      })
    );
  };

  const deleteTask = (id,taskId) => {
    dispatch(
      TimelineActions.deleteTaskTimelineRequest({
        id,
        taskId
      })
    );
  };

  const renderUserCell = (userName) => (
    <TableCell>
      <Box display="flex" alignItems="center">
        <Avatar sx={{ bgcolor: "#1976d2", mr: 1 }}>
          {userName?.charAt(0) || "U"}
        </Avatar>
        {userName || "Unknown User"}
      </Box>
    </TableCell>
  );

  const renderActionButtons = (data) => {

    if(!data.taskDetails) {
      return null;
    }
    if (tabIndex === 2) {
      return (
        <IconButton onClick={() => rejectTask(data._id,data.taskDetails._id)} color="delete">
          <Delete />
        </IconButton>
      );
    }
    return (
      <>
        <IconButton onClick={() => approveTask(data._id,data.taskDetails._id)} color="primary">
          <CheckCircle />
        </IconButton>
        <IconButton onClick={() => rejectTask(data._id,data.taskDetails._id)} color="error">
          <Cancel />
        </IconButton>
         <IconButton onClick={() => deleteTask(data._id,data.taskDetails._id)} color="delete">
          <Delete />
        </IconButton>
      </>
    );
  };

  return (
    <Box p={3}>
      <Card>
        <CardContent>
          <Box
            display="flex"
            justifyContent="space-between"
            alignItems="center"
            mb={2}
          >
            <Tabs value={tabIndex} onChange={handleTabChange}>
              <Tab label="Pending Requests" />
              <Tab label="Approved" />
              <Tab label="Denied" />
            </Tabs>
            <FormControl variant="outlined" size="small" sx={{ minWidth: 120 }}>
              <InputLabel>Team</InputLabel>
              <Select
                value={team}
                onChange={(e) => setTeam(e.target.value)}
                label="Team"
              >
                <MenuItem value="All Team">All Team</MenuItem>
                <MenuItem value="Team A">Team A</MenuItem>
                <MenuItem value="Team B">Team B</MenuItem>
              </Select>
            </FormControl>
          </Box>
          
          <TableContainer>
            <Table>
              <TableHead>
                <TableRow>
                  <TableCell>Name</TableCell>
                  <TableCell>Date</TableCell>
                  <TableCell>Time Range</TableCell>
                  <TableCell>Task Name</TableCell>
                  <TableCell>Action</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {filteredRequests.length === 0 ? (
                  <TableRow>
                    <TableCell colSpan={5} align="center">
                      No requests found
                    </TableCell>
                  </TableRow>
                ) : (
                  filteredRequests.map((request) => (
                    <TableRow key={request._id} hover>
                      {renderUserCell(request.taskDetails?.userName)}
                      <TableCell>
                        {new Date(request.fromTime).toDateString()}
                      </TableCell>
                      <TableCell>
                        {timeFormat(request.toTime, request.fromTime)}
                      </TableCell>
                      <TableCell>
                        {request.taskDetails?.taskTitle || "No Task Title"}
                      </TableCell>
                      <TableCell>
                        {renderActionButtons(request)}
                      </TableCell>
                    </TableRow>
                  ))
                )}
              </TableBody>
            </Table>
          </TableContainer>

          <Box
            mt={2}
            display="flex"
            justifyContent="space-between"
            alignItems="center"
          >
            <Typography variant="body2">Rows per page:</Typography>
            <Pagination count={Math.ceil(filteredRequests.length / 10)} shape="rounded" />
          </Box>
        </CardContent>
      </Card>
    </Box>
  );
};

export default TaskRequests;