const mongoose = require('mongoose');
const User = require('./models/user.model');

// Simulate the frontend's Can function
function Can(act, feat, profile) {
  console.log(`Checking permission: ${act} - ${feat}`);
  console.log('User profile:', JSON.stringify(profile, null, 2));
  
  // If no profile or permissions, deny access
  if (!profile || !profile.permissions || !Array.isArray(profile.permissions)) {
    // console.log(`Permission Check: ${act} - ${feat} = DENIED (No profile or permissions)`);
    return false;
  }
  
  // Normalize feature name (case-insensitive)
  const normalizedFeatLower = feat.toLowerCase();
  const FEATURE_MAPPING = {
    'setting': 'Setting',
    'settings': 'Setting',
  };
  const canonicalFeat = FEATURE_MAPPING[normalizedFeatLower] || feat;
  
  console.log(`Normalized feature: ${canonicalFeat}`);
  
  // Check if user has admin role
  const isAdmin = profile.role && profile.role.includes('admin');
  console.log('Is admin?', isAdmin);
  
  // Special case for admin users and Settings
  if (isAdmin && canonicalFeat === 'Setting') {
    // console.log(`Permission Check: ${act} - ${feat} = GRANTED (Admin special case for Settings)`);
    return true;
  }
  
  // Check for exact permission match
  const hasDirectPermission = profile.permissions.some(p => {
    // Skip invalid permissions
    if (!p || !p.feat || !Array.isArray(p.acts)) {
      return false;
    }
    
    // Normalize permission feature name
    const permFeatLower = p.feat.toLowerCase();
    const canonicalPermFeat = FEATURE_MAPPING[permFeatLower] || p.feat;
    
    // Check for feature match
    const featureMatches = canonicalPermFeat === canonicalFeat;
    // console.log(`Checking permission entry: ${p.feat} (normalized to ${canonicalPermFeat})`);
    // console.log(`Feature matches? ${featureMatches}`);
    
    // Basic permission check - if acts array is empty, treat as no permissions
    if (featureMatches && p.acts.length === 0) {
      // console.log(`Feature ${p.feat} has empty acts array, treating as no permissions`);
      return false;
    }
    
    // Basic permission check
    if (featureMatches && (p.acts.includes(act) || p.acts.includes('*'))) {
      // console.log(`Found direct permission match: ${p.feat} has action ${act}`);
      return true;
    }
    
    return false;
  });
  
  // console.log('Has direct permission?', hasDirectPermission);
  
  return hasDirectPermission;
}

async function testFrontendPermission() {
  try {
    await mongoose.connect('mongodb://localhost:27017/meraki');
    // console.log('Connected to MongoDB');
    
    const user = await User.findOne({email: '<EMAIL>'});
    
    if (!user) {
      // console.log('User not found');
      return;
    }
    
    // Convert Mongoose document to plain object
    const userObj = user.toObject();
    
    // console.log('\nTesting frontend permission check:');
    const canUpdateSettings = Can('update', 'Setting', userObj);
    // console.log('\nCan update settings?', canUpdateSettings);
    
  } catch (error) {
    console.error('Error:', error);
  } finally {
    await mongoose.disconnect();
    console.log('Disconnected from MongoDB');
  }
}

testFrontendPermission();
