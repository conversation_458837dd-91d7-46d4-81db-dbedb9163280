{"ast": null, "code": "var _jsxFileName = \"E:\\\\Suraj Patil\\\\Organization_Management_System\\\\meraki-frontend-main\\\\src\\\\components\\\\Input.js\";\nimport React from \"react\";\nimport { FormControl, FormHelperText, InputBase, Typography } from \"@mui/material\";\nimport PropTypes from \"prop-types\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nInput.propTypes = {\n  label: PropTypes.string,\n  helpertext: PropTypes.string,\n  error: PropTypes.bool\n};\nexport default function Input(props) {\n  const {\n    label,\n    helpertext,\n    error\n  } = props;\n  return /*#__PURE__*/_jsxDEV(FormControl, {\n    fullWidth: true,\n    children: [label && /*#__PURE__*/_jsxDEV(Typography, {\n      variant: \"caption\",\n      sx: {\n        textAlign: 'left'\n      },\n      children: label\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 17,\n      columnNumber: 17\n    }, this), /*#__PURE__*/_jsxDEV(InputBase, {\n      fullWidth: true,\n      helpertext: \"\",\n      ...props\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 19,\n      columnNumber: 13\n    }, this), helpertext && /*#__PURE__*/_jsxDEV(FormHelperText, {\n      error: error,\n      children: helpertext\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 24,\n      columnNumber: 17\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 15,\n    columnNumber: 9\n  }, this);\n}\n_c = Input;\nvar _c;\n$RefreshReg$(_c, \"Input\");", "map": {"version": 3, "names": ["React", "FormControl", "FormHelperText", "InputBase", "Typography", "PropTypes", "jsxDEV", "_jsxDEV", "Input", "propTypes", "label", "string", "helpertext", "error", "bool", "props", "fullWidth", "children", "variant", "sx", "textAlign", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["E:/Suraj Patil/Organization_Management_System/meraki-frontend-main/src/components/Input.js"], "sourcesContent": ["import React from \"react\";\r\nimport {FormControl, FormHelperText, InputBase, Typography} from \"@mui/material\";\r\nimport PropTypes from \"prop-types\";\r\n\r\nInput.propTypes = {\r\n    label: PropTypes.string,\r\n    helpertext: PropTypes.string,\r\n    error: PropTypes.bool\r\n};\r\n\r\nexport default function Input(props) {\r\n    const { label, helpertext, error } = props;\r\n\r\n    return (\r\n        <FormControl fullWidth>\r\n            {label && (\r\n                <Typography variant='caption' sx={{ textAlign: 'left' }}>{label}</Typography>\r\n            )}\r\n            <InputBase\r\n                fullWidth\r\n                helpertext=''\r\n                {...props}/>\r\n            {helpertext && (\r\n                <FormHelperText error={error}>{helpertext}</FormHelperText>\r\n            )}\r\n        </FormControl>\r\n    )\r\n}"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAAQC,WAAW,EAAEC,cAAc,EAAEC,SAAS,EAAEC,UAAU,QAAO,eAAe;AAChF,OAAOC,SAAS,MAAM,YAAY;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEnCC,KAAK,CAACC,SAAS,GAAG;EACdC,KAAK,EAAEL,SAAS,CAACM,MAAM;EACvBC,UAAU,EAAEP,SAAS,CAACM,MAAM;EAC5BE,KAAK,EAAER,SAAS,CAACS;AACrB,CAAC;AAED,eAAe,SAASN,KAAKA,CAACO,KAAK,EAAE;EACjC,MAAM;IAAEL,KAAK;IAAEE,UAAU;IAAEC;EAAM,CAAC,GAAGE,KAAK;EAE1C,oBACIR,OAAA,CAACN,WAAW;IAACe,SAAS;IAAAC,QAAA,GACjBP,KAAK,iBACFH,OAAA,CAACH,UAAU;MAACc,OAAO,EAAC,SAAS;MAACC,EAAE,EAAE;QAAEC,SAAS,EAAE;MAAO,CAAE;MAAAH,QAAA,EAAEP;IAAK;MAAAW,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAa,CAC/E,eACDjB,OAAA,CAACJ,SAAS;MACNa,SAAS;MACTJ,UAAU,EAAC,EAAE;MAAA,GACTG;IAAK;MAAAM,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,EACfZ,UAAU,iBACPL,OAAA,CAACL,cAAc;MAACW,KAAK,EAAEA,KAAM;MAAAI,QAAA,EAAEL;IAAU;MAAAS,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAiB,CAC7D;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACQ,CAAC;AAEtB;AAACC,EAAA,GAjBuBjB,KAAK;AAAA,IAAAiB,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}