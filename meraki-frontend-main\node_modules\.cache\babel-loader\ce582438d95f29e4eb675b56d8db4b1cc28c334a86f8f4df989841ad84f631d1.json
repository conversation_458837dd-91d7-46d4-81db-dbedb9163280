{"ast": null, "code": "import store from \"../store\";\n\n// Enable or disable permission logging (set to true for debugging)\nconst ENABLE_LOGGING = true;\n\n// Add timestamp to logs for easier debugging\nconst log = (...args) => {\n  if (ENABLE_LOGGING) {\n    console.log(`[${new Date().toISOString()}]`, ...args);\n  }\n};\n\n/**\r\n * Normalized feature names mapping\r\n * This maps various feature name formats to their canonical form\r\n */\nconst FEATURE_MAPPING = {\n  // Dashboard\n  'dashboard': 'Dashboard',\n  'user-dashboard': 'Dashboard',\n  'admin-dashboard': 'Dashboard',\n  // User Management\n  'user': 'User',\n  'users': 'User',\n  // Tasks\n  'tasks': 'Tasks',\n  'my tasks': 'Tasks',\n  'mytasks': 'Tasks',\n  'task': 'Tasks',\n  // Projects\n  'projects': 'Project',\n  'project': 'Project',\n  'project list': 'Project List',\n  'projectlist': 'Project List',\n  'project overview': 'Project Overview',\n  'projectoverview': 'Project Overview',\n  'project timesheet': 'Project Timesheet',\n  'projecttimesheet': 'Project Timesheet',\n  // Attendance\n  'attendance': 'Attendance',\n  'my attendance': 'Attendance',\n  // Leave\n  'leave': 'Leave',\n  'my leave': 'Leave',\n  'leave report': 'Leave Report',\n  'leavereport': 'Leave Report',\n  // Timeline\n  'timeline': 'Timeline',\n  'my timeline': 'Timeline',\n  // Expense\n  'expense': 'Expense',\n  'expenses': 'Expense',\n  'my expense': 'Expense',\n  'my expenses': 'Expense',\n  // Setting\n  'setting': 'Setting',\n  'settings': 'Setting'\n};\n\n/**\r\n * Simplified permission check function\r\n * @param {string} act - The action to check (e.g., 'read', 'create')\r\n * @param {string} feat - The feature to check (e.g., 'User', 'Attendance')\r\n * @returns {boolean} - Whether the user has permission\r\n */\nexport default function Can(act, feat) {\n  const state = store.getState();\n  const profile = state.user.profile;\n\n  // If no profile or permissions, deny access\n  if (!profile || !profile.permissions || !Array.isArray(profile.permissions)) {\n    if (ENABLE_LOGGING) {\n      // console.log(`Permission Check: ${act} - ${feat} = DENIED (No profile or permissions)`);\n    }\n    return false;\n  }\n\n  // Normalize feature name (case-insensitive lookup)\n  const normalizedFeatLower = feat.toLowerCase();\n  const canonicalFeat = FEATURE_MAPPING[normalizedFeatLower] || feat;\n  if (ENABLE_LOGGING) {\n    // console.log(`Checking permission: ${act} - ${feat} (normalized to ${canonicalFeat})`);\n    // console.log('User permissions:', profile.permissions);\n  }\n\n  // Check if user has admin role\n  const isAdmin = profile.role && profile.role.includes('admin');\n\n  // Special case for dashboard - always allow access\n  if (canonicalFeat === 'Dashboard') {\n    if (ENABLE_LOGGING) {\n      // console.log(`Permission Check: ${act} - ${feat} = GRANTED (Dashboard special case)`);\n    }\n    return true;\n  }\n\n  // Special case for admin users and Employee Management\n  if (isAdmin && canonicalFeat === 'User') {\n    if (ENABLE_LOGGING) {\n      // console.log(`Permission Check: ${act} - ${feat} = GRANTED (Admin special case for Employee Management)`);\n    }\n    return true;\n  }\n\n  // Special case for admin users and Settings\n  if (isAdmin && canonicalFeat === 'Setting') {\n    if (ENABLE_LOGGING) {\n      // console.log(`Permission Check: ${act} - ${feat} = GRANTED (Admin special case for Settings)`);\n    }\n    return true;\n  }\n\n  // Special case for read permission when user has read_all\n  // This ensures that if a user has read_all, they also have read permission\n  if (act === 'read') {\n    const hasReadAll = profile.permissions.some(p => p.feat === canonicalFeat && p.acts && p.acts.includes('read_all'));\n    if (hasReadAll) {\n      if (ENABLE_LOGGING) {\n        // console.log(`Permission Check: ${act} - ${feat} = GRANTED (User has read_all permission)`);\n      }\n      return true;\n    }\n  }\n\n  // Check for parent-child menu permission inheritance\n  // If a feature has a parent (e.g., \"Leave Report\" is a child of \"Leave\"),\n  // and the user has permission for the parent, grant permission for the child\n  const parentFeatureMap = {\n    // Leave Management children\n    'Leave Report': 'Leave',\n    'Approve': 'Leave',\n    // Project Management children\n    'Project Overview': 'Project',\n    'Project List': 'Project',\n    'Project Timesheet': 'Project',\n    // Expense Management children\n    'Expense Report': 'Expense',\n    'Expense Approval': 'Expense'\n\n    // Add more parent-child relationships as needed\n  };\n\n  // If this feature has a parent defined in the map\n  if (parentFeatureMap[canonicalFeat]) {\n    const parentFeat = parentFeatureMap[canonicalFeat];\n\n    // Check if user has the same permission for the parent feature\n    const hasParentPermission = profile.permissions.some(p => {\n      // Handle both \"Project\" and \"Projects\" for parent feature\n      const parentMatches = p.feat === parentFeat || parentFeat === 'Project' && p.feat === 'Projects' || parentFeat === 'Projects' && p.feat === 'Project';\n      return parentMatches && p.acts && p.acts.includes(act);\n    });\n    if (hasParentPermission) {\n      if (ENABLE_LOGGING) {\n        // console.log(`Permission Check: ${act} - ${feat} = GRANTED (User has permission for parent feature ${parentFeat})`);\n      }\n      return true;\n    }\n\n    // If the action is 'read' and the user has 'read_all' for the parent\n    if (act === 'read') {\n      const hasParentReadAll = profile.permissions.some(p => {\n        // Handle both \"Project\" and \"Projects\" for parent feature\n        const parentMatches = p.feat === parentFeat || parentFeat === 'Project' && p.feat === 'Projects' || parentFeat === 'Projects' && p.feat === 'Project';\n        return parentMatches && p.acts && p.acts.includes('read_all');\n      });\n      if (hasParentReadAll) {\n        if (ENABLE_LOGGING) {\n          // console.log(`Permission Check: ${act} - ${feat} = GRANTED (User has read_all permission for parent feature ${parentFeat})`);\n        }\n        return true;\n      }\n    }\n  }\n\n  // Check for exact permission match\n  const hasDirectPermission = profile.permissions.some(p => {\n    // Skip invalid permissions\n    if (!p || !p.feat || !Array.isArray(p.acts)) {\n      return false;\n    }\n\n    // Normalize permission feature name\n    const permFeatLower = p.feat.toLowerCase();\n    const canonicalPermFeat = FEATURE_MAPPING[permFeatLower] || p.feat;\n\n    // Check for feature match\n    const featureMatches = canonicalPermFeat === canonicalFeat;\n\n    // Basic permission check - if acts array is empty, treat as no permissions\n    if (featureMatches && p.acts.length === 0) {\n      if (ENABLE_LOGGING) {\n        // console.log(`Feature ${p.feat} has empty acts array, treating as no permissions`);\n      }\n      return false;\n    }\n\n    // Basic permission check\n    if (featureMatches && (p.acts.includes(act) || p.acts.includes('*'))) {\n      return true;\n    }\n\n    // For 'read' permission, also allow if user has higher permissions\n    if (act === 'read' && featureMatches) {\n      return p.acts.includes('create') || p.acts.includes('update') || p.acts.includes('delete') || p.acts.includes('read_all') || p.acts.includes('read_some') || p.acts.includes('read_self');\n    }\n\n    // For specialized read permissions\n    if (act === 'read_all' && featureMatches) {\n      return p.acts.includes('read_all');\n    }\n    return false;\n  });\n\n  // Check for wildcard permission\n  const hasWildcardPermission = profile.permissions.some(p => p.feat === '*' && (p.acts.includes(act) || p.acts.includes('*')));\n\n  // Log the result\n  if (ENABLE_LOGGING) {\n    if (hasDirectPermission) {\n      // console.log(`Permission Check: ${act} - ${feat} = GRANTED (Direct match)`);\n    } else if (hasWildcardPermission) {\n      // console.log(`Permission Check: ${act} - ${feat} = GRANTED (Wildcard)`);\n    } else {\n      // console.log(`Permission Check: ${act} - ${feat} = DENIED (No matching permission)`);\n    }\n  }\n  return hasDirectPermission || hasWildcardPermission;\n}\n_c = Can;\nvar _c;\n$RefreshReg$(_c, \"Can\");", "map": {"version": 3, "names": ["store", "ENABLE_LOGGING", "log", "args", "console", "Date", "toISOString", "FEATURE_MAPPING", "Can", "act", "feat", "state", "getState", "profile", "user", "permissions", "Array", "isArray", "normalizedFeatLower", "toLowerCase", "canonicalFeat", "isAdmin", "role", "includes", "hasReadAll", "some", "p", "acts", "parentFeatureMap", "parentFeat", "hasParentPermission", "parentMatches", "hasParentReadAll", "hasDirectPermission", "permFeatLower", "canonicalPermFeat", "featureMatches", "length", "hasWildcardPermission", "_c", "$RefreshReg$"], "sources": ["E:/Suraj Patil/Organization_Management_System/meraki-frontend-main/src/utils/can.js"], "sourcesContent": ["import store from \"../store\";\r\n\r\n// Enable or disable permission logging (set to true for debugging)\r\nconst ENABLE_LOGGING = true;\r\n\r\n// Add timestamp to logs for easier debugging\r\nconst log = (...args) => {\r\n  if (ENABLE_LOGGING) {\r\n    console.log(`[${new Date().toISOString()}]`, ...args);\r\n  }\r\n};\r\n\r\n/**\r\n * Normalized feature names mapping\r\n * This maps various feature name formats to their canonical form\r\n */\r\nconst FEATURE_MAPPING = {\r\n  // Dashboard\r\n  'dashboard': 'Dashboard',\r\n  'user-dashboard': 'Dashboard',\r\n  'admin-dashboard': 'Dashboard',\r\n\r\n  // User Management\r\n  'user': 'User',\r\n  'users': 'User',\r\n\r\n  // Tasks\r\n  'tasks': 'Tasks',\r\n  'my tasks': 'Tasks',\r\n  'mytasks': 'Tasks',\r\n  'task': 'Tasks',\r\n\r\n  // Projects\r\n  'projects': 'Project',\r\n  'project': 'Project',\r\n  'project list': 'Project List',\r\n  'projectlist': 'Project List',\r\n  'project overview': 'Project Overview',\r\n  'projectoverview': 'Project Overview',\r\n  'project timesheet': 'Project Timesheet',\r\n  'projecttimesheet': 'Project Timesheet',\r\n\r\n  // Attendance\r\n  'attendance': 'Attendance',\r\n  'my attendance': 'Attendance',\r\n\r\n  // Leave\r\n  'leave': 'Leave',\r\n  'my leave': 'Leave',\r\n  'leave report': 'Leave Report',\r\n  'leavereport': 'Leave Report',\r\n\r\n  // Timeline\r\n  'timeline': 'Timeline',\r\n  'my timeline': 'Timeline',\r\n\r\n  // Expense\r\n  'expense': 'Expense',\r\n  'expenses': 'Expense',\r\n  'my expense': 'Expense',\r\n  'my expenses': 'Expense',\r\n\r\n  // Setting\r\n  'setting': 'Setting',\r\n  'settings': 'Setting',\r\n};\r\n\r\n/**\r\n * Simplified permission check function\r\n * @param {string} act - The action to check (e.g., 'read', 'create')\r\n * @param {string} feat - The feature to check (e.g., 'User', 'Attendance')\r\n * @returns {boolean} - Whether the user has permission\r\n */\r\nexport default function Can(act, feat) {\r\n    const state = store.getState();\r\n    const profile = state.user.profile;\r\n\r\n    // If no profile or permissions, deny access\r\n    if (!profile || !profile.permissions || !Array.isArray(profile.permissions)) {\r\n        if (ENABLE_LOGGING) {\r\n            // console.log(`Permission Check: ${act} - ${feat} = DENIED (No profile or permissions)`);\r\n        }\r\n        return false;\r\n    }\r\n\r\n    // Normalize feature name (case-insensitive lookup)\r\n    const normalizedFeatLower = feat.toLowerCase();\r\n    const canonicalFeat = FEATURE_MAPPING[normalizedFeatLower] || feat;\r\n\r\n    if (ENABLE_LOGGING) {\r\n        // console.log(`Checking permission: ${act} - ${feat} (normalized to ${canonicalFeat})`);\r\n        // console.log('User permissions:', profile.permissions);\r\n    }\r\n\r\n    // Check if user has admin role\r\n    const isAdmin = profile.role && profile.role.includes('admin');\r\n\r\n    // Special case for dashboard - always allow access\r\n    if (canonicalFeat === 'Dashboard') {\r\n        if (ENABLE_LOGGING) {\r\n            // console.log(`Permission Check: ${act} - ${feat} = GRANTED (Dashboard special case)`);\r\n        }\r\n        return true;\r\n    }\r\n\r\n    // Special case for admin users and Employee Management\r\n    if (isAdmin && canonicalFeat === 'User') {\r\n        if (ENABLE_LOGGING) {\r\n            // console.log(`Permission Check: ${act} - ${feat} = GRANTED (Admin special case for Employee Management)`);\r\n        }\r\n        return true;\r\n    }\r\n\r\n    // Special case for admin users and Settings\r\n    if (isAdmin && canonicalFeat === 'Setting') {\r\n        if (ENABLE_LOGGING) {\r\n            // console.log(`Permission Check: ${act} - ${feat} = GRANTED (Admin special case for Settings)`);\r\n        }\r\n        return true;\r\n    }\r\n\r\n    // Special case for read permission when user has read_all\r\n    // This ensures that if a user has read_all, they also have read permission\r\n    if (act === 'read') {\r\n        const hasReadAll = profile.permissions.some(p =>\r\n            p.feat === canonicalFeat &&\r\n            p.acts &&\r\n            p.acts.includes('read_all')\r\n        );\r\n\r\n        if (hasReadAll) {\r\n            if (ENABLE_LOGGING) {\r\n                // console.log(`Permission Check: ${act} - ${feat} = GRANTED (User has read_all permission)`);\r\n            }\r\n            return true;\r\n        }\r\n    }\r\n\r\n    // Check for parent-child menu permission inheritance\r\n    // If a feature has a parent (e.g., \"Leave Report\" is a child of \"Leave\"),\r\n    // and the user has permission for the parent, grant permission for the child\r\n    const parentFeatureMap = {\r\n        // Leave Management children\r\n        'Leave Report': 'Leave',\r\n        'Approve': 'Leave',\r\n\r\n        // Project Management children\r\n        'Project Overview': 'Project',\r\n        'Project List': 'Project',\r\n        'Project Timesheet': 'Project',\r\n\r\n        // Expense Management children\r\n        'Expense Report': 'Expense',\r\n        'Expense Approval': 'Expense',\r\n\r\n        // Add more parent-child relationships as needed\r\n    };\r\n\r\n    // If this feature has a parent defined in the map\r\n    if (parentFeatureMap[canonicalFeat]) {\r\n        const parentFeat = parentFeatureMap[canonicalFeat];\r\n\r\n        // Check if user has the same permission for the parent feature\r\n        const hasParentPermission = profile.permissions.some(p => {\r\n            // Handle both \"Project\" and \"Projects\" for parent feature\r\n            const parentMatches =\r\n                p.feat === parentFeat ||\r\n                (parentFeat === 'Project' && p.feat === 'Projects') ||\r\n                (parentFeat === 'Projects' && p.feat === 'Project');\r\n\r\n            return parentMatches && p.acts && p.acts.includes(act);\r\n        });\r\n\r\n        if (hasParentPermission) {\r\n            if (ENABLE_LOGGING) {\r\n                // console.log(`Permission Check: ${act} - ${feat} = GRANTED (User has permission for parent feature ${parentFeat})`);\r\n            }\r\n            return true;\r\n        }\r\n\r\n        // If the action is 'read' and the user has 'read_all' for the parent\r\n        if (act === 'read') {\r\n            const hasParentReadAll = profile.permissions.some(p => {\r\n                // Handle both \"Project\" and \"Projects\" for parent feature\r\n                const parentMatches =\r\n                    p.feat === parentFeat ||\r\n                    (parentFeat === 'Project' && p.feat === 'Projects') ||\r\n                    (parentFeat === 'Projects' && p.feat === 'Project');\r\n\r\n                return parentMatches && p.acts && p.acts.includes('read_all');\r\n            });\r\n\r\n            if (hasParentReadAll) {\r\n                if (ENABLE_LOGGING) {\r\n                    // console.log(`Permission Check: ${act} - ${feat} = GRANTED (User has read_all permission for parent feature ${parentFeat})`);\r\n                }\r\n                return true;\r\n            }\r\n        }\r\n    }\r\n\r\n    // Check for exact permission match\r\n    const hasDirectPermission = profile.permissions.some(p => {\r\n        // Skip invalid permissions\r\n        if (!p || !p.feat || !Array.isArray(p.acts)) {\r\n            return false;\r\n        }\r\n\r\n        // Normalize permission feature name\r\n        const permFeatLower = p.feat.toLowerCase();\r\n        const canonicalPermFeat = FEATURE_MAPPING[permFeatLower] || p.feat;\r\n\r\n        // Check for feature match\r\n        const featureMatches = canonicalPermFeat === canonicalFeat;\r\n\r\n        // Basic permission check - if acts array is empty, treat as no permissions\r\n        if (featureMatches && p.acts.length === 0) {\r\n            if (ENABLE_LOGGING) {\r\n                // console.log(`Feature ${p.feat} has empty acts array, treating as no permissions`);\r\n            }\r\n            return false;\r\n        }\r\n\r\n        // Basic permission check\r\n        if (featureMatches && (p.acts.includes(act) || p.acts.includes('*'))) {\r\n            return true;\r\n        }\r\n\r\n        // For 'read' permission, also allow if user has higher permissions\r\n        if (act === 'read' && featureMatches) {\r\n            return p.acts.includes('create') ||\r\n                   p.acts.includes('update') ||\r\n                   p.acts.includes('delete') ||\r\n                   p.acts.includes('read_all') ||\r\n                   p.acts.includes('read_some') ||\r\n                   p.acts.includes('read_self');\r\n        }\r\n\r\n        // For specialized read permissions\r\n        if (act === 'read_all' && featureMatches) {\r\n            return p.acts.includes('read_all');\r\n        }\r\n\r\n        return false;\r\n    });\r\n\r\n    // Check for wildcard permission\r\n    const hasWildcardPermission = profile.permissions.some(p =>\r\n        p.feat === '*' && (p.acts.includes(act) || p.acts.includes('*'))\r\n    );\r\n\r\n    // Log the result\r\n    if (ENABLE_LOGGING) {\r\n        if (hasDirectPermission) {\r\n            // console.log(`Permission Check: ${act} - ${feat} = GRANTED (Direct match)`);\r\n        } else if (hasWildcardPermission) {\r\n            // console.log(`Permission Check: ${act} - ${feat} = GRANTED (Wildcard)`);\r\n        } else {\r\n            // console.log(`Permission Check: ${act} - ${feat} = DENIED (No matching permission)`);\r\n        }\r\n    }\r\n\r\n    return hasDirectPermission || hasWildcardPermission;\r\n}"], "mappings": "AAAA,OAAOA,KAAK,MAAM,UAAU;;AAE5B;AACA,MAAMC,cAAc,GAAG,IAAI;;AAE3B;AACA,MAAMC,GAAG,GAAGA,CAAC,GAAGC,IAAI,KAAK;EACvB,IAAIF,cAAc,EAAE;IAClBG,OAAO,CAACF,GAAG,CAAC,IAAI,IAAIG,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,GAAG,EAAE,GAAGH,IAAI,CAAC;EACvD;AACF,CAAC;;AAED;AACA;AACA;AACA;AACA,MAAMI,eAAe,GAAG;EACtB;EACA,WAAW,EAAE,WAAW;EACxB,gBAAgB,EAAE,WAAW;EAC7B,iBAAiB,EAAE,WAAW;EAE9B;EACA,MAAM,EAAE,MAAM;EACd,OAAO,EAAE,MAAM;EAEf;EACA,OAAO,EAAE,OAAO;EAChB,UAAU,EAAE,OAAO;EACnB,SAAS,EAAE,OAAO;EAClB,MAAM,EAAE,OAAO;EAEf;EACA,UAAU,EAAE,SAAS;EACrB,SAAS,EAAE,SAAS;EACpB,cAAc,EAAE,cAAc;EAC9B,aAAa,EAAE,cAAc;EAC7B,kBAAkB,EAAE,kBAAkB;EACtC,iBAAiB,EAAE,kBAAkB;EACrC,mBAAmB,EAAE,mBAAmB;EACxC,kBAAkB,EAAE,mBAAmB;EAEvC;EACA,YAAY,EAAE,YAAY;EAC1B,eAAe,EAAE,YAAY;EAE7B;EACA,OAAO,EAAE,OAAO;EAChB,UAAU,EAAE,OAAO;EACnB,cAAc,EAAE,cAAc;EAC9B,aAAa,EAAE,cAAc;EAE7B;EACA,UAAU,EAAE,UAAU;EACtB,aAAa,EAAE,UAAU;EAEzB;EACA,SAAS,EAAE,SAAS;EACpB,UAAU,EAAE,SAAS;EACrB,YAAY,EAAE,SAAS;EACvB,aAAa,EAAE,SAAS;EAExB;EACA,SAAS,EAAE,SAAS;EACpB,UAAU,EAAE;AACd,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA,eAAe,SAASC,GAAGA,CAACC,GAAG,EAAEC,IAAI,EAAE;EACnC,MAAMC,KAAK,GAAGX,KAAK,CAACY,QAAQ,CAAC,CAAC;EAC9B,MAAMC,OAAO,GAAGF,KAAK,CAACG,IAAI,CAACD,OAAO;;EAElC;EACA,IAAI,CAACA,OAAO,IAAI,CAACA,OAAO,CAACE,WAAW,IAAI,CAACC,KAAK,CAACC,OAAO,CAACJ,OAAO,CAACE,WAAW,CAAC,EAAE;IACzE,IAAId,cAAc,EAAE;MAChB;IAAA;IAEJ,OAAO,KAAK;EAChB;;EAEA;EACA,MAAMiB,mBAAmB,GAAGR,IAAI,CAACS,WAAW,CAAC,CAAC;EAC9C,MAAMC,aAAa,GAAGb,eAAe,CAACW,mBAAmB,CAAC,IAAIR,IAAI;EAElE,IAAIT,cAAc,EAAE;IAChB;IACA;EAAA;;EAGJ;EACA,MAAMoB,OAAO,GAAGR,OAAO,CAACS,IAAI,IAAIT,OAAO,CAACS,IAAI,CAACC,QAAQ,CAAC,OAAO,CAAC;;EAE9D;EACA,IAAIH,aAAa,KAAK,WAAW,EAAE;IAC/B,IAAInB,cAAc,EAAE;MAChB;IAAA;IAEJ,OAAO,IAAI;EACf;;EAEA;EACA,IAAIoB,OAAO,IAAID,aAAa,KAAK,MAAM,EAAE;IACrC,IAAInB,cAAc,EAAE;MAChB;IAAA;IAEJ,OAAO,IAAI;EACf;;EAEA;EACA,IAAIoB,OAAO,IAAID,aAAa,KAAK,SAAS,EAAE;IACxC,IAAInB,cAAc,EAAE;MAChB;IAAA;IAEJ,OAAO,IAAI;EACf;;EAEA;EACA;EACA,IAAIQ,GAAG,KAAK,MAAM,EAAE;IAChB,MAAMe,UAAU,GAAGX,OAAO,CAACE,WAAW,CAACU,IAAI,CAACC,CAAC,IACzCA,CAAC,CAAChB,IAAI,KAAKU,aAAa,IACxBM,CAAC,CAACC,IAAI,IACND,CAAC,CAACC,IAAI,CAACJ,QAAQ,CAAC,UAAU,CAC9B,CAAC;IAED,IAAIC,UAAU,EAAE;MACZ,IAAIvB,cAAc,EAAE;QAChB;MAAA;MAEJ,OAAO,IAAI;IACf;EACJ;;EAEA;EACA;EACA;EACA,MAAM2B,gBAAgB,GAAG;IACrB;IACA,cAAc,EAAE,OAAO;IACvB,SAAS,EAAE,OAAO;IAElB;IACA,kBAAkB,EAAE,SAAS;IAC7B,cAAc,EAAE,SAAS;IACzB,mBAAmB,EAAE,SAAS;IAE9B;IACA,gBAAgB,EAAE,SAAS;IAC3B,kBAAkB,EAAE;;IAEpB;EACJ,CAAC;;EAED;EACA,IAAIA,gBAAgB,CAACR,aAAa,CAAC,EAAE;IACjC,MAAMS,UAAU,GAAGD,gBAAgB,CAACR,aAAa,CAAC;;IAElD;IACA,MAAMU,mBAAmB,GAAGjB,OAAO,CAACE,WAAW,CAACU,IAAI,CAACC,CAAC,IAAI;MACtD;MACA,MAAMK,aAAa,GACfL,CAAC,CAAChB,IAAI,KAAKmB,UAAU,IACpBA,UAAU,KAAK,SAAS,IAAIH,CAAC,CAAChB,IAAI,KAAK,UAAW,IAClDmB,UAAU,KAAK,UAAU,IAAIH,CAAC,CAAChB,IAAI,KAAK,SAAU;MAEvD,OAAOqB,aAAa,IAAIL,CAAC,CAACC,IAAI,IAAID,CAAC,CAACC,IAAI,CAACJ,QAAQ,CAACd,GAAG,CAAC;IAC1D,CAAC,CAAC;IAEF,IAAIqB,mBAAmB,EAAE;MACrB,IAAI7B,cAAc,EAAE;QAChB;MAAA;MAEJ,OAAO,IAAI;IACf;;IAEA;IACA,IAAIQ,GAAG,KAAK,MAAM,EAAE;MAChB,MAAMuB,gBAAgB,GAAGnB,OAAO,CAACE,WAAW,CAACU,IAAI,CAACC,CAAC,IAAI;QACnD;QACA,MAAMK,aAAa,GACfL,CAAC,CAAChB,IAAI,KAAKmB,UAAU,IACpBA,UAAU,KAAK,SAAS,IAAIH,CAAC,CAAChB,IAAI,KAAK,UAAW,IAClDmB,UAAU,KAAK,UAAU,IAAIH,CAAC,CAAChB,IAAI,KAAK,SAAU;QAEvD,OAAOqB,aAAa,IAAIL,CAAC,CAACC,IAAI,IAAID,CAAC,CAACC,IAAI,CAACJ,QAAQ,CAAC,UAAU,CAAC;MACjE,CAAC,CAAC;MAEF,IAAIS,gBAAgB,EAAE;QAClB,IAAI/B,cAAc,EAAE;UAChB;QAAA;QAEJ,OAAO,IAAI;MACf;IACJ;EACJ;;EAEA;EACA,MAAMgC,mBAAmB,GAAGpB,OAAO,CAACE,WAAW,CAACU,IAAI,CAACC,CAAC,IAAI;IACtD;IACA,IAAI,CAACA,CAAC,IAAI,CAACA,CAAC,CAAChB,IAAI,IAAI,CAACM,KAAK,CAACC,OAAO,CAACS,CAAC,CAACC,IAAI,CAAC,EAAE;MACzC,OAAO,KAAK;IAChB;;IAEA;IACA,MAAMO,aAAa,GAAGR,CAAC,CAAChB,IAAI,CAACS,WAAW,CAAC,CAAC;IAC1C,MAAMgB,iBAAiB,GAAG5B,eAAe,CAAC2B,aAAa,CAAC,IAAIR,CAAC,CAAChB,IAAI;;IAElE;IACA,MAAM0B,cAAc,GAAGD,iBAAiB,KAAKf,aAAa;;IAE1D;IACA,IAAIgB,cAAc,IAAIV,CAAC,CAACC,IAAI,CAACU,MAAM,KAAK,CAAC,EAAE;MACvC,IAAIpC,cAAc,EAAE;QAChB;MAAA;MAEJ,OAAO,KAAK;IAChB;;IAEA;IACA,IAAImC,cAAc,KAAKV,CAAC,CAACC,IAAI,CAACJ,QAAQ,CAACd,GAAG,CAAC,IAAIiB,CAAC,CAACC,IAAI,CAACJ,QAAQ,CAAC,GAAG,CAAC,CAAC,EAAE;MAClE,OAAO,IAAI;IACf;;IAEA;IACA,IAAId,GAAG,KAAK,MAAM,IAAI2B,cAAc,EAAE;MAClC,OAAOV,CAAC,CAACC,IAAI,CAACJ,QAAQ,CAAC,QAAQ,CAAC,IACzBG,CAAC,CAACC,IAAI,CAACJ,QAAQ,CAAC,QAAQ,CAAC,IACzBG,CAAC,CAACC,IAAI,CAACJ,QAAQ,CAAC,QAAQ,CAAC,IACzBG,CAAC,CAACC,IAAI,CAACJ,QAAQ,CAAC,UAAU,CAAC,IAC3BG,CAAC,CAACC,IAAI,CAACJ,QAAQ,CAAC,WAAW,CAAC,IAC5BG,CAAC,CAACC,IAAI,CAACJ,QAAQ,CAAC,WAAW,CAAC;IACvC;;IAEA;IACA,IAAId,GAAG,KAAK,UAAU,IAAI2B,cAAc,EAAE;MACtC,OAAOV,CAAC,CAACC,IAAI,CAACJ,QAAQ,CAAC,UAAU,CAAC;IACtC;IAEA,OAAO,KAAK;EAChB,CAAC,CAAC;;EAEF;EACA,MAAMe,qBAAqB,GAAGzB,OAAO,CAACE,WAAW,CAACU,IAAI,CAACC,CAAC,IACpDA,CAAC,CAAChB,IAAI,KAAK,GAAG,KAAKgB,CAAC,CAACC,IAAI,CAACJ,QAAQ,CAACd,GAAG,CAAC,IAAIiB,CAAC,CAACC,IAAI,CAACJ,QAAQ,CAAC,GAAG,CAAC,CACnE,CAAC;;EAED;EACA,IAAItB,cAAc,EAAE;IAChB,IAAIgC,mBAAmB,EAAE;MACrB;IAAA,CACH,MAAM,IAAIK,qBAAqB,EAAE;MAC9B;IAAA,CACH,MAAM;MACH;IAAA;EAER;EAEA,OAAOL,mBAAmB,IAAIK,qBAAqB;AACvD;AAACC,EAAA,GA9LuB/B,GAAG;AAAA,IAAA+B,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}