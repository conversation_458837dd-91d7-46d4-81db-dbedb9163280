{"ast": null, "code": "var _jsxFileName = \"E:\\\\Suraj Patil\\\\Organization_Management_System\\\\meraki-frontend-main\\\\src\\\\screens\\\\Designation\\\\Form.js\",\n  _s = $RefreshSig$();\nimport React, { useEffect } from \"react\";\nimport { Box, Button, Card, Grid } from \"@mui/material\";\nimport PageTitle from \"components/PageTitle\";\nimport { useParams } from \"react-router-dom\";\nimport { useDispatch, useSelector } from \"react-redux\";\nimport { toast } from \"react-toastify\";\nimport * as yup from \"yup\";\nimport { useFormik } from \"formik\";\nimport { DesignationSelector, GeneralSelector } from \"selectors\";\nimport { DesignationActions, GeneralActions } from \"slices/actions\";\nimport Input from \"components/Input\";\nimport FormSkeleton from \"../../components/Skeleton/FormSkeleton\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nexport default function FormDesignation() {\n  _s();\n  var _designation$name, _designation$departme, _designation$departme2;\n  const {\n    id\n  } = useParams();\n  const dispatch = useDispatch();\n  const designation = useSelector(DesignationSelector.getDesignationById());\n  const loading = useSelector(GeneralSelector.loader(DesignationActions.getDesignationById.type));\n  const actions = [DesignationActions.createDesignation.type, DesignationActions.updateDesignation.type];\n  const success = useSelector(GeneralSelector.success(actions));\n  useEffect(() => {\n    if (id) {\n      dispatch(DesignationActions.getDesignationById(id));\n    }\n  }, []);\n  useEffect(() => {\n    if (success.length > 0) {\n      var _action$message;\n      const action = success.find(item => actions.includes(item.action));\n      toast.success(`${(_action$message = action === null || action === void 0 ? void 0 : action.message) !== null && _action$message !== void 0 ? _action$message : \"Success\"}`, {\n        position: \"top-right\",\n        autoClose: 3000,\n        closeOnClick: true,\n        pauseOnHover: false,\n        pauseOnFocusLoss: false\n      });\n      dispatch(GeneralActions.removeSuccess(actions));\n    }\n  }, [success]);\n  const validationSchema = yup.object({\n    name: yup.string().required('Name is required'),\n    description: yup.string()\n  });\n  const formik = useFormik({\n    initialValues: {\n      name: (_designation$name = designation === null || designation === void 0 ? void 0 : designation.name) !== null && _designation$name !== void 0 ? _designation$name : \"\",\n      department: (_designation$departme = designation === null || designation === void 0 ? void 0 : (_designation$departme2 = designation.department) === null || _designation$departme2 === void 0 ? void 0 : _designation$departme2._id) !== null && _designation$departme !== void 0 ? _designation$departme : \"\"\n    },\n    enableReinitialize: true,\n    validateOnChange: true,\n    validationSchema: validationSchema,\n    onSubmit: values => {\n      handleSubmit(values);\n    }\n  });\n  const handleSubmit = values => {\n    if (id) {\n      values.id = id;\n      dispatch(DesignationActions.updateDesignation(values));\n    } else {\n      dispatch(DesignationActions.createDesignation(values));\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(Box, {\n    children: [/*#__PURE__*/_jsxDEV(PageTitle, {\n      isBack: true,\n      title: `${id ? 'Update' : 'Create'} Designation`\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 76,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(Card, {\n      children: loading ? /*#__PURE__*/_jsxDEV(FormSkeleton, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 80,\n        columnNumber: 21\n      }, this) : /*#__PURE__*/_jsxDEV(\"form\", {\n        onSubmit: formik.handleSubmit,\n        children: /*#__PURE__*/_jsxDEV(Grid, {\n          container: true,\n          spacing: 3,\n          children: [/*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            lg: 12,\n            xs: 12,\n            children: /*#__PURE__*/_jsxDEV(Input, {\n              label: \"Name\",\n              name: \"name\",\n              value: formik.values.name,\n              onChange: formik.handleChange,\n              error: formik.touched.name && Boolean(formik.errors.name),\n              helpertext: formik.touched.name ? formik.errors.name : \"\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 85,\n              columnNumber: 33\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 84,\n            columnNumber: 29\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            lg: 12,\n            xs: 12,\n            children: /*#__PURE__*/_jsxDEV(Input, {\n              multiline: true,\n              rows: 5,\n              label: \"Description\",\n              name: \"description\",\n              value: formik.values.description,\n              onChange: formik.handleChange\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 94,\n              columnNumber: 33\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 93,\n            columnNumber: 29\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            container: true,\n            justifyContent: \"flex-end\",\n            children: /*#__PURE__*/_jsxDEV(Button, {\n              type: \"submit\",\n              color: \"primary\",\n              variant: \"contained\",\n              children: \"Submit\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 103,\n              columnNumber: 33\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 102,\n            columnNumber: 29\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 83,\n          columnNumber: 25\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 82,\n        columnNumber: 21\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 78,\n      columnNumber: 13\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 75,\n    columnNumber: 9\n  }, this);\n}\n_s(FormDesignation, \"2NFWwWkzGdnAQBz2K8OwSpchkuY=\", false, function () {\n  return [useParams, useDispatch, useSelector, useSelector, useSelector, useFormik];\n});\n_c = FormDesignation;\nvar _c;\n$RefreshReg$(_c, \"FormDesignation\");", "map": {"version": 3, "names": ["React", "useEffect", "Box", "<PERSON><PERSON>", "Card", "Grid", "Page<PERSON><PERSON>le", "useParams", "useDispatch", "useSelector", "toast", "yup", "useFormik", "DesignationSelector", "GeneralSelector", "DesignationActions", "GeneralActions", "Input", "FormSkeleton", "jsxDEV", "_jsxDEV", "FormDesignation", "_s", "_designation$name", "_designation$departme", "_designation$departme2", "id", "dispatch", "designation", "getDesignationById", "loading", "loader", "type", "actions", "createDesignation", "updateDesignation", "success", "length", "_action$message", "action", "find", "item", "includes", "message", "position", "autoClose", "closeOnClick", "pauseOnHover", "pauseOnFocusLoss", "removeSuccess", "validationSchema", "object", "name", "string", "required", "description", "formik", "initialValues", "department", "_id", "enableReinitialize", "validateOnChange", "onSubmit", "values", "handleSubmit", "children", "isBack", "title", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "container", "spacing", "lg", "xs", "label", "value", "onChange", "handleChange", "error", "touched", "Boolean", "errors", "helpertext", "multiline", "rows", "justifyContent", "color", "variant", "_c", "$RefreshReg$"], "sources": ["E:/Suraj Patil/Organization_Management_System/meraki-frontend-main/src/screens/Designation/Form.js"], "sourcesContent": ["import React, {useEffect} from \"react\";\r\nimport {Box, Button, Card, Grid} from \"@mui/material\";\r\nimport PageTitle from \"components/PageTitle\";\r\nimport {useParams} from \"react-router-dom\";\r\nimport {useDispatch, useSelector} from \"react-redux\";\r\nimport {toast} from \"react-toastify\";\r\nimport * as yup from \"yup\";\r\nimport {useFormik} from \"formik\";\r\nimport {DesignationSelector, GeneralSelector} from \"selectors\";\r\nimport {DesignationActions, GeneralActions} from \"slices/actions\";\r\nimport Input from \"components/Input\";\r\nimport FormSkeleton from \"../../components/Skeleton/FormSkeleton\";\r\n\r\nexport default function FormDesignation() {\r\n    const { id } = useParams();\r\n    const dispatch = useDispatch();\r\n    const designation = useSelector(DesignationSelector.getDesignationById());\r\n    const loading = useSelector(GeneralSelector.loader(DesignationActions.getDesignationById.type));\r\n    const actions = [\r\n        DesignationActions.createDesignation.type,\r\n        DesignationActions.updateDesignation.type\r\n    ];\r\n    const success = useSelector(GeneralSelector.success(actions));\r\n\r\n    useEffect(() => {\r\n        if (id) {\r\n            dispatch(DesignationActions.getDesignationById(id));\r\n        }\r\n    }, []);\r\n\r\n    useEffect(() => {\r\n        if (success.length > 0) {\r\n            const action = success.find(item => actions.includes(item.action));\r\n\r\n            toast.success(`${action?.message ?? \"Success\"}`, {\r\n                    position: \"top-right\",\r\n                    autoClose: 3000,\r\n                    closeOnClick: true,\r\n                pauseOnHover: false,\r\n                pauseOnFocusLoss: false\r\n                });\r\n\r\n            dispatch(GeneralActions.removeSuccess(actions));\r\n        }\r\n    }, [success]);\r\n\r\n    const validationSchema = yup.object({\r\n        name: yup.string().required('Name is required'),\r\n        description: yup.string()\r\n    });\r\n\r\n    const formik = useFormik({\r\n        initialValues: {\r\n            name: designation?.name ?? \"\",\r\n            department: designation?.department?._id ?? \"\"\r\n        },\r\n        enableReinitialize: true,\r\n        validateOnChange: true,\r\n        validationSchema: validationSchema,\r\n        onSubmit: (values) => {\r\n            handleSubmit(values);\r\n        }\r\n    });\r\n\r\n    const handleSubmit = (values) => {\r\n        if (id) {\r\n            values.id = id;\r\n            dispatch(DesignationActions.updateDesignation(values));\r\n        } else {\r\n            dispatch(DesignationActions.createDesignation(values));\r\n        }\r\n    }\r\n\r\n    return (\r\n        <Box>\r\n            <PageTitle isBack={true} title={`${id ? 'Update' : 'Create'} Designation`}/>\r\n\r\n            <Card>\r\n                {loading ? (\r\n                    <FormSkeleton/>\r\n                ) : (\r\n                    <form onSubmit={formik.handleSubmit}>\r\n                        <Grid container spacing={3}>\r\n                            <Grid item lg={12} xs={12}>\r\n                                <Input\r\n                                    label=\"Name\"\r\n                                    name=\"name\"\r\n                                    value={formik.values.name}\r\n                                    onChange={formik.handleChange}\r\n                                    error={formik.touched.name && Boolean(formik.errors.name)}\r\n                                    helpertext={formik.touched.name ? formik.errors.name : \"\"}/>\r\n                            </Grid>\r\n                            <Grid item lg={12} xs={12}>\r\n                                <Input\r\n                                    multiline\r\n                                    rows={5}\r\n                                    label=\"Description\"\r\n                                    name=\"description\"\r\n                                    value={formik.values.description}\r\n                                    onChange={formik.handleChange}/>\r\n                            </Grid>\r\n                            <Grid item container justifyContent=\"flex-end\">\r\n                                <Button\r\n                                    type=\"submit\"\r\n                                    color=\"primary\"\r\n                                    variant=\"contained\">\r\n                                    Submit\r\n                                </Button>\r\n                            </Grid>\r\n                        </Grid>\r\n                    </form>\r\n                )}\r\n            </Card>\r\n        </Box>\r\n    )\r\n}"], "mappings": ";;AAAA,OAAOA,KAAK,IAAGC,SAAS,QAAO,OAAO;AACtC,SAAQC,GAAG,EAAEC,MAAM,EAAEC,IAAI,EAAEC,IAAI,QAAO,eAAe;AACrD,OAAOC,SAAS,MAAM,sBAAsB;AAC5C,SAAQC,SAAS,QAAO,kBAAkB;AAC1C,SAAQC,WAAW,EAAEC,WAAW,QAAO,aAAa;AACpD,SAAQC,KAAK,QAAO,gBAAgB;AACpC,OAAO,KAAKC,GAAG,MAAM,KAAK;AAC1B,SAAQC,SAAS,QAAO,QAAQ;AAChC,SAAQC,mBAAmB,EAAEC,eAAe,QAAO,WAAW;AAC9D,SAAQC,kBAAkB,EAAEC,cAAc,QAAO,gBAAgB;AACjE,OAAOC,KAAK,MAAM,kBAAkB;AACpC,OAAOC,YAAY,MAAM,wCAAwC;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAElE,eAAe,SAASC,eAAeA,CAAA,EAAG;EAAAC,EAAA;EAAA,IAAAC,iBAAA,EAAAC,qBAAA,EAAAC,sBAAA;EACtC,MAAM;IAAEC;EAAG,CAAC,GAAGnB,SAAS,CAAC,CAAC;EAC1B,MAAMoB,QAAQ,GAAGnB,WAAW,CAAC,CAAC;EAC9B,MAAMoB,WAAW,GAAGnB,WAAW,CAACI,mBAAmB,CAACgB,kBAAkB,CAAC,CAAC,CAAC;EACzE,MAAMC,OAAO,GAAGrB,WAAW,CAACK,eAAe,CAACiB,MAAM,CAAChB,kBAAkB,CAACc,kBAAkB,CAACG,IAAI,CAAC,CAAC;EAC/F,MAAMC,OAAO,GAAG,CACZlB,kBAAkB,CAACmB,iBAAiB,CAACF,IAAI,EACzCjB,kBAAkB,CAACoB,iBAAiB,CAACH,IAAI,CAC5C;EACD,MAAMI,OAAO,GAAG3B,WAAW,CAACK,eAAe,CAACsB,OAAO,CAACH,OAAO,CAAC,CAAC;EAE7DhC,SAAS,CAAC,MAAM;IACZ,IAAIyB,EAAE,EAAE;MACJC,QAAQ,CAACZ,kBAAkB,CAACc,kBAAkB,CAACH,EAAE,CAAC,CAAC;IACvD;EACJ,CAAC,EAAE,EAAE,CAAC;EAENzB,SAAS,CAAC,MAAM;IACZ,IAAImC,OAAO,CAACC,MAAM,GAAG,CAAC,EAAE;MAAA,IAAAC,eAAA;MACpB,MAAMC,MAAM,GAAGH,OAAO,CAACI,IAAI,CAACC,IAAI,IAAIR,OAAO,CAACS,QAAQ,CAACD,IAAI,CAACF,MAAM,CAAC,CAAC;MAElE7B,KAAK,CAAC0B,OAAO,CAAC,IAAAE,eAAA,GAAGC,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAEI,OAAO,cAAAL,eAAA,cAAAA,eAAA,GAAI,SAAS,EAAE,EAAE;QACzCM,QAAQ,EAAE,WAAW;QACrBC,SAAS,EAAE,IAAI;QACfC,YAAY,EAAE,IAAI;QACtBC,YAAY,EAAE,KAAK;QACnBC,gBAAgB,EAAE;MAClB,CAAC,CAAC;MAENrB,QAAQ,CAACX,cAAc,CAACiC,aAAa,CAAChB,OAAO,CAAC,CAAC;IACnD;EACJ,CAAC,EAAE,CAACG,OAAO,CAAC,CAAC;EAEb,MAAMc,gBAAgB,GAAGvC,GAAG,CAACwC,MAAM,CAAC;IAChCC,IAAI,EAAEzC,GAAG,CAAC0C,MAAM,CAAC,CAAC,CAACC,QAAQ,CAAC,kBAAkB,CAAC;IAC/CC,WAAW,EAAE5C,GAAG,CAAC0C,MAAM,CAAC;EAC5B,CAAC,CAAC;EAEF,MAAMG,MAAM,GAAG5C,SAAS,CAAC;IACrB6C,aAAa,EAAE;MACXL,IAAI,GAAA7B,iBAAA,GAAEK,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAEwB,IAAI,cAAA7B,iBAAA,cAAAA,iBAAA,GAAI,EAAE;MAC7BmC,UAAU,GAAAlC,qBAAA,GAAEI,WAAW,aAAXA,WAAW,wBAAAH,sBAAA,GAAXG,WAAW,CAAE8B,UAAU,cAAAjC,sBAAA,uBAAvBA,sBAAA,CAAyBkC,GAAG,cAAAnC,qBAAA,cAAAA,qBAAA,GAAI;IAChD,CAAC;IACDoC,kBAAkB,EAAE,IAAI;IACxBC,gBAAgB,EAAE,IAAI;IACtBX,gBAAgB,EAAEA,gBAAgB;IAClCY,QAAQ,EAAGC,MAAM,IAAK;MAClBC,YAAY,CAACD,MAAM,CAAC;IACxB;EACJ,CAAC,CAAC;EAEF,MAAMC,YAAY,GAAID,MAAM,IAAK;IAC7B,IAAIrC,EAAE,EAAE;MACJqC,MAAM,CAACrC,EAAE,GAAGA,EAAE;MACdC,QAAQ,CAACZ,kBAAkB,CAACoB,iBAAiB,CAAC4B,MAAM,CAAC,CAAC;IAC1D,CAAC,MAAM;MACHpC,QAAQ,CAACZ,kBAAkB,CAACmB,iBAAiB,CAAC6B,MAAM,CAAC,CAAC;IAC1D;EACJ,CAAC;EAED,oBACI3C,OAAA,CAAClB,GAAG;IAAA+D,QAAA,gBACA7C,OAAA,CAACd,SAAS;MAAC4D,MAAM,EAAE,IAAK;MAACC,KAAK,EAAE,GAAGzC,EAAE,GAAG,QAAQ,GAAG,QAAQ;IAAe;MAAA0C,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAC,CAAC,eAE5EnD,OAAA,CAAChB,IAAI;MAAA6D,QAAA,EACAnC,OAAO,gBACJV,OAAA,CAACF,YAAY;QAAAkD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAC,CAAC,gBAEfnD,OAAA;QAAM0C,QAAQ,EAAEN,MAAM,CAACQ,YAAa;QAAAC,QAAA,eAChC7C,OAAA,CAACf,IAAI;UAACmE,SAAS;UAACC,OAAO,EAAE,CAAE;UAAAR,QAAA,gBACvB7C,OAAA,CAACf,IAAI;YAACoC,IAAI;YAACiC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,EAAG;YAAAV,QAAA,eACtB7C,OAAA,CAACH,KAAK;cACF2D,KAAK,EAAC,MAAM;cACZxB,IAAI,EAAC,MAAM;cACXyB,KAAK,EAAErB,MAAM,CAACO,MAAM,CAACX,IAAK;cAC1B0B,QAAQ,EAAEtB,MAAM,CAACuB,YAAa;cAC9BC,KAAK,EAAExB,MAAM,CAACyB,OAAO,CAAC7B,IAAI,IAAI8B,OAAO,CAAC1B,MAAM,CAAC2B,MAAM,CAAC/B,IAAI,CAAE;cAC1DgC,UAAU,EAAE5B,MAAM,CAACyB,OAAO,CAAC7B,IAAI,GAAGI,MAAM,CAAC2B,MAAM,CAAC/B,IAAI,GAAG;YAAG;cAAAgB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9D,CAAC,eACPnD,OAAA,CAACf,IAAI;YAACoC,IAAI;YAACiC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,EAAG;YAAAV,QAAA,eACtB7C,OAAA,CAACH,KAAK;cACFoE,SAAS;cACTC,IAAI,EAAE,CAAE;cACRV,KAAK,EAAC,aAAa;cACnBxB,IAAI,EAAC,aAAa;cAClByB,KAAK,EAAErB,MAAM,CAACO,MAAM,CAACR,WAAY;cACjCuB,QAAQ,EAAEtB,MAAM,CAACuB;YAAa;cAAAX,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClC,CAAC,eACPnD,OAAA,CAACf,IAAI;YAACoC,IAAI;YAAC+B,SAAS;YAACe,cAAc,EAAC,UAAU;YAAAtB,QAAA,eAC1C7C,OAAA,CAACjB,MAAM;cACH6B,IAAI,EAAC,QAAQ;cACbwD,KAAK,EAAC,SAAS;cACfC,OAAO,EAAC,WAAW;cAAAxB,QAAA,EAAC;YAExB;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACP,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL;IACT;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACN,CAAC;AAEd;AAACjD,EAAA,CAtGuBD,eAAe;EAAA,QACpBd,SAAS,EACPC,WAAW,EACRC,WAAW,EACfA,WAAW,EAKXA,WAAW,EA6BZG,SAAS;AAAA;AAAA8E,EAAA,GAtCJrE,eAAe;AAAA,IAAAqE,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}