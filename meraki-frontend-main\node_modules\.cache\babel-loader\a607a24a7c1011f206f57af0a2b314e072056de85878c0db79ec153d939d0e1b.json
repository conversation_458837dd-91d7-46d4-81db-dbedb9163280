{"ast": null, "code": "var _jsxFileName = \"E:\\\\Suraj Patil\\\\Organization_Management_System\\\\meraki-frontend-main\\\\src\\\\screens\\\\User\\\\Permission.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from \"react\";\nimport PropTypes from 'prop-types';\nimport { Box, Card, Checkbox, FormControlLabel, Typography, Button, Alert, CircularProgress, Chip, Divider } from \"@mui/material\";\nimport { useParams } from \"react-router-dom\";\nimport { features } from \"constants/permission\";\nimport { getApiUrl } from \"utils/apiConfig\";\n\n// Define available actions\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst actionLabels = {\n  read: \"Can View\",\n  read_all: \"Can View All\",\n  read_some: \"Can View Some\",\n  read_self: \"Can View Own\",\n  create: \"Can Create\",\n  update: \"Can Edit\",\n  delete: \"Can Delete\"\n};\n\n// Define feature categories for better organization\nconst featureCategories = {\n  core: {\n    title: \"Core Features\",\n    features: [\"Dashboard\", \"User\", \"Department\", \"Designation\", \"Setting\"]\n  },\n  attendance: {\n    title: \"Attendance & Leave\",\n    features: [\"Attendance\", \"Leave\", \"Calendar\", \"Configuration\", \"Approve\", \"Leave Report\"]\n  },\n  projects: {\n    title: \"Projects & Tasks\",\n    features: [\"Projects\", \"Project List\", \"Project Overview\", \"Project Timesheet\", \"Sprint\", \"Client\", \"Tasks\", \"My Tasks\", \"Task Note\"]\n  },\n  timeline: {\n    title: \"Timeline & Scheduling\",\n    features: [\"Timeline\", \"Overview\", \"Time Request\", \"Task Request\", \"Work Schedule\"]\n  },\n  expenses: {\n    title: \"Finance\",\n    features: [\"Expense\", \"Report\"]\n  }\n};\n\n// Define menu information with routes and access type\nconst menuInfo = {\n  // Core Features\n  Dashboard: {\n    menuName: \"Dashboard\",\n    route: \"/app/dashboard\",\n    access: \"both admin and user\",\n    description: \"View dashboard with activity and statistics\",\n    category: \"core\"\n  },\n  User: {\n    menuName: \"Employee Management\",\n    route: \"/app/user\",\n    access: \"admin only\",\n    description: \"Manage all employees in the system\",\n    category: \"core\"\n  },\n  Department: {\n    menuName: \"Department\",\n    route: \"/app/department\",\n    access: \"admin only\",\n    description: \"Manage company departments\",\n    category: \"core\"\n  },\n  Designation: {\n    menuName: \"Designation\",\n    route: \"/app/designation\",\n    access: \"admin only\",\n    description: \"Manage job designations\",\n    category: \"core\"\n  },\n  Setting: {\n    menuName: \"Setting\",\n    route: \"/app/setting\",\n    access: \"both admin and user\",\n    description: \"Manage system settings - accessible to both admins and users with proper permissions\",\n    category: \"core\"\n  },\n  // Attendance & Leave\n  Attendance: {\n    menuName: \"Attendance\",\n    route: \"/app/attendance\",\n    access: \"admin only\",\n    description: \"View and manage attendance for all employees\",\n    category: \"attendance\"\n  },\n  Leave: {\n    menuName: \"Leave Management\",\n    route: \"/app/leave\",\n    access: \"admin only\",\n    description: \"Manage leave requests for all employees\",\n    category: \"attendance\"\n  },\n  Calendar: {\n    menuName: \"Calendar\",\n    route: \"/app/leave/calendar\",\n    access: \"admin only\",\n    description: \"View leave calendar\",\n    category: \"attendance\",\n    parent: \"Leave\"\n  },\n  Configuration: {\n    menuName: \"Configuration\",\n    route: \"/app/leave/configuration\",\n    access: \"admin only\",\n    description: \"Configure leave settings\",\n    category: \"attendance\",\n    parent: \"Leave\"\n  },\n  Approve: {\n    menuName: \"Approval\",\n    route: \"/app/leave/approval\",\n    access: \"admin only\",\n    description: \"Approve leave requests\",\n    category: \"attendance\",\n    parent: \"Leave\"\n  },\n  \"Leave Report\": {\n    menuName: \"Leave Report\",\n    route: \"/app/leave/report\",\n    access: \"admin only\",\n    description: \"View leave reports\",\n    category: \"attendance\",\n    parent: \"Leave\"\n  },\n  // Projects & Tasks\n  Projects: {\n    menuName: \"Projects\",\n    route: \"/app/project/list\",\n    access: \"admin only\",\n    description: \"Manage all projects\",\n    category: \"projects\"\n  },\n  \"Project List\": {\n    menuName: \"Project List\",\n    route: \"/app/project/list\",\n    access: \"admin only\",\n    description: \"View list of all projects\",\n    category: \"projects\",\n    parent: \"Projects\"\n  },\n  \"Project Overview\": {\n    menuName: \"Project Overview\",\n    route: \"/app/project/overview\",\n    access: \"admin only\",\n    description: \"View project overview\",\n    category: \"projects\",\n    parent: \"Projects\"\n  },\n  \"Project Timesheet\": {\n    menuName: \"Project Timesheet\",\n    route: \"/app/project/timesheet\",\n    access: \"admin only\",\n    description: \"Manage project timesheets\",\n    category: \"projects\",\n    parent: \"Projects\"\n  },\n  Client: {\n    menuName: \"Client\",\n    route: \"/app/client\",\n    access: \"admin only\",\n    description: \"Manage clients\",\n    category: \"projects\"\n  },\n  Tasks: {\n    menuName: \"Tasks\",\n    route: \"/app/tasks\",\n    access: \"both admin and user\",\n    description: \"View and manage tasks\",\n    category: \"projects\"\n  },\n  \"My Tasks\": {\n    menuName: \"My Tasks\",\n    route: \"/app/tasks\",\n    access: \"user only\",\n    description: \"View and manage your tasks\",\n    category: \"projects\"\n  },\n  \"Task Note\": {\n    menuName: \"Task Note\",\n    route: \"/app/user/tasklist/note\",\n    access: \"user only\",\n    description: \"View task notes\",\n    category: \"projects\",\n    parent: \"My Tasks\"\n  },\n  // Add to menuInfo object\n  Sprint: {\n    menuName: \"Sprints\",\n    route: \"/app/sprint\",\n    access: \"both admin and user\",\n    // Changed from \"admin only\"\n    description: \"View all sprints with read_all, or own sprints with read_self\",\n    category: \"projects\"\n  },\n  \"usersprint\": {\n    menuName: \"My Sprints\",\n    route: \"/app/user/sprint\",\n    access: \"user only\",\n    description: \"View and manage your created and assigned sprints\",\n    category: \"projects\",\n    adminEquivalent: \"Sprint\"\n  },\n  // Timeline & Scheduling\n  Timeline: {\n    menuName: \"Timeline\",\n    route: \"/app/timeline\",\n    access: \"admin only\",\n    description: \"View and manage timelines\",\n    category: \"timeline\"\n  },\n  Overview: {\n    menuName: \"Overview\",\n    route: \"/app/timeline/overview\",\n    access: \"admin only\",\n    description: \"View timeline overview\",\n    category: \"timeline\",\n    parent: \"Timeline\"\n  },\n  \"Time Request\": {\n    menuName: \"Time Request\",\n    route: \"/app/timeline/request\",\n    access: \"admin only\",\n    description: \"Manage time requests\",\n    category: \"timeline\",\n    parent: \"Timeline\"\n  },\n  \"Task Request\": {\n    menuName: \"Task Request\",\n    route: \"/app/timeline/taskrequest\",\n    access: \"admin only\",\n    description: \"Manage task requests\",\n    category: \"timeline\",\n    parent: \"Timeline\"\n  },\n  \"Work Schedule\": {\n    menuName: \"Work Schedule\",\n    route: \"/app/timeline/workschedule\",\n    access: \"admin only\",\n    description: \"Manage work schedules\",\n    category: \"timeline\",\n    parent: \"Timeline\"\n  },\n  // Finance\n  Expense: {\n    menuName: \"Expenses\",\n    route: \"/app/expenses\",\n    access: \"admin only\",\n    description: \"Manage all expense reports\",\n    category: \"expenses\"\n  },\n  Report: {\n    menuName: \"Report\",\n    route: \"/app/report\",\n    access: \"admin only\",\n    description: \"View and generate reports\",\n    category: \"expenses\"\n  },\n  // User-specific versions of admin features\n  projectoverview: {\n    menuName: \"Project Overview\",\n    route: \"/app/user/project/overview\",\n    access: \"user only\",\n    description: \"View your project overview\",\n    category: \"projects\",\n    adminEquivalent: \"Project Overview\"\n  },\n  projecttimesheet: {\n    menuName: \"Project Timesheet\",\n    route: \"/app/user/project/timesheet\",\n    access: \"user only\",\n    description: \"Manage your project timesheets\",\n    category: \"projects\",\n    adminEquivalent: \"Project Timesheet\"\n  },\n  projectlist: {\n    menuName: \"Project List\",\n    route: \"/app/user/projects\",\n    access: \"user only\",\n    description: \"View your projects\",\n    category: \"projects\",\n    adminEquivalent: \"Project List\"\n  }\n};\n\n// Helper function to determine which actions to show for each feature\nconst getAvailableActionsForFeature = (featureKey, featureLabel) => {\n  // Default actions for all features\n  const defaultActions = ['read', 'create', 'update', 'delete'];\n\n  // Features that need specialized read permissions\n  const needsReadAll = ['Dashboard', 'Attendance', 'Leave', 'Projects', 'Project List', 'Project Overview', 'Project Timesheet', 'Timeline', 'Overview', 'Time Request', 'Task Request', 'Work Schedule', 'Client', 'Sprint'];\n  const needsReadSome = ['Attendance', 'Leave', 'User'];\n  const needsReadSelf = ['Attendance', 'Leave', 'Tasks', 'My Tasks', 'usersprint', 'Sprint'];\n\n  // Start with default actions\n  let actions = [...defaultActions];\n\n  // Add specialized read permissions if needed\n  if (needsReadAll.includes(featureLabel)) {\n    actions.push('read_all');\n  }\n  if (needsReadSome.includes(featureLabel)) {\n    actions.push('read_some');\n  }\n  if (needsReadSelf.includes(featureLabel)) {\n    actions.push('read_self');\n  }\n  return actions;\n};\nexport default function Permission({\n  user\n}) {\n  _s();\n  // Get userId either from props or from URL params\n  const {\n    userId: urlUserId\n  } = useParams();\n  const userId = (user === null || user === void 0 ? void 0 : user._id) || urlUserId;\n  const [loading, setLoading] = useState(false);\n  const [saving, setSaving] = useState(false);\n  const [error, setError] = useState(null);\n  const [success, setSuccess] = useState(false);\n\n  // Store permissions in format: { feature: [actions] }\n  const [permissions, setPermissions] = useState({});\n\n  // Debug user and userId\n  useEffect(() => {\n    console.log('Permission component - User prop:', user);\n    console.log('Permission component - User ID from prop:', user === null || user === void 0 ? void 0 : user._id);\n    console.log('Permission component - URL userId:', urlUserId);\n    console.log('Permission component - Using userId:', userId);\n\n    // Check token\n    const token = localStorage.getItem('merakihr-token');\n    console.log('Token available:', token ? 'Yes' : 'No');\n    if (!userId) {\n      console.warn('No userId available from either props or URL');\n    }\n  }, [user, urlUserId, userId]);\n\n  // Fetch user permissions when component mounts\n  useEffect(() => {\n    if (userId) {\n      console.log('Fetching permissions for user ID:', userId);\n      fetchUserPermissions();\n    } else {\n      console.warn('No userId provided to Permission component');\n      setError('User ID is missing. Cannot fetch permissions.');\n    }\n  }, [userId]);\n\n  // Fetch user permissions from the backend\n  const fetchUserPermissions = async () => {\n    try {\n      setLoading(true);\n      setError(null);\n\n      // Use fetch instead of axios as per project preference\n      const token = localStorage.getItem('merakihr-token');\n\n      // Debug token\n      console.log('Token for permissions request:', token ? 'Token exists' : 'No token found');\n\n      // Check if we're in admin mode or user mode\n      const isAdminView = window.location.pathname.includes('/app/user/');\n      console.log('Is admin view:', isAdminView);\n\n      // Initialize with empty array as default\n      let permissionsData = [];\n      if (isAdminView) {\n        // Admin view - use permissions API\n        // Use the API config utility to get the correct URL\n        const apiUrl = getApiUrl(`permissions/${userId}`);\n        console.log('Fetching permissions from admin API:', apiUrl);\n        const response = await fetch(apiUrl, {\n          method: 'GET',\n          headers: {\n            'Authorization': `Bearer ${token}`,\n            'Content-Type': 'application/json'\n          }\n        });\n        if (!response.ok) {\n          // If admin API fails (likely due to permissions), try the user API\n          throw new Error('Admin permissions API access denied');\n        }\n        permissionsData = await response.json();\n      } else {\n        // User view - use user profile API which doesn't require admin access\n        const apiUrl = getApiUrl(`user/${userId}`);\n        // console.log('Fetching permissions from user API:', apiUrl);\n\n        const response = await fetch(apiUrl, {\n          method: 'GET',\n          headers: {\n            'Authorization': `Bearer ${token}`,\n            'Content-Type': 'application/json'\n          }\n        });\n        if (!response.ok) {\n          throw new Error('Failed to fetch user data');\n        }\n        const userData = await response.json();\n        permissionsData = userData.permissions || [];\n      }\n\n      // Process the permissions data\n      // console.log('User Permissions (Raw):', permissionsData);\n\n      // Convert backend format to component format\n      const formattedPermissions = {};\n      if (Array.isArray(permissionsData)) {\n        permissionsData.forEach(permission => {\n          // Handle special cases for feature naming consistency\n          let normalizedFeat = permission.feat;\n\n          // Find the feature key in the features object\n          const featureEntry = Object.entries(features).find(([_, val]) => val === permission.feat);\n          if (featureEntry) {\n            // Use the key from features object\n            normalizedFeat = featureEntry[0];\n          } else {\n            // Handle special cases\n            if (permission.feat === 'My Tasks') {\n              normalizedFeat = 'mytasks';\n            } else if (permission.feat === 'Project List') {\n              normalizedFeat = 'projectlist';\n            } else if (permission.feat === 'Project Overview') {\n              normalizedFeat = 'projectoverview';\n            } else if (permission.feat === 'Project Timesheet') {\n              normalizedFeat = 'projecttimesheet';\n            } else {\n              // Convert to lowercase with no spaces for consistency\n              normalizedFeat = permission.feat.toLowerCase().replace(/\\s+/g, '');\n            }\n          }\n          formattedPermissions[normalizedFeat] = permission.acts;\n        });\n      } else {\n        console.warn('Permissions data is not an array:', permissionsData);\n      }\n\n      // Log the formatted permissions\n      console.log('User Permissions (Formatted):', formattedPermissions);\n      setPermissions(formattedPermissions);\n    } catch (err) {\n      setError(err.message || 'Failed to load permissions');\n      console.error('Error fetching permissions:', err);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Handle permission toggle\n  const handleToggle = (feature, action) => {\n    setPermissions(prev => {\n      const current = prev[feature] || [];\n      const updated = current.includes(action) ? current.filter(a => a !== action) : [...current, action];\n\n      // Log the permission change\n      console.log(`Permission toggle: ${feature} - ${action} - ${current.includes(action) ? 'REMOVED' : 'ADDED'}`);\n      console.log(`Feature ${feature} now has permissions: ${updated.join(', ') || 'EMPTY'}`);\n      return {\n        ...prev,\n        [feature]: updated\n      };\n    });\n  };\n\n  // Save permissions to the backend\n  const handleSave = async () => {\n    try {\n      // Validate userId before proceeding\n      if (!userId) {\n        setError('Cannot save permissions: User ID is missing');\n        console.error('Attempted to save permissions without a user ID');\n        return;\n      }\n      setSaving(true);\n      setError(null);\n      setSuccess(false);\n\n      // Convert component format to backend format\n      const formattedPermissions = Object.entries(permissions).map(([feat, acts]) => {\n        // Handle special cases for feature naming consistency\n        let normalizedFeat = feat;\n\n        // Special case for tasks - use \"My Tasks\" in the database\n        if (feat === 'tasks' || feat === 'mytasks') {\n          normalizedFeat = 'My Tasks';\n        }\n\n        // Special case for project features - ensure consistent casing\n        if (feat === 'projectlist' || feat === 'projectList') {\n          normalizedFeat = 'Project List';\n        }\n        if (feat === 'projectoverview' || feat === 'projectOverview') {\n          normalizedFeat = 'Project Overview';\n        }\n        if (feat === 'projecttimesheet' || feat === 'projectTimesheet') {\n          normalizedFeat = 'Project Timesheet';\n        }\n\n        // For features in the features object, use the exact string from there\n        const featureEntry = Object.entries(features).find(([key, _]) => key === feat);\n        if (featureEntry) {\n          normalizedFeat = featureEntry[1];\n        }\n        return {\n          feat: normalizedFeat,\n          acts\n        };\n      });\n\n      // Log the permissions being saved\n      console.log('Saving User Permissions:', {\n        userId,\n        permissions: formattedPermissions\n      });\n\n      // Use fetch instead of axios as per project preference\n      const token = localStorage.getItem('merakihr-token');\n\n      // Debug the request payload\n      console.log('Request payload:', JSON.stringify({\n        permissions: formattedPermissions\n      }));\n\n      // Check if we're in admin mode or user mode\n      const isAdminView = window.location.pathname.includes('/app/user/');\n      console.log('Is admin view for saving:', isAdminView);\n\n      // Initialize with null as default\n      let response = null;\n      if (isAdminView) {\n        // Admin view - use permissions API\n        // Use the API config utility to get the correct URL\n        const apiUrl = getApiUrl(`permissions/${userId}`);\n        console.log('Saving permissions to admin API:', apiUrl);\n        response = await fetch(apiUrl, {\n          method: 'PUT',\n          headers: {\n            'Content-Type': 'application/json',\n            'Authorization': `Bearer ${token}`\n          },\n          body: JSON.stringify({\n            permissions: formattedPermissions\n          })\n        });\n      } else {\n        // User view - use user update API which doesn't require admin access\n        const apiUrl = getApiUrl(`user/${userId}`);\n        console.log('Saving permissions to user API:', apiUrl);\n        response = await fetch(apiUrl, {\n          method: 'PATCH',\n          headers: {\n            'Content-Type': 'application/json',\n            'Authorization': `Bearer ${token}`\n          },\n          body: JSON.stringify({\n            permissions: formattedPermissions\n          })\n        });\n      }\n\n      // Clone the response so we can read it multiple times if needed\n      const clonedResponse = response.clone();\n\n      // Handle response based on status\n      if (!response.ok) {\n        // Try to get error message from response\n        let errorMessage = 'Failed to update permissions';\n        try {\n          const errorData = await clonedResponse.json();\n          errorMessage = errorData.message || errorMessage;\n        } catch (e) {\n          console.log('Error response is not valid JSON:', e);\n          // Only try to get text if the first attempt failed\n          try {\n            const errorText = await response.text();\n            if (errorText) {\n              errorMessage = errorText;\n            }\n          } catch (textError) {\n            console.error('Could not parse error response:', textError);\n          }\n        }\n        throw new Error(errorMessage);\n      }\n\n      // For successful responses, use a default message in case parsing fails\n      let responseData = {\n        message: 'Permissions updated successfully'\n      };\n      try {\n        // Use the original response for successful responses\n        responseData = await response.json();\n        console.log('Success response:', responseData);\n      } catch (e) {\n        console.log('Success response is not valid JSON, using default message');\n        // responseData already initialized with default value\n      }\n\n      // Show success message\n      setSuccess(true);\n      setTimeout(() => setSuccess(false), 3000);\n      return responseData;\n    } catch (err) {\n      setError(err.message || 'Failed to save permissions');\n      console.error('Error saving permissions:', err);\n    } finally {\n      setSaving(false);\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(Card, {\n    sx: {\n      p: 3,\n      mb: 3\n    },\n    children: [/*#__PURE__*/_jsxDEV(Typography, {\n      variant: \"h5\",\n      sx: {\n        mb: 2\n      },\n      children: \"User Permissions\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 659,\n      columnNumber: 7\n    }, this), loading && /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        display: 'flex',\n        justifyContent: 'center',\n        my: 3\n      },\n      children: /*#__PURE__*/_jsxDEV(CircularProgress, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 666,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 665,\n      columnNumber: 9\n    }, this), error && /*#__PURE__*/_jsxDEV(Alert, {\n      severity: \"error\",\n      sx: {\n        mb: 3\n      },\n      children: error\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 672,\n      columnNumber: 9\n    }, this), success && /*#__PURE__*/_jsxDEV(Alert, {\n      severity: \"success\",\n      sx: {\n        mb: 3\n      },\n      children: \"Permissions updated successfully!\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 679,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        display: 'flex',\n        flexDirection: 'column',\n        gap: 2\n      },\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"subtitle1\",\n        sx: {\n          mb: 2,\n          fontWeight: 'bold',\n          color: 'primary.main'\n        },\n        children: \"PERMISSION SYSTEM REFERENCE GUIDE\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 686,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"body2\",\n        sx: {\n          mb: 3\n        },\n        children: \"This guide shows which permissions correspond to which menus and routes in the system. When you grant a permission, the user will be able to access the corresponding menu and route.\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 689,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Divider, {\n        sx: {\n          mb: 3\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 694,\n        columnNumber: 9\n      }, this), Object.entries(featureCategories).map(([categoryKey, category]) => /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          mb: 4\n        },\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h6\",\n          sx: {\n            mb: 2,\n            fontWeight: 'bold',\n            color: 'text.primary'\n          },\n          children: category.title\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 699,\n          columnNumber: 13\n        }, this), category.features.map(featureLabel => {\n          var _Object$entries$find;\n          // Get the feature key from features object\n          const featureKey = ((_Object$entries$find = Object.entries(features).find(([key, val]) => val === featureLabel)) === null || _Object$entries$find === void 0 ? void 0 : _Object$entries$find[0]) || featureLabel.toLowerCase().replace(/\\s+/g, '');\n          const info = menuInfo[featureLabel] || {};\n\n          // Skip child features - they'll be shown with their parents\n          if (info.parent) {\n            return null;\n          }\n          const isAdminOnly = info.access === 'admin only';\n          const isUserOnly = info.access === 'user only';\n          const isBoth = info.access === 'both admin and user';\n\n          // Determine background color and chip color based on access type\n          let bgColor = 'white';\n          let chipColor = 'default';\n          if (isAdminOnly) {\n            bgColor = '#f8f9fa';\n            chipColor = 'error';\n          } else if (isUserOnly) {\n            bgColor = '#f0f4ff';\n            chipColor = 'primary';\n          } else if (isBoth) {\n            bgColor = '#e6f7ff'; // Light blue for both\n            chipColor = 'success';\n          }\n\n          // Find child features\n          const childFeatures = Object.entries(menuInfo).filter(([_, childInfo]) => childInfo.parent === featureLabel).map(([childKey, _]) => childKey);\n          return /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              mb: 2,\n              p: 2,\n              border: '1px solid #eee',\n              borderRadius: 1,\n              backgroundColor: bgColor\n            },\n            children: [/*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                display: 'flex',\n                justifyContent: 'space-between',\n                alignItems: 'center',\n                mb: 1\n              },\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"subtitle1\",\n                sx: {\n                  fontWeight: 'bold'\n                },\n                children: featureLabel\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 747,\n                columnNumber: 21\n              }, this), info.access && /*#__PURE__*/_jsxDEV(Chip, {\n                label: info.access,\n                size: \"small\",\n                color: chipColor,\n                sx: {\n                  fontWeight: 'bold',\n                  textTransform: 'uppercase',\n                  fontSize: '0.7rem'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 752,\n                columnNumber: 23\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 746,\n              columnNumber: 19\n            }, this), info.menuName && /*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                mb: 1\n              },\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                sx: {\n                  fontWeight: 'bold',\n                  display: 'inline'\n                },\n                children: \"Menu:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 767,\n                columnNumber: 23\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                sx: {\n                  display: 'inline'\n                },\n                children: info.menuName\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 770,\n                columnNumber: 23\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 766,\n              columnNumber: 21\n            }, this), info.route && /*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                mb: 1\n              },\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                sx: {\n                  fontWeight: 'bold',\n                  display: 'inline'\n                },\n                children: \"Route:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 778,\n                columnNumber: 23\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                sx: {\n                  display: 'inline',\n                  fontFamily: 'monospace'\n                },\n                children: info.route\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 781,\n                columnNumber: 23\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 777,\n              columnNumber: 21\n            }, this), info.description && /*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                mb: 2\n              },\n              children: /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                color: \"text.secondary\",\n                children: info.description\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 789,\n                columnNumber: 23\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 788,\n              columnNumber: 21\n            }, this), /*#__PURE__*/_jsxDEV(Divider, {\n              sx: {\n                my: 1\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 795,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                display: 'flex',\n                flexWrap: 'wrap',\n                gap: 1\n              },\n              children: Object.entries(actionLabels).filter(([action]) => getAvailableActionsForFeature(featureKey, featureLabel).includes(action)).map(([action, label]) => {\n                var _permissions$featureK;\n                return /*#__PURE__*/_jsxDEV(FormControlLabel, {\n                  control: /*#__PURE__*/_jsxDEV(Checkbox, {\n                    checked: ((_permissions$featureK = permissions[featureKey]) === null || _permissions$featureK === void 0 ? void 0 : _permissions$featureK.includes(action)) || false,\n                    onChange: () => handleToggle(featureKey, action),\n                    disabled: saving\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 804,\n                    columnNumber: 29\n                  }, this),\n                  label: label\n                }, action, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 801,\n                  columnNumber: 25\n                }, this);\n              })\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 797,\n              columnNumber: 19\n            }, this), childFeatures.length > 0 && /*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                mt: 2,\n                ml: 3,\n                borderLeft: '2px solid #eee',\n                pl: 2\n              },\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"subtitle2\",\n                sx: {\n                  mb: 1,\n                  fontWeight: 'bold'\n                },\n                children: \"Child Features:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 818,\n                columnNumber: 23\n              }, this), childFeatures.map(childFeatureLabel => {\n                var _Object$entries$find2;\n                const childFeatureKey = ((_Object$entries$find2 = Object.entries(features).find(([key, val]) => val === childFeatureLabel)) === null || _Object$entries$find2 === void 0 ? void 0 : _Object$entries$find2[0]) || childFeatureLabel.toLowerCase().replace(/\\s+/g, '');\n                const childInfo = menuInfo[childFeatureLabel] || {};\n                return /*#__PURE__*/_jsxDEV(Box, {\n                  sx: {\n                    mb: 2,\n                    p: 2,\n                    border: '1px solid #eee',\n                    borderRadius: 1,\n                    backgroundColor: 'rgba(0,0,0,0.02)'\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(Box, {\n                    sx: {\n                      display: 'flex',\n                      justifyContent: 'space-between',\n                      alignItems: 'center',\n                      mb: 1\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"body1\",\n                      sx: {\n                        fontWeight: 'bold'\n                      },\n                      children: childFeatureLabel\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 835,\n                      columnNumber: 31\n                    }, this), childInfo.access && /*#__PURE__*/_jsxDEV(Chip, {\n                      label: childInfo.access,\n                      size: \"small\",\n                      color: chipColor,\n                      sx: {\n                        fontWeight: 'bold',\n                        textTransform: 'uppercase',\n                        fontSize: '0.7rem'\n                      }\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 840,\n                      columnNumber: 33\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 834,\n                    columnNumber: 29\n                  }, this), childInfo.route && /*#__PURE__*/_jsxDEV(Box, {\n                    sx: {\n                      mb: 1\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"body2\",\n                      sx: {\n                        fontWeight: 'bold',\n                        display: 'inline'\n                      },\n                      children: \"Route:\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 855,\n                      columnNumber: 33\n                    }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"body2\",\n                      sx: {\n                        display: 'inline',\n                        fontFamily: 'monospace'\n                      },\n                      children: childInfo.route\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 858,\n                      columnNumber: 33\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 854,\n                    columnNumber: 31\n                  }, this), /*#__PURE__*/_jsxDEV(Box, {\n                    sx: {\n                      display: 'flex',\n                      flexWrap: 'wrap',\n                      gap: 1,\n                      mt: 1\n                    },\n                    children: Object.entries(actionLabels).filter(([action]) => getAvailableActionsForFeature(childFeatureKey, childFeatureLabel).includes(action)).map(([action, label]) => {\n                      var _permissions$childFea;\n                      return /*#__PURE__*/_jsxDEV(FormControlLabel, {\n                        control: /*#__PURE__*/_jsxDEV(Checkbox, {\n                          checked: ((_permissions$childFea = permissions[childFeatureKey]) === null || _permissions$childFea === void 0 ? void 0 : _permissions$childFea.includes(action)) || false,\n                          onChange: () => handleToggle(childFeatureKey, action),\n                          disabled: saving,\n                          size: \"small\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 871,\n                          columnNumber: 39\n                        }, this),\n                        label: label\n                      }, action, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 868,\n                        columnNumber: 35\n                      }, this);\n                    })\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 864,\n                    columnNumber: 29\n                  }, this)]\n                }, childFeatureKey, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 827,\n                  columnNumber: 27\n                }, this);\n              })]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 817,\n              columnNumber: 21\n            }, this)]\n          }, featureKey, true, {\n            fileName: _jsxFileName,\n            lineNumber: 739,\n            columnNumber: 17\n          }, this);\n        })]\n      }, categoryKey, true, {\n        fileName: _jsxFileName,\n        lineNumber: 698,\n        columnNumber: 11\n      }, this))]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 685,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        mt: 3,\n        display: 'flex',\n        justifyContent: 'flex-end'\n      },\n      children: /*#__PURE__*/_jsxDEV(Button, {\n        variant: \"contained\",\n        color: \"primary\",\n        onClick: handleSave,\n        disabled: saving || loading,\n        children: saving ? 'Saving...' : 'Save Permissions'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 896,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 895,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 658,\n    columnNumber: 5\n  }, this);\n}\n\n// PropTypes validation\n_s(Permission, \"EU9sVy8e+xM4Y05Te0nMuyrP4CE=\", false, function () {\n  return [useParams];\n});\n_c = Permission;\nPermission.propTypes = {\n  user: PropTypes.shape({\n    _id: PropTypes.string,\n    name: PropTypes.string,\n    email: PropTypes.string,\n    permissions: PropTypes.array\n  }),\n  form: PropTypes.object,\n  setForm: PropTypes.func\n};\n\n// Default props\nPermission.defaultProps = {\n  user: null\n};\nvar _c;\n$RefreshReg$(_c, \"Permission\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "PropTypes", "Box", "Card", "Checkbox", "FormControlLabel", "Typography", "<PERSON><PERSON>", "<PERSON><PERSON>", "CircularProgress", "Chip", "Divider", "useParams", "features", "getApiUrl", "jsxDEV", "_jsxDEV", "actionLabels", "read", "read_all", "read_some", "read_self", "create", "update", "delete", "featureCategories", "core", "title", "attendance", "projects", "timeline", "expenses", "menuInfo", "Dashboard", "menuName", "route", "access", "description", "category", "User", "Department", "Designation", "Setting", "Attendance", "Leave", "Calendar", "parent", "Configuration", "Approve", "Projects", "Client", "Tasks", "Sprint", "adminEquivalent", "Timeline", "Overview", "Expense", "Report", "projectoverview", "projecttimesheet", "projectlist", "getAvailableActionsForFeature", "<PERSON><PERSON><PERSON>", "featureLabel", "defaultActions", "needsReadAll", "needsReadSome", "needsReadSelf", "actions", "includes", "push", "Permission", "user", "_s", "userId", "urlUserId", "_id", "loading", "setLoading", "saving", "setSaving", "error", "setError", "success", "setSuccess", "permissions", "setPermissions", "console", "log", "token", "localStorage", "getItem", "warn", "fetchUserPermissions", "isAdminView", "window", "location", "pathname", "permissionsData", "apiUrl", "response", "fetch", "method", "headers", "ok", "Error", "json", "userData", "formattedPermissions", "Array", "isArray", "for<PERSON>ach", "permission", "normalizedFeat", "feat", "featureEntry", "Object", "entries", "find", "_", "val", "toLowerCase", "replace", "acts", "err", "message", "handleToggle", "feature", "action", "prev", "current", "updated", "filter", "a", "join", "handleSave", "map", "key", "JSON", "stringify", "body", "clonedResponse", "clone", "errorMessage", "errorData", "e", "errorText", "text", "textError", "responseData", "setTimeout", "sx", "p", "mb", "children", "variant", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "display", "justifyContent", "my", "severity", "flexDirection", "gap", "fontWeight", "color", "categoryKey", "_Object$entries$find", "info", "isAdminOnly", "isUserOnly", "isBoth", "bgColor", "chipColor", "childFeatures", "childInfo", "<PERSON><PERSON><PERSON>", "border", "borderRadius", "backgroundColor", "alignItems", "label", "size", "textTransform", "fontSize", "fontFamily", "flexWrap", "_permissions$featureK", "control", "checked", "onChange", "disabled", "length", "mt", "ml", "borderLeft", "pl", "child<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "_Object$entries$find2", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "_permissions$childFea", "onClick", "_c", "propTypes", "shape", "string", "name", "email", "array", "form", "object", "setForm", "func", "defaultProps", "$RefreshReg$"], "sources": ["E:/Suraj Patil/Organization_Management_System/meraki-frontend-main/src/screens/User/Permission.jsx"], "sourcesContent": ["import React, { useState, useEffect } from \"react\";\r\nimport PropTypes from 'prop-types';\r\nimport {\r\n  Box,\r\n  Card,\r\n  Checkbox,\r\n  FormControlLabel,\r\n  Typography,\r\n  Button,\r\n  Alert,\r\n  CircularProgress,\r\n  Chip,\r\n  Divider\r\n} from \"@mui/material\";\r\nimport { useParams } from \"react-router-dom\";\r\nimport { features } from \"constants/permission\";\r\nimport { getApiUrl } from \"utils/apiConfig\";\r\n\r\n\r\n// Define available actions\r\nconst actionLabels = {\r\n  read: \"Can View\",\r\n  read_all: \"Can View All\",\r\n  read_some: \"Can View Some\",\r\n  read_self: \"Can View Own\",\r\n  create: \"Can Create\",\r\n  update: \"Can Edit\",\r\n  delete: \"Can Delete\",\r\n};\r\n\r\n// Define feature categories for better organization\r\nconst featureCategories = {\r\n  core: {\r\n    title: \"Core Features\",\r\n    features: [\"Dashboard\", \"User\", \"Department\", \"Designation\", \"Setting\"]\r\n  },\r\n  attendance: {\r\n    title: \"Attendance & Leave\",\r\n    features: [\"Attendance\", \"Leave\", \"Calendar\", \"Configuration\", \"Approve\", \"Leave Report\"]\r\n  },\r\n  projects: {\r\n    title: \"Projects & Tasks\",\r\n    features: [\"Projects\", \"Project List\", \"Project Overview\", \"Project Timesheet\",\"Sprint\", \"Client\", \"Tasks\", \"My Tasks\", \"Task Note\"]\r\n  },\r\n  timeline: {\r\n    title: \"Timeline & Scheduling\",\r\n    features: [\"Timeline\", \"Overview\", \"Time Request\", \"Task Request\", \"Work Schedule\"]\r\n  },\r\n  expenses: {\r\n    title: \"Finance\",\r\n    features: [\"Expense\", \"Report\"]\r\n  }\r\n};\r\n\r\n// Define menu information with routes and access type\r\nconst menuInfo = {\r\n  // Core Features\r\n  Dashboard: {\r\n    menuName: \"Dashboard\",\r\n    route: \"/app/dashboard\",\r\n    access: \"both admin and user\",\r\n    description: \"View dashboard with activity and statistics\",\r\n    category: \"core\"\r\n  },\r\n  User: {\r\n    menuName: \"Employee Management\",\r\n    route: \"/app/user\",\r\n    access: \"admin only\",\r\n    description: \"Manage all employees in the system\",\r\n    category: \"core\"\r\n  },\r\n  Department: {\r\n    menuName: \"Department\",\r\n    route: \"/app/department\",\r\n    access: \"admin only\",\r\n    description: \"Manage company departments\",\r\n    category: \"core\"\r\n  },\r\n  Designation: {\r\n    menuName: \"Designation\",\r\n    route: \"/app/designation\",\r\n    access: \"admin only\",\r\n    description: \"Manage job designations\",\r\n    category: \"core\"\r\n  },\r\n  Setting: {\r\n    menuName: \"Setting\",\r\n    route: \"/app/setting\",\r\n    access: \"both admin and user\",\r\n    description: \"Manage system settings - accessible to both admins and users with proper permissions\",\r\n    category: \"core\"\r\n  },\r\n\r\n  // Attendance & Leave\r\n  Attendance: {\r\n    menuName: \"Attendance\",\r\n    route: \"/app/attendance\",\r\n    access: \"admin only\",\r\n    description: \"View and manage attendance for all employees\",\r\n    category: \"attendance\"\r\n  },\r\n  Leave: {\r\n    menuName: \"Leave Management\",\r\n    route: \"/app/leave\",\r\n    access: \"admin only\",\r\n    description: \"Manage leave requests for all employees\",\r\n    category: \"attendance\"\r\n  },\r\n  Calendar: {\r\n    menuName: \"Calendar\",\r\n    route: \"/app/leave/calendar\",\r\n    access: \"admin only\",\r\n    description: \"View leave calendar\",\r\n    category: \"attendance\",\r\n    parent: \"Leave\"\r\n  },\r\n  Configuration: {\r\n    menuName: \"Configuration\",\r\n    route: \"/app/leave/configuration\",\r\n    access: \"admin only\",\r\n    description: \"Configure leave settings\",\r\n    category: \"attendance\",\r\n    parent: \"Leave\"\r\n  },\r\n  Approve: {\r\n    menuName: \"Approval\",\r\n    route: \"/app/leave/approval\",\r\n    access: \"admin only\",\r\n    description: \"Approve leave requests\",\r\n    category: \"attendance\",\r\n    parent: \"Leave\"\r\n  },\r\n  \"Leave Report\": {\r\n    menuName: \"Leave Report\",\r\n    route: \"/app/leave/report\",\r\n    access: \"admin only\",\r\n    description: \"View leave reports\",\r\n    category: \"attendance\",\r\n    parent: \"Leave\"\r\n  },\r\n\r\n  // Projects & Tasks\r\n  Projects: {\r\n    menuName: \"Projects\",\r\n    route: \"/app/project/list\",\r\n    access: \"admin only\",\r\n    description: \"Manage all projects\",\r\n    category: \"projects\"\r\n  },\r\n  \"Project List\": {\r\n    menuName: \"Project List\",\r\n    route: \"/app/project/list\",\r\n    access: \"admin only\",\r\n    description: \"View list of all projects\",\r\n    category: \"projects\",\r\n    parent: \"Projects\"\r\n  },\r\n  \"Project Overview\": {\r\n    menuName: \"Project Overview\",\r\n    route: \"/app/project/overview\",\r\n    access: \"admin only\",\r\n    description: \"View project overview\",\r\n    category: \"projects\",\r\n    parent: \"Projects\"\r\n  },\r\n  \"Project Timesheet\": {\r\n    menuName: \"Project Timesheet\",\r\n    route: \"/app/project/timesheet\",\r\n    access: \"admin only\",\r\n    description: \"Manage project timesheets\",\r\n    category: \"projects\",\r\n    parent: \"Projects\"\r\n  },\r\n  Client: {\r\n    menuName: \"Client\",\r\n    route: \"/app/client\",\r\n    access: \"admin only\",\r\n    description: \"Manage clients\",\r\n    category: \"projects\"\r\n  },\r\n  Tasks: {\r\n    menuName: \"Tasks\",\r\n    route: \"/app/tasks\",\r\n    access: \"both admin and user\",\r\n    description: \"View and manage tasks\",\r\n    category: \"projects\"\r\n  },\r\n  \"My Tasks\": {\r\n    menuName: \"My Tasks\",\r\n    route: \"/app/tasks\",\r\n    access: \"user only\",\r\n    description: \"View and manage your tasks\",\r\n    category: \"projects\"\r\n  },\r\n  \"Task Note\": {\r\n    menuName: \"Task Note\",\r\n    route: \"/app/user/tasklist/note\",\r\n    access: \"user only\",\r\n    description: \"View task notes\",\r\n    category: \"projects\",\r\n    parent: \"My Tasks\"\r\n  },\r\n  // Add to menuInfo object\r\nSprint: {\r\n  menuName: \"Sprints\",\r\n  route: \"/app/sprint\",\r\n  access: \"both admin and user\",  // Changed from \"admin only\"\r\n  description: \"View all sprints with read_all, or own sprints with read_self\",\r\n  category: \"projects\"\r\n},\r\n\r\n\"usersprint\": {\r\n  menuName: \"My Sprints\",\r\n  route: \"/app/user/sprint\",\r\n  access: \"user only\",\r\n  description: \"View and manage your created and assigned sprints\",\r\n  category: \"projects\",\r\n  adminEquivalent: \"Sprint\"\r\n},\r\n\r\n\r\n\r\n  // Timeline & Scheduling\r\n  Timeline: {\r\n    menuName: \"Timeline\",\r\n    route: \"/app/timeline\",\r\n    access: \"admin only\",\r\n    description: \"View and manage timelines\",\r\n    category: \"timeline\"\r\n  },\r\n  Overview: {\r\n    menuName: \"Overview\",\r\n    route: \"/app/timeline/overview\",\r\n    access: \"admin only\",\r\n    description: \"View timeline overview\",\r\n    category: \"timeline\",\r\n    parent: \"Timeline\"\r\n  },\r\n  \"Time Request\": {\r\n    menuName: \"Time Request\",\r\n    route: \"/app/timeline/request\",\r\n    access: \"admin only\",\r\n    description: \"Manage time requests\",\r\n    category: \"timeline\",\r\n    parent: \"Timeline\"\r\n  },\r\n  \"Task Request\": {\r\n    menuName: \"Task Request\",\r\n    route: \"/app/timeline/taskrequest\",\r\n    access: \"admin only\",\r\n    description: \"Manage task requests\",\r\n    category: \"timeline\",\r\n    parent: \"Timeline\"\r\n  },\r\n  \"Work Schedule\": {\r\n    menuName: \"Work Schedule\",\r\n    route: \"/app/timeline/workschedule\",\r\n    access: \"admin only\",\r\n    description: \"Manage work schedules\",\r\n    category: \"timeline\",\r\n    parent: \"Timeline\"\r\n  },\r\n\r\n  // Finance\r\n  Expense: {\r\n    menuName: \"Expenses\",\r\n    route: \"/app/expenses\",\r\n    access: \"admin only\",\r\n    description: \"Manage all expense reports\",\r\n    category: \"expenses\"\r\n  },\r\n  Report: {\r\n    menuName: \"Report\",\r\n    route: \"/app/report\",\r\n    access: \"admin only\",\r\n    description: \"View and generate reports\",\r\n    category: \"expenses\"\r\n  },\r\n\r\n  // User-specific versions of admin features\r\n  projectoverview: {\r\n    menuName: \"Project Overview\",\r\n    route: \"/app/user/project/overview\",\r\n    access: \"user only\",\r\n    description: \"View your project overview\",\r\n    category: \"projects\",\r\n    adminEquivalent: \"Project Overview\"\r\n  },\r\n  projecttimesheet: {\r\n    menuName: \"Project Timesheet\",\r\n    route: \"/app/user/project/timesheet\",\r\n    access: \"user only\",\r\n    description: \"Manage your project timesheets\",\r\n    category: \"projects\",\r\n    adminEquivalent: \"Project Timesheet\"\r\n  },\r\n  projectlist: {\r\n    menuName: \"Project List\",\r\n    route: \"/app/user/projects\",\r\n    access: \"user only\",\r\n    description: \"View your projects\",\r\n    category: \"projects\",\r\n    adminEquivalent: \"Project List\"\r\n  }\r\n};\r\n\r\n// Helper function to determine which actions to show for each feature\r\nconst getAvailableActionsForFeature = (featureKey, featureLabel) => {\r\n  // Default actions for all features\r\n  const defaultActions = ['read', 'create', 'update', 'delete'];\r\n\r\n  // Features that need specialized read permissions\r\nconst needsReadAll = [\r\n  'Dashboard', 'Attendance', 'Leave', 'Projects', 'Project List',\r\n  'Project Overview', 'Project Timesheet', 'Timeline', 'Overview',\r\n  'Time Request', 'Task Request', 'Work Schedule', 'Client', 'Sprint'\r\n];\r\n\r\n\r\n  const needsReadSome = [\r\n    'Attendance', 'Leave', 'User'\r\n  ];\r\n\r\n const needsReadSelf = [\r\n  'Attendance', 'Leave', 'Tasks', 'My Tasks', 'usersprint', 'Sprint'\r\n];\r\n\r\n\r\n  // Start with default actions\r\n  let actions = [...defaultActions];\r\n\r\n  // Add specialized read permissions if needed\r\n  if (needsReadAll.includes(featureLabel)) {\r\n    actions.push('read_all');\r\n  }\r\n\r\n  if (needsReadSome.includes(featureLabel)) {\r\n    actions.push('read_some');\r\n  }\r\n\r\n  if (needsReadSelf.includes(featureLabel)) {\r\n    actions.push('read_self');\r\n  }\r\n\r\n  return actions;\r\n};\r\n\r\nexport default function Permission({ user }) {\r\n  // Get userId either from props or from URL params\r\n  const { userId: urlUserId } = useParams();\r\n  const userId = user?._id || urlUserId;\r\n\r\n  const [loading, setLoading] = useState(false);\r\n  const [saving, setSaving] = useState(false);\r\n  const [error, setError] = useState(null);\r\n  const [success, setSuccess] = useState(false);\r\n\r\n  // Store permissions in format: { feature: [actions] }\r\n  const [permissions, setPermissions] = useState({});\r\n\r\n  // Debug user and userId\r\n  useEffect(() => {\r\n    console.log('Permission component - User prop:', user);\r\n    console.log('Permission component - User ID from prop:', user?._id);\r\n    console.log('Permission component - URL userId:', urlUserId);\r\n    console.log('Permission component - Using userId:', userId);\r\n\r\n    // Check token\r\n    const token = localStorage.getItem('merakihr-token');\r\n    console.log('Token available:', token ? 'Yes' : 'No');\r\n\r\n    if (!userId) {\r\n      console.warn('No userId available from either props or URL');\r\n    }\r\n  }, [user, urlUserId, userId]);\r\n\r\n  // Fetch user permissions when component mounts\r\n  useEffect(() => {\r\n    if (userId) {\r\n      console.log('Fetching permissions for user ID:', userId);\r\n      fetchUserPermissions();\r\n    } else {\r\n      console.warn('No userId provided to Permission component');\r\n      setError('User ID is missing. Cannot fetch permissions.');\r\n    }\r\n  }, [userId]);\r\n\r\n  // Fetch user permissions from the backend\r\n  const fetchUserPermissions = async () => {\r\n    try {\r\n      setLoading(true);\r\n      setError(null);\r\n\r\n      // Use fetch instead of axios as per project preference\r\n      const token = localStorage.getItem('merakihr-token');\r\n\r\n      // Debug token\r\n      console.log('Token for permissions request:', token ? 'Token exists' : 'No token found');\r\n\r\n      // Check if we're in admin mode or user mode\r\n      const isAdminView = window.location.pathname.includes('/app/user/');\r\n      console.log('Is admin view:', isAdminView);\r\n\r\n      // Initialize with empty array as default\r\n      let permissionsData = [];\r\n\r\n      if (isAdminView) {\r\n        // Admin view - use permissions API\r\n        // Use the API config utility to get the correct URL\r\n        const apiUrl = getApiUrl(`permissions/${userId}`);\r\n        console.log('Fetching permissions from admin API:', apiUrl);\r\n\r\n        const response = await fetch(apiUrl, {\r\n          method: 'GET',\r\n          headers: {\r\n            'Authorization': `Bearer ${token}`,\r\n            'Content-Type': 'application/json'\r\n          }\r\n        });\r\n\r\n        if (!response.ok) {\r\n          // If admin API fails (likely due to permissions), try the user API\r\n          throw new Error('Admin permissions API access denied');\r\n        }\r\n\r\n        permissionsData = await response.json();\r\n      } else {\r\n        // User view - use user profile API which doesn't require admin access\r\n        const apiUrl = getApiUrl(`user/${userId}`);\r\n        // console.log('Fetching permissions from user API:', apiUrl);\r\n\r\n        const response = await fetch(apiUrl, {\r\n          method: 'GET',\r\n          headers: {\r\n            'Authorization': `Bearer ${token}`,\r\n            'Content-Type': 'application/json'\r\n          }\r\n        });\r\n\r\n        if (!response.ok) {\r\n          throw new Error('Failed to fetch user data');\r\n        }\r\n\r\n        const userData = await response.json();\r\n        permissionsData = userData.permissions || [];\r\n      }\r\n\r\n      // Process the permissions data\r\n      // console.log('User Permissions (Raw):', permissionsData);\r\n\r\n      // Convert backend format to component format\r\n      const formattedPermissions = {};\r\n      if (Array.isArray(permissionsData)) {\r\n        permissionsData.forEach(permission => {\r\n          // Handle special cases for feature naming consistency\r\n          let normalizedFeat = permission.feat;\r\n\r\n          // Find the feature key in the features object\r\n          const featureEntry = Object.entries(features).find(([_, val]) => val === permission.feat);\r\n          if (featureEntry) {\r\n            // Use the key from features object\r\n            normalizedFeat = featureEntry[0];\r\n          } else {\r\n            // Handle special cases\r\n            if (permission.feat === 'My Tasks') {\r\n              normalizedFeat = 'mytasks';\r\n            } else if (permission.feat === 'Project List') {\r\n              normalizedFeat = 'projectlist';\r\n            } else if (permission.feat === 'Project Overview') {\r\n              normalizedFeat = 'projectoverview';\r\n            } else if (permission.feat === 'Project Timesheet') {\r\n              normalizedFeat = 'projecttimesheet';\r\n            } else {\r\n              // Convert to lowercase with no spaces for consistency\r\n              normalizedFeat = permission.feat.toLowerCase().replace(/\\s+/g, '');\r\n            }\r\n          }\r\n\r\n          formattedPermissions[normalizedFeat] = permission.acts;\r\n        });\r\n      } else {\r\n        console.warn('Permissions data is not an array:', permissionsData);\r\n      }\r\n\r\n      // Log the formatted permissions\r\n      console.log('User Permissions (Formatted):', formattedPermissions);\r\n\r\n      setPermissions(formattedPermissions);\r\n    } catch (err) {\r\n      setError(err.message || 'Failed to load permissions');\r\n      console.error('Error fetching permissions:', err);\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  };\r\n\r\n  // Handle permission toggle\r\n  const handleToggle = (feature, action) => {\r\n    setPermissions((prev) => {\r\n      const current = prev[feature] || [];\r\n      const updated = current.includes(action) ? current.filter((a) => a !== action) : [...current, action];\r\n\r\n      // Log the permission change\r\n      console.log(`Permission toggle: ${feature} - ${action} - ${current.includes(action) ? 'REMOVED' : 'ADDED'}`);\r\n      console.log(`Feature ${feature} now has permissions: ${updated.join(', ') || 'EMPTY'}`);\r\n\r\n      return { ...prev, [feature]: updated };\r\n    });\r\n  };\r\n\r\n  // Save permissions to the backend\r\n  const handleSave = async () => {\r\n    try {\r\n      // Validate userId before proceeding\r\n      if (!userId) {\r\n        setError('Cannot save permissions: User ID is missing');\r\n        console.error('Attempted to save permissions without a user ID');\r\n        return;\r\n      }\r\n\r\n      setSaving(true);\r\n      setError(null);\r\n      setSuccess(false);\r\n\r\n      // Convert component format to backend format\r\n      const formattedPermissions = Object.entries(permissions).map(([feat, acts]) => {\r\n        // Handle special cases for feature naming consistency\r\n        let normalizedFeat = feat;\r\n\r\n        // Special case for tasks - use \"My Tasks\" in the database\r\n        if (feat === 'tasks' || feat === 'mytasks') {\r\n          normalizedFeat = 'My Tasks';\r\n        }\r\n\r\n        // Special case for project features - ensure consistent casing\r\n        if (feat === 'projectlist' || feat === 'projectList') {\r\n          normalizedFeat = 'Project List';\r\n        }\r\n\r\n        if (feat === 'projectoverview' || feat === 'projectOverview') {\r\n          normalizedFeat = 'Project Overview';\r\n        }\r\n\r\n        if (feat === 'projecttimesheet' || feat === 'projectTimesheet') {\r\n          normalizedFeat = 'Project Timesheet';\r\n        }\r\n\r\n        // For features in the features object, use the exact string from there\r\n        const featureEntry = Object.entries(features).find(([key, _]) => key === feat);\r\n        if (featureEntry) {\r\n          normalizedFeat = featureEntry[1];\r\n        }\r\n\r\n        return {\r\n          feat: normalizedFeat,\r\n          acts\r\n        };\r\n      });\r\n\r\n      // Log the permissions being saved\r\n      console.log('Saving User Permissions:', {\r\n        userId,\r\n        permissions: formattedPermissions\r\n      });\r\n\r\n      // Use fetch instead of axios as per project preference\r\n      const token = localStorage.getItem('merakihr-token');\r\n\r\n      // Debug the request payload\r\n      console.log('Request payload:', JSON.stringify({ permissions: formattedPermissions }));\r\n\r\n      // Check if we're in admin mode or user mode\r\n      const isAdminView = window.location.pathname.includes('/app/user/');\r\n      console.log('Is admin view for saving:', isAdminView);\r\n\r\n      // Initialize with null as default\r\n      let response = null;\r\n\r\n      if (isAdminView) {\r\n        // Admin view - use permissions API\r\n        // Use the API config utility to get the correct URL\r\n        const apiUrl = getApiUrl(`permissions/${userId}`);\r\n        console.log('Saving permissions to admin API:', apiUrl);\r\n\r\n        response = await fetch(apiUrl, {\r\n          method: 'PUT',\r\n          headers: {\r\n            'Content-Type': 'application/json',\r\n            'Authorization': `Bearer ${token}`\r\n          },\r\n          body: JSON.stringify({ permissions: formattedPermissions })\r\n        });\r\n      } else {\r\n        // User view - use user update API which doesn't require admin access\r\n        const apiUrl = getApiUrl(`user/${userId}`);\r\n        console.log('Saving permissions to user API:', apiUrl);\r\n\r\n        response = await fetch(apiUrl, {\r\n          method: 'PATCH',\r\n          headers: {\r\n            'Content-Type': 'application/json',\r\n            'Authorization': `Bearer ${token}`\r\n          },\r\n          body: JSON.stringify({ permissions: formattedPermissions })\r\n        });\r\n      }\r\n\r\n      // Clone the response so we can read it multiple times if needed\r\n      const clonedResponse = response.clone();\r\n\r\n      // Handle response based on status\r\n      if (!response.ok) {\r\n        // Try to get error message from response\r\n        let errorMessage = 'Failed to update permissions';\r\n        try {\r\n          const errorData = await clonedResponse.json();\r\n          errorMessage = errorData.message || errorMessage;\r\n        } catch (e) {\r\n          console.log('Error response is not valid JSON:', e);\r\n          // Only try to get text if the first attempt failed\r\n          try {\r\n            const errorText = await response.text();\r\n            if (errorText) {\r\n              errorMessage = errorText;\r\n            }\r\n          } catch (textError) {\r\n            console.error('Could not parse error response:', textError);\r\n          }\r\n        }\r\n        throw new Error(errorMessage);\r\n      }\r\n\r\n      // For successful responses, use a default message in case parsing fails\r\n      let responseData = { message: 'Permissions updated successfully' };\r\n      try {\r\n        // Use the original response for successful responses\r\n        responseData = await response.json();\r\n        console.log('Success response:', responseData);\r\n      } catch (e) {\r\n        console.log('Success response is not valid JSON, using default message');\r\n        // responseData already initialized with default value\r\n      }\r\n\r\n      // Show success message\r\n      setSuccess(true);\r\n      setTimeout(() => setSuccess(false), 3000);\r\n\r\n      return responseData;\r\n    } catch (err) {\r\n      setError(err.message || 'Failed to save permissions');\r\n      console.error('Error saving permissions:', err);\r\n    } finally {\r\n      setSaving(false);\r\n    }\r\n  };\r\n\r\n  return (\r\n    <Card sx={{ p: 3, mb: 3 }}>\r\n      <Typography variant=\"h5\" sx={{ mb: 2 }}>\r\n        User Permissions\r\n      </Typography>\r\n\r\n      {/* Show loading state */}\r\n      {loading && (\r\n        <Box sx={{ display: 'flex', justifyContent: 'center', my: 3 }}>\r\n          <CircularProgress />\r\n        </Box>\r\n      )}\r\n\r\n      {/* Show error message */}\r\n      {error && (\r\n        <Alert severity=\"error\" sx={{ mb: 3 }}>\r\n          {error}\r\n        </Alert>\r\n      )}\r\n\r\n      {/* Show success message */}\r\n      {success && (\r\n        <Alert severity=\"success\" sx={{ mb: 3 }}>\r\n          Permissions updated successfully!\r\n        </Alert>\r\n      )}\r\n\r\n      {/* Permissions grid */}\r\n      <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>\r\n        <Typography variant=\"subtitle1\" sx={{ mb: 2, fontWeight: 'bold', color: 'primary.main' }}>\r\n          PERMISSION SYSTEM REFERENCE GUIDE\r\n        </Typography>\r\n        <Typography variant=\"body2\" sx={{ mb: 3 }}>\r\n          This guide shows which permissions correspond to which menus and routes in the system.\r\n          When you grant a permission, the user will be able to access the corresponding menu and route.\r\n        </Typography>\r\n\r\n        <Divider sx={{ mb: 3 }} />\r\n\r\n        {/* Render permissions by category */}\r\n        {Object.entries(featureCategories).map(([categoryKey, category]) => (\r\n          <Box key={categoryKey} sx={{ mb: 4 }}>\r\n            <Typography variant=\"h6\" sx={{ mb: 2, fontWeight: 'bold', color: 'text.primary' }}>\r\n              {category.title}\r\n            </Typography>\r\n\r\n            {/* Render features in this category */}\r\n            {category.features.map(featureLabel => {\r\n              // Get the feature key from features object\r\n              const featureKey = Object.entries(features).find(([key, val]) => val === featureLabel)?.[0] || featureLabel.toLowerCase().replace(/\\s+/g, '');\r\n              const info = menuInfo[featureLabel] || {};\r\n\r\n              // Skip child features - they'll be shown with their parents\r\n              if (info.parent) {\r\n                return null;\r\n              }\r\n\r\n              const isAdminOnly = info.access === 'admin only';\r\n              const isUserOnly = info.access === 'user only';\r\n              const isBoth = info.access === 'both admin and user';\r\n\r\n              // Determine background color and chip color based on access type\r\n              let bgColor = 'white';\r\n              let chipColor = 'default';\r\n\r\n              if (isAdminOnly) {\r\n                bgColor = '#f8f9fa';\r\n                chipColor = 'error';\r\n              } else if (isUserOnly) {\r\n                bgColor = '#f0f4ff';\r\n                chipColor = 'primary';\r\n              } else if (isBoth) {\r\n                bgColor = '#e6f7ff'; // Light blue for both\r\n                chipColor = 'success';\r\n              }\r\n\r\n              // Find child features\r\n              const childFeatures = Object.entries(menuInfo).filter(([_, childInfo]) =>\r\n                childInfo.parent === featureLabel\r\n              ).map(([childKey, _]) => childKey);\r\n\r\n              return (\r\n                <Box key={featureKey} sx={{\r\n                  mb: 2,\r\n                  p: 2,\r\n                  border: '1px solid #eee',\r\n                  borderRadius: 1,\r\n                  backgroundColor: bgColor\r\n                }}>\r\n                  <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 1 }}>\r\n                    <Typography variant=\"subtitle1\" sx={{ fontWeight: 'bold' }}>\r\n                      {featureLabel}\r\n                    </Typography>\r\n\r\n                    {info.access && (\r\n                      <Chip\r\n                        label={info.access}\r\n                        size=\"small\"\r\n                        color={chipColor}\r\n                        sx={{\r\n                          fontWeight: 'bold',\r\n                          textTransform: 'uppercase',\r\n                          fontSize: '0.7rem'\r\n                        }}\r\n                      />\r\n                    )}\r\n                  </Box>\r\n\r\n                  {info.menuName && (\r\n                    <Box sx={{ mb: 1 }}>\r\n                      <Typography variant=\"body2\" sx={{ fontWeight: 'bold', display: 'inline' }}>\r\n                        Menu:\r\n                      </Typography>\r\n                      <Typography variant=\"body2\" sx={{ display: 'inline' }}>\r\n                        {info.menuName}\r\n                      </Typography>\r\n                    </Box>\r\n                  )}\r\n\r\n                  {info.route && (\r\n                    <Box sx={{ mb: 1 }}>\r\n                      <Typography variant=\"body2\" sx={{ fontWeight: 'bold', display: 'inline' }}>\r\n                        Route:\r\n                      </Typography>\r\n                      <Typography variant=\"body2\" sx={{ display: 'inline', fontFamily: 'monospace' }}>\r\n                        {info.route}\r\n                      </Typography>\r\n                    </Box>\r\n                  )}\r\n\r\n                  {info.description && (\r\n                    <Box sx={{ mb: 2 }}>\r\n                      <Typography variant=\"body2\" color=\"text.secondary\">\r\n                        {info.description}\r\n                      </Typography>\r\n                    </Box>\r\n                  )}\r\n\r\n                  <Divider sx={{ my: 1 }} />\r\n\r\n                  <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1 }}>\r\n                    {/* Only show relevant actions for this feature */}\r\n                    {Object.entries(actionLabels).filter(([action]) =>\r\n                        getAvailableActionsForFeature(featureKey, featureLabel).includes(action)).map(([action, label]) => (\r\n                        <FormControlLabel\r\n                          key={action}\r\n                          control={\r\n                            <Checkbox\r\n                              checked={permissions[featureKey]?.includes(action) || false}\r\n                              onChange={() => handleToggle(featureKey, action)}\r\n                              disabled={saving}\r\n                            />\r\n                          }\r\n                          label={label}\r\n                        />\r\n                    ))}\r\n                  </Box>\r\n\r\n                  {/* Render child features if any */}\r\n                  {childFeatures.length > 0 && (\r\n                    <Box sx={{ mt: 2, ml: 3, borderLeft: '2px solid #eee', pl: 2 }}>\r\n                      <Typography variant=\"subtitle2\" sx={{ mb: 1, fontWeight: 'bold' }}>\r\n                        Child Features:\r\n                      </Typography>\r\n\r\n                      {childFeatures.map(childFeatureLabel => {\r\n                        const childFeatureKey = Object.entries(features).find(([key, val]) => val === childFeatureLabel)?.[0] || childFeatureLabel.toLowerCase().replace(/\\s+/g, '');\r\n                        const childInfo = menuInfo[childFeatureLabel] || {};\r\n\r\n                        return (\r\n                          <Box key={childFeatureKey} sx={{\r\n                            mb: 2,\r\n                            p: 2,\r\n                            border: '1px solid #eee',\r\n                            borderRadius: 1,\r\n                            backgroundColor: 'rgba(0,0,0,0.02)'\r\n                          }}>\r\n                            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 1 }}>\r\n                              <Typography variant=\"body1\" sx={{ fontWeight: 'bold' }}>\r\n                                {childFeatureLabel}\r\n                              </Typography>\r\n\r\n                              {childInfo.access && (\r\n                                <Chip\r\n                                  label={childInfo.access}\r\n                                  size=\"small\"\r\n                                  color={chipColor}\r\n                                  sx={{\r\n                                    fontWeight: 'bold',\r\n                                    textTransform: 'uppercase',\r\n                                    fontSize: '0.7rem'\r\n                                  }}\r\n                                />\r\n                              )}\r\n                            </Box>\r\n\r\n                            {childInfo.route && (\r\n                              <Box sx={{ mb: 1 }}>\r\n                                <Typography variant=\"body2\" sx={{ fontWeight: 'bold', display: 'inline' }}>\r\n                                  Route:\r\n                                </Typography>\r\n                                <Typography variant=\"body2\" sx={{ display: 'inline', fontFamily: 'monospace' }}>\r\n                                  {childInfo.route}\r\n                                </Typography>\r\n                              </Box>\r\n                            )}\r\n\r\n                            <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1, mt: 1 }}>\r\n                              {/* Only show relevant actions for this child feature */}\r\n                              {Object.entries(actionLabels).filter(([action]) =>\r\n                                  getAvailableActionsForFeature(childFeatureKey, childFeatureLabel).includes(action)).map(([action, label]) => (\r\n                                  <FormControlLabel\r\n                                    key={action}\r\n                                    control={\r\n                                      <Checkbox\r\n                                        checked={permissions[childFeatureKey]?.includes(action) || false}\r\n                                        onChange={() => handleToggle(childFeatureKey, action)}\r\n                                        disabled={saving}\r\n                                        size=\"small\"\r\n                                      />\r\n                                    }\r\n                                    label={label}\r\n                                  />\r\n                              ))}\r\n                            </Box>\r\n                          </Box>\r\n                        );\r\n                      })}\r\n                    </Box>\r\n                  )}\r\n                </Box>\r\n              );\r\n            })}\r\n          </Box>\r\n        ))}\r\n      </Box>\r\n\r\n      {/* Save button */}\r\n      <Box sx={{ mt: 3, display: 'flex', justifyContent: 'flex-end' }}>\r\n        <Button\r\n          variant=\"contained\"\r\n          color=\"primary\"\r\n          onClick={handleSave}\r\n          disabled={saving || loading}\r\n        >\r\n          {saving ? 'Saving...' : 'Save Permissions'}\r\n        </Button>\r\n      </Box>\r\n    </Card>\r\n  );\r\n}\r\n\r\n// PropTypes validation\r\nPermission.propTypes = {\r\n  user: PropTypes.shape({\r\n    _id: PropTypes.string,\r\n    name: PropTypes.string,\r\n    email: PropTypes.string,\r\n    permissions: PropTypes.array\r\n  }),\r\n  form: PropTypes.object,\r\n  setForm: PropTypes.func\r\n};\r\n\r\n// Default props\r\nPermission.defaultProps = {\r\n  user: null\r\n};\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,OAAOC,SAAS,MAAM,YAAY;AAClC,SACEC,GAAG,EACHC,IAAI,EACJC,QAAQ,EACRC,gBAAgB,EAChBC,UAAU,EACVC,MAAM,EACNC,KAAK,EACLC,gBAAgB,EAChBC,IAAI,EACJC,OAAO,QACF,eAAe;AACtB,SAASC,SAAS,QAAQ,kBAAkB;AAC5C,SAASC,QAAQ,QAAQ,sBAAsB;AAC/C,SAASC,SAAS,QAAQ,iBAAiB;;AAG3C;AAAA,SAAAC,MAAA,IAAAC,OAAA;AACA,MAAMC,YAAY,GAAG;EACnBC,IAAI,EAAE,UAAU;EAChBC,QAAQ,EAAE,cAAc;EACxBC,SAAS,EAAE,eAAe;EAC1BC,SAAS,EAAE,cAAc;EACzBC,MAAM,EAAE,YAAY;EACpBC,MAAM,EAAE,UAAU;EAClBC,MAAM,EAAE;AACV,CAAC;;AAED;AACA,MAAMC,iBAAiB,GAAG;EACxBC,IAAI,EAAE;IACJC,KAAK,EAAE,eAAe;IACtBd,QAAQ,EAAE,CAAC,WAAW,EAAE,MAAM,EAAE,YAAY,EAAE,aAAa,EAAE,SAAS;EACxE,CAAC;EACDe,UAAU,EAAE;IACVD,KAAK,EAAE,oBAAoB;IAC3Bd,QAAQ,EAAE,CAAC,YAAY,EAAE,OAAO,EAAE,UAAU,EAAE,eAAe,EAAE,SAAS,EAAE,cAAc;EAC1F,CAAC;EACDgB,QAAQ,EAAE;IACRF,KAAK,EAAE,kBAAkB;IACzBd,QAAQ,EAAE,CAAC,UAAU,EAAE,cAAc,EAAE,kBAAkB,EAAE,mBAAmB,EAAC,QAAQ,EAAE,QAAQ,EAAE,OAAO,EAAE,UAAU,EAAE,WAAW;EACrI,CAAC;EACDiB,QAAQ,EAAE;IACRH,KAAK,EAAE,uBAAuB;IAC9Bd,QAAQ,EAAE,CAAC,UAAU,EAAE,UAAU,EAAE,cAAc,EAAE,cAAc,EAAE,eAAe;EACpF,CAAC;EACDkB,QAAQ,EAAE;IACRJ,KAAK,EAAE,SAAS;IAChBd,QAAQ,EAAE,CAAC,SAAS,EAAE,QAAQ;EAChC;AACF,CAAC;;AAED;AACA,MAAMmB,QAAQ,GAAG;EACf;EACAC,SAAS,EAAE;IACTC,QAAQ,EAAE,WAAW;IACrBC,KAAK,EAAE,gBAAgB;IACvBC,MAAM,EAAE,qBAAqB;IAC7BC,WAAW,EAAE,6CAA6C;IAC1DC,QAAQ,EAAE;EACZ,CAAC;EACDC,IAAI,EAAE;IACJL,QAAQ,EAAE,qBAAqB;IAC/BC,KAAK,EAAE,WAAW;IAClBC,MAAM,EAAE,YAAY;IACpBC,WAAW,EAAE,oCAAoC;IACjDC,QAAQ,EAAE;EACZ,CAAC;EACDE,UAAU,EAAE;IACVN,QAAQ,EAAE,YAAY;IACtBC,KAAK,EAAE,iBAAiB;IACxBC,MAAM,EAAE,YAAY;IACpBC,WAAW,EAAE,4BAA4B;IACzCC,QAAQ,EAAE;EACZ,CAAC;EACDG,WAAW,EAAE;IACXP,QAAQ,EAAE,aAAa;IACvBC,KAAK,EAAE,kBAAkB;IACzBC,MAAM,EAAE,YAAY;IACpBC,WAAW,EAAE,yBAAyB;IACtCC,QAAQ,EAAE;EACZ,CAAC;EACDI,OAAO,EAAE;IACPR,QAAQ,EAAE,SAAS;IACnBC,KAAK,EAAE,cAAc;IACrBC,MAAM,EAAE,qBAAqB;IAC7BC,WAAW,EAAE,sFAAsF;IACnGC,QAAQ,EAAE;EACZ,CAAC;EAED;EACAK,UAAU,EAAE;IACVT,QAAQ,EAAE,YAAY;IACtBC,KAAK,EAAE,iBAAiB;IACxBC,MAAM,EAAE,YAAY;IACpBC,WAAW,EAAE,8CAA8C;IAC3DC,QAAQ,EAAE;EACZ,CAAC;EACDM,KAAK,EAAE;IACLV,QAAQ,EAAE,kBAAkB;IAC5BC,KAAK,EAAE,YAAY;IACnBC,MAAM,EAAE,YAAY;IACpBC,WAAW,EAAE,yCAAyC;IACtDC,QAAQ,EAAE;EACZ,CAAC;EACDO,QAAQ,EAAE;IACRX,QAAQ,EAAE,UAAU;IACpBC,KAAK,EAAE,qBAAqB;IAC5BC,MAAM,EAAE,YAAY;IACpBC,WAAW,EAAE,qBAAqB;IAClCC,QAAQ,EAAE,YAAY;IACtBQ,MAAM,EAAE;EACV,CAAC;EACDC,aAAa,EAAE;IACbb,QAAQ,EAAE,eAAe;IACzBC,KAAK,EAAE,0BAA0B;IACjCC,MAAM,EAAE,YAAY;IACpBC,WAAW,EAAE,0BAA0B;IACvCC,QAAQ,EAAE,YAAY;IACtBQ,MAAM,EAAE;EACV,CAAC;EACDE,OAAO,EAAE;IACPd,QAAQ,EAAE,UAAU;IACpBC,KAAK,EAAE,qBAAqB;IAC5BC,MAAM,EAAE,YAAY;IACpBC,WAAW,EAAE,wBAAwB;IACrCC,QAAQ,EAAE,YAAY;IACtBQ,MAAM,EAAE;EACV,CAAC;EACD,cAAc,EAAE;IACdZ,QAAQ,EAAE,cAAc;IACxBC,KAAK,EAAE,mBAAmB;IAC1BC,MAAM,EAAE,YAAY;IACpBC,WAAW,EAAE,oBAAoB;IACjCC,QAAQ,EAAE,YAAY;IACtBQ,MAAM,EAAE;EACV,CAAC;EAED;EACAG,QAAQ,EAAE;IACRf,QAAQ,EAAE,UAAU;IACpBC,KAAK,EAAE,mBAAmB;IAC1BC,MAAM,EAAE,YAAY;IACpBC,WAAW,EAAE,qBAAqB;IAClCC,QAAQ,EAAE;EACZ,CAAC;EACD,cAAc,EAAE;IACdJ,QAAQ,EAAE,cAAc;IACxBC,KAAK,EAAE,mBAAmB;IAC1BC,MAAM,EAAE,YAAY;IACpBC,WAAW,EAAE,2BAA2B;IACxCC,QAAQ,EAAE,UAAU;IACpBQ,MAAM,EAAE;EACV,CAAC;EACD,kBAAkB,EAAE;IAClBZ,QAAQ,EAAE,kBAAkB;IAC5BC,KAAK,EAAE,uBAAuB;IAC9BC,MAAM,EAAE,YAAY;IACpBC,WAAW,EAAE,uBAAuB;IACpCC,QAAQ,EAAE,UAAU;IACpBQ,MAAM,EAAE;EACV,CAAC;EACD,mBAAmB,EAAE;IACnBZ,QAAQ,EAAE,mBAAmB;IAC7BC,KAAK,EAAE,wBAAwB;IAC/BC,MAAM,EAAE,YAAY;IACpBC,WAAW,EAAE,2BAA2B;IACxCC,QAAQ,EAAE,UAAU;IACpBQ,MAAM,EAAE;EACV,CAAC;EACDI,MAAM,EAAE;IACNhB,QAAQ,EAAE,QAAQ;IAClBC,KAAK,EAAE,aAAa;IACpBC,MAAM,EAAE,YAAY;IACpBC,WAAW,EAAE,gBAAgB;IAC7BC,QAAQ,EAAE;EACZ,CAAC;EACDa,KAAK,EAAE;IACLjB,QAAQ,EAAE,OAAO;IACjBC,KAAK,EAAE,YAAY;IACnBC,MAAM,EAAE,qBAAqB;IAC7BC,WAAW,EAAE,uBAAuB;IACpCC,QAAQ,EAAE;EACZ,CAAC;EACD,UAAU,EAAE;IACVJ,QAAQ,EAAE,UAAU;IACpBC,KAAK,EAAE,YAAY;IACnBC,MAAM,EAAE,WAAW;IACnBC,WAAW,EAAE,4BAA4B;IACzCC,QAAQ,EAAE;EACZ,CAAC;EACD,WAAW,EAAE;IACXJ,QAAQ,EAAE,WAAW;IACrBC,KAAK,EAAE,yBAAyB;IAChCC,MAAM,EAAE,WAAW;IACnBC,WAAW,EAAE,iBAAiB;IAC9BC,QAAQ,EAAE,UAAU;IACpBQ,MAAM,EAAE;EACV,CAAC;EACD;EACFM,MAAM,EAAE;IACNlB,QAAQ,EAAE,SAAS;IACnBC,KAAK,EAAE,aAAa;IACpBC,MAAM,EAAE,qBAAqB;IAAG;IAChCC,WAAW,EAAE,+DAA+D;IAC5EC,QAAQ,EAAE;EACZ,CAAC;EAED,YAAY,EAAE;IACZJ,QAAQ,EAAE,YAAY;IACtBC,KAAK,EAAE,kBAAkB;IACzBC,MAAM,EAAE,WAAW;IACnBC,WAAW,EAAE,mDAAmD;IAChEC,QAAQ,EAAE,UAAU;IACpBe,eAAe,EAAE;EACnB,CAAC;EAIC;EACAC,QAAQ,EAAE;IACRpB,QAAQ,EAAE,UAAU;IACpBC,KAAK,EAAE,eAAe;IACtBC,MAAM,EAAE,YAAY;IACpBC,WAAW,EAAE,2BAA2B;IACxCC,QAAQ,EAAE;EACZ,CAAC;EACDiB,QAAQ,EAAE;IACRrB,QAAQ,EAAE,UAAU;IACpBC,KAAK,EAAE,wBAAwB;IAC/BC,MAAM,EAAE,YAAY;IACpBC,WAAW,EAAE,wBAAwB;IACrCC,QAAQ,EAAE,UAAU;IACpBQ,MAAM,EAAE;EACV,CAAC;EACD,cAAc,EAAE;IACdZ,QAAQ,EAAE,cAAc;IACxBC,KAAK,EAAE,uBAAuB;IAC9BC,MAAM,EAAE,YAAY;IACpBC,WAAW,EAAE,sBAAsB;IACnCC,QAAQ,EAAE,UAAU;IACpBQ,MAAM,EAAE;EACV,CAAC;EACD,cAAc,EAAE;IACdZ,QAAQ,EAAE,cAAc;IACxBC,KAAK,EAAE,2BAA2B;IAClCC,MAAM,EAAE,YAAY;IACpBC,WAAW,EAAE,sBAAsB;IACnCC,QAAQ,EAAE,UAAU;IACpBQ,MAAM,EAAE;EACV,CAAC;EACD,eAAe,EAAE;IACfZ,QAAQ,EAAE,eAAe;IACzBC,KAAK,EAAE,4BAA4B;IACnCC,MAAM,EAAE,YAAY;IACpBC,WAAW,EAAE,uBAAuB;IACpCC,QAAQ,EAAE,UAAU;IACpBQ,MAAM,EAAE;EACV,CAAC;EAED;EACAU,OAAO,EAAE;IACPtB,QAAQ,EAAE,UAAU;IACpBC,KAAK,EAAE,eAAe;IACtBC,MAAM,EAAE,YAAY;IACpBC,WAAW,EAAE,4BAA4B;IACzCC,QAAQ,EAAE;EACZ,CAAC;EACDmB,MAAM,EAAE;IACNvB,QAAQ,EAAE,QAAQ;IAClBC,KAAK,EAAE,aAAa;IACpBC,MAAM,EAAE,YAAY;IACpBC,WAAW,EAAE,2BAA2B;IACxCC,QAAQ,EAAE;EACZ,CAAC;EAED;EACAoB,eAAe,EAAE;IACfxB,QAAQ,EAAE,kBAAkB;IAC5BC,KAAK,EAAE,4BAA4B;IACnCC,MAAM,EAAE,WAAW;IACnBC,WAAW,EAAE,4BAA4B;IACzCC,QAAQ,EAAE,UAAU;IACpBe,eAAe,EAAE;EACnB,CAAC;EACDM,gBAAgB,EAAE;IAChBzB,QAAQ,EAAE,mBAAmB;IAC7BC,KAAK,EAAE,6BAA6B;IACpCC,MAAM,EAAE,WAAW;IACnBC,WAAW,EAAE,gCAAgC;IAC7CC,QAAQ,EAAE,UAAU;IACpBe,eAAe,EAAE;EACnB,CAAC;EACDO,WAAW,EAAE;IACX1B,QAAQ,EAAE,cAAc;IACxBC,KAAK,EAAE,oBAAoB;IAC3BC,MAAM,EAAE,WAAW;IACnBC,WAAW,EAAE,oBAAoB;IACjCC,QAAQ,EAAE,UAAU;IACpBe,eAAe,EAAE;EACnB;AACF,CAAC;;AAED;AACA,MAAMQ,6BAA6B,GAAGA,CAACC,UAAU,EAAEC,YAAY,KAAK;EAClE;EACA,MAAMC,cAAc,GAAG,CAAC,MAAM,EAAE,QAAQ,EAAE,QAAQ,EAAE,QAAQ,CAAC;;EAE7D;EACF,MAAMC,YAAY,GAAG,CACnB,WAAW,EAAE,YAAY,EAAE,OAAO,EAAE,UAAU,EAAE,cAAc,EAC9D,kBAAkB,EAAE,mBAAmB,EAAE,UAAU,EAAE,UAAU,EAC/D,cAAc,EAAE,cAAc,EAAE,eAAe,EAAE,QAAQ,EAAE,QAAQ,CACpE;EAGC,MAAMC,aAAa,GAAG,CACpB,YAAY,EAAE,OAAO,EAAE,MAAM,CAC9B;EAEF,MAAMC,aAAa,GAAG,CACrB,YAAY,EAAE,OAAO,EAAE,OAAO,EAAE,UAAU,EAAE,YAAY,EAAE,QAAQ,CACnE;;EAGC;EACA,IAAIC,OAAO,GAAG,CAAC,GAAGJ,cAAc,CAAC;;EAEjC;EACA,IAAIC,YAAY,CAACI,QAAQ,CAACN,YAAY,CAAC,EAAE;IACvCK,OAAO,CAACE,IAAI,CAAC,UAAU,CAAC;EAC1B;EAEA,IAAIJ,aAAa,CAACG,QAAQ,CAACN,YAAY,CAAC,EAAE;IACxCK,OAAO,CAACE,IAAI,CAAC,WAAW,CAAC;EAC3B;EAEA,IAAIH,aAAa,CAACE,QAAQ,CAACN,YAAY,CAAC,EAAE;IACxCK,OAAO,CAACE,IAAI,CAAC,WAAW,CAAC;EAC3B;EAEA,OAAOF,OAAO;AAChB,CAAC;AAED,eAAe,SAASG,UAAUA,CAAC;EAAEC;AAAK,CAAC,EAAE;EAAAC,EAAA;EAC3C;EACA,MAAM;IAAEC,MAAM,EAAEC;EAAU,CAAC,GAAG/D,SAAS,CAAC,CAAC;EACzC,MAAM8D,MAAM,GAAG,CAAAF,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEI,GAAG,KAAID,SAAS;EAErC,MAAM,CAACE,OAAO,EAAEC,UAAU,CAAC,GAAG/E,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACgF,MAAM,EAAEC,SAAS,CAAC,GAAGjF,QAAQ,CAAC,KAAK,CAAC;EAC3C,MAAM,CAACkF,KAAK,EAAEC,QAAQ,CAAC,GAAGnF,QAAQ,CAAC,IAAI,CAAC;EACxC,MAAM,CAACoF,OAAO,EAAEC,UAAU,CAAC,GAAGrF,QAAQ,CAAC,KAAK,CAAC;;EAE7C;EACA,MAAM,CAACsF,WAAW,EAAEC,cAAc,CAAC,GAAGvF,QAAQ,CAAC,CAAC,CAAC,CAAC;;EAElD;EACAC,SAAS,CAAC,MAAM;IACduF,OAAO,CAACC,GAAG,CAAC,mCAAmC,EAAEhB,IAAI,CAAC;IACtDe,OAAO,CAACC,GAAG,CAAC,2CAA2C,EAAEhB,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEI,GAAG,CAAC;IACnEW,OAAO,CAACC,GAAG,CAAC,oCAAoC,EAAEb,SAAS,CAAC;IAC5DY,OAAO,CAACC,GAAG,CAAC,sCAAsC,EAAEd,MAAM,CAAC;;IAE3D;IACA,MAAMe,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,gBAAgB,CAAC;IACpDJ,OAAO,CAACC,GAAG,CAAC,kBAAkB,EAAEC,KAAK,GAAG,KAAK,GAAG,IAAI,CAAC;IAErD,IAAI,CAACf,MAAM,EAAE;MACXa,OAAO,CAACK,IAAI,CAAC,8CAA8C,CAAC;IAC9D;EACF,CAAC,EAAE,CAACpB,IAAI,EAAEG,SAAS,EAAED,MAAM,CAAC,CAAC;;EAE7B;EACA1E,SAAS,CAAC,MAAM;IACd,IAAI0E,MAAM,EAAE;MACVa,OAAO,CAACC,GAAG,CAAC,mCAAmC,EAAEd,MAAM,CAAC;MACxDmB,oBAAoB,CAAC,CAAC;IACxB,CAAC,MAAM;MACLN,OAAO,CAACK,IAAI,CAAC,4CAA4C,CAAC;MAC1DV,QAAQ,CAAC,+CAA+C,CAAC;IAC3D;EACF,CAAC,EAAE,CAACR,MAAM,CAAC,CAAC;;EAEZ;EACA,MAAMmB,oBAAoB,GAAG,MAAAA,CAAA,KAAY;IACvC,IAAI;MACFf,UAAU,CAAC,IAAI,CAAC;MAChBI,QAAQ,CAAC,IAAI,CAAC;;MAEd;MACA,MAAMO,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,gBAAgB,CAAC;;MAEpD;MACAJ,OAAO,CAACC,GAAG,CAAC,gCAAgC,EAAEC,KAAK,GAAG,cAAc,GAAG,gBAAgB,CAAC;;MAExF;MACA,MAAMK,WAAW,GAAGC,MAAM,CAACC,QAAQ,CAACC,QAAQ,CAAC5B,QAAQ,CAAC,YAAY,CAAC;MACnEkB,OAAO,CAACC,GAAG,CAAC,gBAAgB,EAAEM,WAAW,CAAC;;MAE1C;MACA,IAAII,eAAe,GAAG,EAAE;MAExB,IAAIJ,WAAW,EAAE;QACf;QACA;QACA,MAAMK,MAAM,GAAGrF,SAAS,CAAC,eAAe4D,MAAM,EAAE,CAAC;QACjDa,OAAO,CAACC,GAAG,CAAC,sCAAsC,EAAEW,MAAM,CAAC;QAE3D,MAAMC,QAAQ,GAAG,MAAMC,KAAK,CAACF,MAAM,EAAE;UACnCG,MAAM,EAAE,KAAK;UACbC,OAAO,EAAE;YACP,eAAe,EAAE,UAAUd,KAAK,EAAE;YAClC,cAAc,EAAE;UAClB;QACF,CAAC,CAAC;QAEF,IAAI,CAACW,QAAQ,CAACI,EAAE,EAAE;UAChB;UACA,MAAM,IAAIC,KAAK,CAAC,qCAAqC,CAAC;QACxD;QAEAP,eAAe,GAAG,MAAME,QAAQ,CAACM,IAAI,CAAC,CAAC;MACzC,CAAC,MAAM;QACL;QACA,MAAMP,MAAM,GAAGrF,SAAS,CAAC,QAAQ4D,MAAM,EAAE,CAAC;QAC1C;;QAEA,MAAM0B,QAAQ,GAAG,MAAMC,KAAK,CAACF,MAAM,EAAE;UACnCG,MAAM,EAAE,KAAK;UACbC,OAAO,EAAE;YACP,eAAe,EAAE,UAAUd,KAAK,EAAE;YAClC,cAAc,EAAE;UAClB;QACF,CAAC,CAAC;QAEF,IAAI,CAACW,QAAQ,CAACI,EAAE,EAAE;UAChB,MAAM,IAAIC,KAAK,CAAC,2BAA2B,CAAC;QAC9C;QAEA,MAAME,QAAQ,GAAG,MAAMP,QAAQ,CAACM,IAAI,CAAC,CAAC;QACtCR,eAAe,GAAGS,QAAQ,CAACtB,WAAW,IAAI,EAAE;MAC9C;;MAEA;MACA;;MAEA;MACA,MAAMuB,oBAAoB,GAAG,CAAC,CAAC;MAC/B,IAAIC,KAAK,CAACC,OAAO,CAACZ,eAAe,CAAC,EAAE;QAClCA,eAAe,CAACa,OAAO,CAACC,UAAU,IAAI;UACpC;UACA,IAAIC,cAAc,GAAGD,UAAU,CAACE,IAAI;;UAEpC;UACA,MAAMC,YAAY,GAAGC,MAAM,CAACC,OAAO,CAACxG,QAAQ,CAAC,CAACyG,IAAI,CAAC,CAAC,CAACC,CAAC,EAAEC,GAAG,CAAC,KAAKA,GAAG,KAAKR,UAAU,CAACE,IAAI,CAAC;UACzF,IAAIC,YAAY,EAAE;YAChB;YACAF,cAAc,GAAGE,YAAY,CAAC,CAAC,CAAC;UAClC,CAAC,MAAM;YACL;YACA,IAAIH,UAAU,CAACE,IAAI,KAAK,UAAU,EAAE;cAClCD,cAAc,GAAG,SAAS;YAC5B,CAAC,MAAM,IAAID,UAAU,CAACE,IAAI,KAAK,cAAc,EAAE;cAC7CD,cAAc,GAAG,aAAa;YAChC,CAAC,MAAM,IAAID,UAAU,CAACE,IAAI,KAAK,kBAAkB,EAAE;cACjDD,cAAc,GAAG,iBAAiB;YACpC,CAAC,MAAM,IAAID,UAAU,CAACE,IAAI,KAAK,mBAAmB,EAAE;cAClDD,cAAc,GAAG,kBAAkB;YACrC,CAAC,MAAM;cACL;cACAA,cAAc,GAAGD,UAAU,CAACE,IAAI,CAACO,WAAW,CAAC,CAAC,CAACC,OAAO,CAAC,MAAM,EAAE,EAAE,CAAC;YACpE;UACF;UAEAd,oBAAoB,CAACK,cAAc,CAAC,GAAGD,UAAU,CAACW,IAAI;QACxD,CAAC,CAAC;MACJ,CAAC,MAAM;QACLpC,OAAO,CAACK,IAAI,CAAC,mCAAmC,EAAEM,eAAe,CAAC;MACpE;;MAEA;MACAX,OAAO,CAACC,GAAG,CAAC,+BAA+B,EAAEoB,oBAAoB,CAAC;MAElEtB,cAAc,CAACsB,oBAAoB,CAAC;IACtC,CAAC,CAAC,OAAOgB,GAAG,EAAE;MACZ1C,QAAQ,CAAC0C,GAAG,CAACC,OAAO,IAAI,4BAA4B,CAAC;MACrDtC,OAAO,CAACN,KAAK,CAAC,6BAA6B,EAAE2C,GAAG,CAAC;IACnD,CAAC,SAAS;MACR9C,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACA,MAAMgD,YAAY,GAAGA,CAACC,OAAO,EAAEC,MAAM,KAAK;IACxC1C,cAAc,CAAE2C,IAAI,IAAK;MACvB,MAAMC,OAAO,GAAGD,IAAI,CAACF,OAAO,CAAC,IAAI,EAAE;MACnC,MAAMI,OAAO,GAAGD,OAAO,CAAC7D,QAAQ,CAAC2D,MAAM,CAAC,GAAGE,OAAO,CAACE,MAAM,CAAEC,CAAC,IAAKA,CAAC,KAAKL,MAAM,CAAC,GAAG,CAAC,GAAGE,OAAO,EAAEF,MAAM,CAAC;;MAErG;MACAzC,OAAO,CAACC,GAAG,CAAC,sBAAsBuC,OAAO,MAAMC,MAAM,MAAME,OAAO,CAAC7D,QAAQ,CAAC2D,MAAM,CAAC,GAAG,SAAS,GAAG,OAAO,EAAE,CAAC;MAC5GzC,OAAO,CAACC,GAAG,CAAC,WAAWuC,OAAO,yBAAyBI,OAAO,CAACG,IAAI,CAAC,IAAI,CAAC,IAAI,OAAO,EAAE,CAAC;MAEvF,OAAO;QAAE,GAAGL,IAAI;QAAE,CAACF,OAAO,GAAGI;MAAQ,CAAC;IACxC,CAAC,CAAC;EACJ,CAAC;;EAED;EACA,MAAMI,UAAU,GAAG,MAAAA,CAAA,KAAY;IAC7B,IAAI;MACF;MACA,IAAI,CAAC7D,MAAM,EAAE;QACXQ,QAAQ,CAAC,6CAA6C,CAAC;QACvDK,OAAO,CAACN,KAAK,CAAC,iDAAiD,CAAC;QAChE;MACF;MAEAD,SAAS,CAAC,IAAI,CAAC;MACfE,QAAQ,CAAC,IAAI,CAAC;MACdE,UAAU,CAAC,KAAK,CAAC;;MAEjB;MACA,MAAMwB,oBAAoB,GAAGQ,MAAM,CAACC,OAAO,CAAChC,WAAW,CAAC,CAACmD,GAAG,CAAC,CAAC,CAACtB,IAAI,EAAES,IAAI,CAAC,KAAK;QAC7E;QACA,IAAIV,cAAc,GAAGC,IAAI;;QAEzB;QACA,IAAIA,IAAI,KAAK,OAAO,IAAIA,IAAI,KAAK,SAAS,EAAE;UAC1CD,cAAc,GAAG,UAAU;QAC7B;;QAEA;QACA,IAAIC,IAAI,KAAK,aAAa,IAAIA,IAAI,KAAK,aAAa,EAAE;UACpDD,cAAc,GAAG,cAAc;QACjC;QAEA,IAAIC,IAAI,KAAK,iBAAiB,IAAIA,IAAI,KAAK,iBAAiB,EAAE;UAC5DD,cAAc,GAAG,kBAAkB;QACrC;QAEA,IAAIC,IAAI,KAAK,kBAAkB,IAAIA,IAAI,KAAK,kBAAkB,EAAE;UAC9DD,cAAc,GAAG,mBAAmB;QACtC;;QAEA;QACA,MAAME,YAAY,GAAGC,MAAM,CAACC,OAAO,CAACxG,QAAQ,CAAC,CAACyG,IAAI,CAAC,CAAC,CAACmB,GAAG,EAAElB,CAAC,CAAC,KAAKkB,GAAG,KAAKvB,IAAI,CAAC;QAC9E,IAAIC,YAAY,EAAE;UAChBF,cAAc,GAAGE,YAAY,CAAC,CAAC,CAAC;QAClC;QAEA,OAAO;UACLD,IAAI,EAAED,cAAc;UACpBU;QACF,CAAC;MACH,CAAC,CAAC;;MAEF;MACApC,OAAO,CAACC,GAAG,CAAC,0BAA0B,EAAE;QACtCd,MAAM;QACNW,WAAW,EAAEuB;MACf,CAAC,CAAC;;MAEF;MACA,MAAMnB,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,gBAAgB,CAAC;;MAEpD;MACAJ,OAAO,CAACC,GAAG,CAAC,kBAAkB,EAAEkD,IAAI,CAACC,SAAS,CAAC;QAAEtD,WAAW,EAAEuB;MAAqB,CAAC,CAAC,CAAC;;MAEtF;MACA,MAAMd,WAAW,GAAGC,MAAM,CAACC,QAAQ,CAACC,QAAQ,CAAC5B,QAAQ,CAAC,YAAY,CAAC;MACnEkB,OAAO,CAACC,GAAG,CAAC,2BAA2B,EAAEM,WAAW,CAAC;;MAErD;MACA,IAAIM,QAAQ,GAAG,IAAI;MAEnB,IAAIN,WAAW,EAAE;QACf;QACA;QACA,MAAMK,MAAM,GAAGrF,SAAS,CAAC,eAAe4D,MAAM,EAAE,CAAC;QACjDa,OAAO,CAACC,GAAG,CAAC,kCAAkC,EAAEW,MAAM,CAAC;QAEvDC,QAAQ,GAAG,MAAMC,KAAK,CAACF,MAAM,EAAE;UAC7BG,MAAM,EAAE,KAAK;UACbC,OAAO,EAAE;YACP,cAAc,EAAE,kBAAkB;YAClC,eAAe,EAAE,UAAUd,KAAK;UAClC,CAAC;UACDmD,IAAI,EAAEF,IAAI,CAACC,SAAS,CAAC;YAAEtD,WAAW,EAAEuB;UAAqB,CAAC;QAC5D,CAAC,CAAC;MACJ,CAAC,MAAM;QACL;QACA,MAAMT,MAAM,GAAGrF,SAAS,CAAC,QAAQ4D,MAAM,EAAE,CAAC;QAC1Ca,OAAO,CAACC,GAAG,CAAC,iCAAiC,EAAEW,MAAM,CAAC;QAEtDC,QAAQ,GAAG,MAAMC,KAAK,CAACF,MAAM,EAAE;UAC7BG,MAAM,EAAE,OAAO;UACfC,OAAO,EAAE;YACP,cAAc,EAAE,kBAAkB;YAClC,eAAe,EAAE,UAAUd,KAAK;UAClC,CAAC;UACDmD,IAAI,EAAEF,IAAI,CAACC,SAAS,CAAC;YAAEtD,WAAW,EAAEuB;UAAqB,CAAC;QAC5D,CAAC,CAAC;MACJ;;MAEA;MACA,MAAMiC,cAAc,GAAGzC,QAAQ,CAAC0C,KAAK,CAAC,CAAC;;MAEvC;MACA,IAAI,CAAC1C,QAAQ,CAACI,EAAE,EAAE;QAChB;QACA,IAAIuC,YAAY,GAAG,8BAA8B;QACjD,IAAI;UACF,MAAMC,SAAS,GAAG,MAAMH,cAAc,CAACnC,IAAI,CAAC,CAAC;UAC7CqC,YAAY,GAAGC,SAAS,CAACnB,OAAO,IAAIkB,YAAY;QAClD,CAAC,CAAC,OAAOE,CAAC,EAAE;UACV1D,OAAO,CAACC,GAAG,CAAC,mCAAmC,EAAEyD,CAAC,CAAC;UACnD;UACA,IAAI;YACF,MAAMC,SAAS,GAAG,MAAM9C,QAAQ,CAAC+C,IAAI,CAAC,CAAC;YACvC,IAAID,SAAS,EAAE;cACbH,YAAY,GAAGG,SAAS;YAC1B;UACF,CAAC,CAAC,OAAOE,SAAS,EAAE;YAClB7D,OAAO,CAACN,KAAK,CAAC,iCAAiC,EAAEmE,SAAS,CAAC;UAC7D;QACF;QACA,MAAM,IAAI3C,KAAK,CAACsC,YAAY,CAAC;MAC/B;;MAEA;MACA,IAAIM,YAAY,GAAG;QAAExB,OAAO,EAAE;MAAmC,CAAC;MAClE,IAAI;QACF;QACAwB,YAAY,GAAG,MAAMjD,QAAQ,CAACM,IAAI,CAAC,CAAC;QACpCnB,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAE6D,YAAY,CAAC;MAChD,CAAC,CAAC,OAAOJ,CAAC,EAAE;QACV1D,OAAO,CAACC,GAAG,CAAC,2DAA2D,CAAC;QACxE;MACF;;MAEA;MACAJ,UAAU,CAAC,IAAI,CAAC;MAChBkE,UAAU,CAAC,MAAMlE,UAAU,CAAC,KAAK,CAAC,EAAE,IAAI,CAAC;MAEzC,OAAOiE,YAAY;IACrB,CAAC,CAAC,OAAOzB,GAAG,EAAE;MACZ1C,QAAQ,CAAC0C,GAAG,CAACC,OAAO,IAAI,4BAA4B,CAAC;MACrDtC,OAAO,CAACN,KAAK,CAAC,2BAA2B,EAAE2C,GAAG,CAAC;IACjD,CAAC,SAAS;MACR5C,SAAS,CAAC,KAAK,CAAC;IAClB;EACF,CAAC;EAED,oBACEhE,OAAA,CAACb,IAAI;IAACoJ,EAAE,EAAE;MAAEC,CAAC,EAAE,CAAC;MAAEC,EAAE,EAAE;IAAE,CAAE;IAAAC,QAAA,gBACxB1I,OAAA,CAACV,UAAU;MAACqJ,OAAO,EAAC,IAAI;MAACJ,EAAE,EAAE;QAAEE,EAAE,EAAE;MAAE,CAAE;MAAAC,QAAA,EAAC;IAExC;MAAAE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAY,CAAC,EAGZlF,OAAO,iBACN7D,OAAA,CAACd,GAAG;MAACqJ,EAAE,EAAE;QAAES,OAAO,EAAE,MAAM;QAAEC,cAAc,EAAE,QAAQ;QAAEC,EAAE,EAAE;MAAE,CAAE;MAAAR,QAAA,eAC5D1I,OAAA,CAACP,gBAAgB;QAAAmJ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACjB,CACN,EAGA9E,KAAK,iBACJjE,OAAA,CAACR,KAAK;MAAC2J,QAAQ,EAAC,OAAO;MAACZ,EAAE,EAAE;QAAEE,EAAE,EAAE;MAAE,CAAE;MAAAC,QAAA,EACnCzE;IAAK;MAAA2E,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CACR,EAGA5E,OAAO,iBACNnE,OAAA,CAACR,KAAK;MAAC2J,QAAQ,EAAC,SAAS;MAACZ,EAAE,EAAE;QAAEE,EAAE,EAAE;MAAE,CAAE;MAAAC,QAAA,EAAC;IAEzC;MAAAE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAO,CACR,eAGD/I,OAAA,CAACd,GAAG;MAACqJ,EAAE,EAAE;QAAES,OAAO,EAAE,MAAM;QAAEI,aAAa,EAAE,QAAQ;QAAEC,GAAG,EAAE;MAAE,CAAE;MAAAX,QAAA,gBAC5D1I,OAAA,CAACV,UAAU;QAACqJ,OAAO,EAAC,WAAW;QAACJ,EAAE,EAAE;UAAEE,EAAE,EAAE,CAAC;UAAEa,UAAU,EAAE,MAAM;UAAEC,KAAK,EAAE;QAAe,CAAE;QAAAb,QAAA,EAAC;MAE1F;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eACb/I,OAAA,CAACV,UAAU;QAACqJ,OAAO,EAAC,OAAO;QAACJ,EAAE,EAAE;UAAEE,EAAE,EAAE;QAAE,CAAE;QAAAC,QAAA,EAAC;MAG3C;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eAEb/I,OAAA,CAACL,OAAO;QAAC4I,EAAE,EAAE;UAAEE,EAAE,EAAE;QAAE;MAAE;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,EAGzB3C,MAAM,CAACC,OAAO,CAAC5F,iBAAiB,CAAC,CAAC+G,GAAG,CAAC,CAAC,CAACgC,WAAW,EAAElI,QAAQ,CAAC,kBAC7DtB,OAAA,CAACd,GAAG;QAAmBqJ,EAAE,EAAE;UAAEE,EAAE,EAAE;QAAE,CAAE;QAAAC,QAAA,gBACnC1I,OAAA,CAACV,UAAU;UAACqJ,OAAO,EAAC,IAAI;UAACJ,EAAE,EAAE;YAAEE,EAAE,EAAE,CAAC;YAAEa,UAAU,EAAE,MAAM;YAAEC,KAAK,EAAE;UAAe,CAAE;UAAAb,QAAA,EAC/EpH,QAAQ,CAACX;QAAK;UAAAiI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC,EAGZzH,QAAQ,CAACzB,QAAQ,CAAC2H,GAAG,CAACzE,YAAY,IAAI;UAAA,IAAA0G,oBAAA;UACrC;UACA,MAAM3G,UAAU,GAAG,EAAA2G,oBAAA,GAAArD,MAAM,CAACC,OAAO,CAACxG,QAAQ,CAAC,CAACyG,IAAI,CAAC,CAAC,CAACmB,GAAG,EAAEjB,GAAG,CAAC,KAAKA,GAAG,KAAKzD,YAAY,CAAC,cAAA0G,oBAAA,uBAAnEA,oBAAA,CAAsE,CAAC,CAAC,KAAI1G,YAAY,CAAC0D,WAAW,CAAC,CAAC,CAACC,OAAO,CAAC,MAAM,EAAE,EAAE,CAAC;UAC7I,MAAMgD,IAAI,GAAG1I,QAAQ,CAAC+B,YAAY,CAAC,IAAI,CAAC,CAAC;;UAEzC;UACA,IAAI2G,IAAI,CAAC5H,MAAM,EAAE;YACf,OAAO,IAAI;UACb;UAEA,MAAM6H,WAAW,GAAGD,IAAI,CAACtI,MAAM,KAAK,YAAY;UAChD,MAAMwI,UAAU,GAAGF,IAAI,CAACtI,MAAM,KAAK,WAAW;UAC9C,MAAMyI,MAAM,GAAGH,IAAI,CAACtI,MAAM,KAAK,qBAAqB;;UAEpD;UACA,IAAI0I,OAAO,GAAG,OAAO;UACrB,IAAIC,SAAS,GAAG,SAAS;UAEzB,IAAIJ,WAAW,EAAE;YACfG,OAAO,GAAG,SAAS;YACnBC,SAAS,GAAG,OAAO;UACrB,CAAC,MAAM,IAAIH,UAAU,EAAE;YACrBE,OAAO,GAAG,SAAS;YACnBC,SAAS,GAAG,SAAS;UACvB,CAAC,MAAM,IAAIF,MAAM,EAAE;YACjBC,OAAO,GAAG,SAAS,CAAC,CAAC;YACrBC,SAAS,GAAG,SAAS;UACvB;;UAEA;UACA,MAAMC,aAAa,GAAG5D,MAAM,CAACC,OAAO,CAACrF,QAAQ,CAAC,CAACoG,MAAM,CAAC,CAAC,CAACb,CAAC,EAAE0D,SAAS,CAAC,KACnEA,SAAS,CAACnI,MAAM,KAAKiB,YACvB,CAAC,CAACyE,GAAG,CAAC,CAAC,CAAC0C,QAAQ,EAAE3D,CAAC,CAAC,KAAK2D,QAAQ,CAAC;UAElC,oBACElK,OAAA,CAACd,GAAG;YAAkBqJ,EAAE,EAAE;cACxBE,EAAE,EAAE,CAAC;cACLD,CAAC,EAAE,CAAC;cACJ2B,MAAM,EAAE,gBAAgB;cACxBC,YAAY,EAAE,CAAC;cACfC,eAAe,EAAEP;YACnB,CAAE;YAAApB,QAAA,gBACA1I,OAAA,CAACd,GAAG;cAACqJ,EAAE,EAAE;gBAAES,OAAO,EAAE,MAAM;gBAAEC,cAAc,EAAE,eAAe;gBAAEqB,UAAU,EAAE,QAAQ;gBAAE7B,EAAE,EAAE;cAAE,CAAE;cAAAC,QAAA,gBACzF1I,OAAA,CAACV,UAAU;gBAACqJ,OAAO,EAAC,WAAW;gBAACJ,EAAE,EAAE;kBAAEe,UAAU,EAAE;gBAAO,CAAE;gBAAAZ,QAAA,EACxD3F;cAAY;gBAAA6F,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,EAEZW,IAAI,CAACtI,MAAM,iBACVpB,OAAA,CAACN,IAAI;gBACH6K,KAAK,EAAEb,IAAI,CAACtI,MAAO;gBACnBoJ,IAAI,EAAC,OAAO;gBACZjB,KAAK,EAAEQ,SAAU;gBACjBxB,EAAE,EAAE;kBACFe,UAAU,EAAE,MAAM;kBAClBmB,aAAa,EAAE,WAAW;kBAC1BC,QAAQ,EAAE;gBACZ;cAAE;gBAAA9B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CACF;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,EAELW,IAAI,CAACxI,QAAQ,iBACZlB,OAAA,CAACd,GAAG;cAACqJ,EAAE,EAAE;gBAAEE,EAAE,EAAE;cAAE,CAAE;cAAAC,QAAA,gBACjB1I,OAAA,CAACV,UAAU;gBAACqJ,OAAO,EAAC,OAAO;gBAACJ,EAAE,EAAE;kBAAEe,UAAU,EAAE,MAAM;kBAAEN,OAAO,EAAE;gBAAS,CAAE;gBAAAN,QAAA,EAAC;cAE3E;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACb/I,OAAA,CAACV,UAAU;gBAACqJ,OAAO,EAAC,OAAO;gBAACJ,EAAE,EAAE;kBAAES,OAAO,EAAE;gBAAS,CAAE;gBAAAN,QAAA,EACnDgB,IAAI,CAACxI;cAAQ;gBAAA0H,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CACN,EAEAW,IAAI,CAACvI,KAAK,iBACTnB,OAAA,CAACd,GAAG;cAACqJ,EAAE,EAAE;gBAAEE,EAAE,EAAE;cAAE,CAAE;cAAAC,QAAA,gBACjB1I,OAAA,CAACV,UAAU;gBAACqJ,OAAO,EAAC,OAAO;gBAACJ,EAAE,EAAE;kBAAEe,UAAU,EAAE,MAAM;kBAAEN,OAAO,EAAE;gBAAS,CAAE;gBAAAN,QAAA,EAAC;cAE3E;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACb/I,OAAA,CAACV,UAAU;gBAACqJ,OAAO,EAAC,OAAO;gBAACJ,EAAE,EAAE;kBAAES,OAAO,EAAE,QAAQ;kBAAE2B,UAAU,EAAE;gBAAY,CAAE;gBAAAjC,QAAA,EAC5EgB,IAAI,CAACvI;cAAK;gBAAAyH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACD,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CACN,EAEAW,IAAI,CAACrI,WAAW,iBACfrB,OAAA,CAACd,GAAG;cAACqJ,EAAE,EAAE;gBAAEE,EAAE,EAAE;cAAE,CAAE;cAAAC,QAAA,eACjB1I,OAAA,CAACV,UAAU;gBAACqJ,OAAO,EAAC,OAAO;gBAACY,KAAK,EAAC,gBAAgB;gBAAAb,QAAA,EAC/CgB,IAAI,CAACrI;cAAW;gBAAAuH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACP;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CACN,eAED/I,OAAA,CAACL,OAAO;cAAC4I,EAAE,EAAE;gBAAEW,EAAE,EAAE;cAAE;YAAE;cAAAN,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAE1B/I,OAAA,CAACd,GAAG;cAACqJ,EAAE,EAAE;gBAAES,OAAO,EAAE,MAAM;gBAAE4B,QAAQ,EAAE,MAAM;gBAAEvB,GAAG,EAAE;cAAE,CAAE;cAAAX,QAAA,EAEpDtC,MAAM,CAACC,OAAO,CAACpG,YAAY,CAAC,CAACmH,MAAM,CAAC,CAAC,CAACJ,MAAM,CAAC,KAC1CnE,6BAA6B,CAACC,UAAU,EAAEC,YAAY,CAAC,CAACM,QAAQ,CAAC2D,MAAM,CAAC,CAAC,CAACQ,GAAG,CAAC,CAAC,CAACR,MAAM,EAAEuD,KAAK,CAAC;gBAAA,IAAAM,qBAAA;gBAAA,oBAC9F7K,OAAA,CAACX,gBAAgB;kBAEfyL,OAAO,eACL9K,OAAA,CAACZ,QAAQ;oBACP2L,OAAO,EAAE,EAAAF,qBAAA,GAAAxG,WAAW,CAACvB,UAAU,CAAC,cAAA+H,qBAAA,uBAAvBA,qBAAA,CAAyBxH,QAAQ,CAAC2D,MAAM,CAAC,KAAI,KAAM;oBAC5DgE,QAAQ,EAAEA,CAAA,KAAMlE,YAAY,CAAChE,UAAU,EAAEkE,MAAM,CAAE;oBACjDiE,QAAQ,EAAElH;kBAAO;oBAAA6E,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAClB,CACF;kBACDwB,KAAK,EAAEA;gBAAM,GARRvD,MAAM;kBAAA4B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OASZ,CAAC;cAAA,CACL;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,EAGLiB,aAAa,CAACkB,MAAM,GAAG,CAAC,iBACvBlL,OAAA,CAACd,GAAG;cAACqJ,EAAE,EAAE;gBAAE4C,EAAE,EAAE,CAAC;gBAAEC,EAAE,EAAE,CAAC;gBAAEC,UAAU,EAAE,gBAAgB;gBAAEC,EAAE,EAAE;cAAE,CAAE;cAAA5C,QAAA,gBAC7D1I,OAAA,CAACV,UAAU;gBAACqJ,OAAO,EAAC,WAAW;gBAACJ,EAAE,EAAE;kBAAEE,EAAE,EAAE,CAAC;kBAAEa,UAAU,EAAE;gBAAO,CAAE;gBAAAZ,QAAA,EAAC;cAEnE;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,EAEZiB,aAAa,CAACxC,GAAG,CAAC+D,iBAAiB,IAAI;gBAAA,IAAAC,qBAAA;gBACtC,MAAMC,eAAe,GAAG,EAAAD,qBAAA,GAAApF,MAAM,CAACC,OAAO,CAACxG,QAAQ,CAAC,CAACyG,IAAI,CAAC,CAAC,CAACmB,GAAG,EAAEjB,GAAG,CAAC,KAAKA,GAAG,KAAK+E,iBAAiB,CAAC,cAAAC,qBAAA,uBAAxEA,qBAAA,CAA2E,CAAC,CAAC,KAAID,iBAAiB,CAAC9E,WAAW,CAAC,CAAC,CAACC,OAAO,CAAC,MAAM,EAAE,EAAE,CAAC;gBAC5J,MAAMuD,SAAS,GAAGjJ,QAAQ,CAACuK,iBAAiB,CAAC,IAAI,CAAC,CAAC;gBAEnD,oBACEvL,OAAA,CAACd,GAAG;kBAAuBqJ,EAAE,EAAE;oBAC7BE,EAAE,EAAE,CAAC;oBACLD,CAAC,EAAE,CAAC;oBACJ2B,MAAM,EAAE,gBAAgB;oBACxBC,YAAY,EAAE,CAAC;oBACfC,eAAe,EAAE;kBACnB,CAAE;kBAAA3B,QAAA,gBACA1I,OAAA,CAACd,GAAG;oBAACqJ,EAAE,EAAE;sBAAES,OAAO,EAAE,MAAM;sBAAEC,cAAc,EAAE,eAAe;sBAAEqB,UAAU,EAAE,QAAQ;sBAAE7B,EAAE,EAAE;oBAAE,CAAE;oBAAAC,QAAA,gBACzF1I,OAAA,CAACV,UAAU;sBAACqJ,OAAO,EAAC,OAAO;sBAACJ,EAAE,EAAE;wBAAEe,UAAU,EAAE;sBAAO,CAAE;sBAAAZ,QAAA,EACpD6C;oBAAiB;sBAAA3C,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACR,CAAC,EAEZkB,SAAS,CAAC7I,MAAM,iBACfpB,OAAA,CAACN,IAAI;sBACH6K,KAAK,EAAEN,SAAS,CAAC7I,MAAO;sBACxBoJ,IAAI,EAAC,OAAO;sBACZjB,KAAK,EAAEQ,SAAU;sBACjBxB,EAAE,EAAE;wBACFe,UAAU,EAAE,MAAM;wBAClBmB,aAAa,EAAE,WAAW;wBAC1BC,QAAQ,EAAE;sBACZ;oBAAE;sBAAA9B,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CACF;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACE,CAAC,EAELkB,SAAS,CAAC9I,KAAK,iBACdnB,OAAA,CAACd,GAAG;oBAACqJ,EAAE,EAAE;sBAAEE,EAAE,EAAE;oBAAE,CAAE;oBAAAC,QAAA,gBACjB1I,OAAA,CAACV,UAAU;sBAACqJ,OAAO,EAAC,OAAO;sBAACJ,EAAE,EAAE;wBAAEe,UAAU,EAAE,MAAM;wBAAEN,OAAO,EAAE;sBAAS,CAAE;sBAAAN,QAAA,EAAC;oBAE3E;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAY,CAAC,eACb/I,OAAA,CAACV,UAAU;sBAACqJ,OAAO,EAAC,OAAO;sBAACJ,EAAE,EAAE;wBAAES,OAAO,EAAE,QAAQ;wBAAE2B,UAAU,EAAE;sBAAY,CAAE;sBAAAjC,QAAA,EAC5EuB,SAAS,CAAC9I;oBAAK;sBAAAyH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACN,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACV,CACN,eAED/I,OAAA,CAACd,GAAG;oBAACqJ,EAAE,EAAE;sBAAES,OAAO,EAAE,MAAM;sBAAE4B,QAAQ,EAAE,MAAM;sBAAEvB,GAAG,EAAE,CAAC;sBAAE8B,EAAE,EAAE;oBAAE,CAAE;oBAAAzC,QAAA,EAE3DtC,MAAM,CAACC,OAAO,CAACpG,YAAY,CAAC,CAACmH,MAAM,CAAC,CAAC,CAACJ,MAAM,CAAC,KAC1CnE,6BAA6B,CAAC4I,eAAe,EAAEF,iBAAiB,CAAC,CAAClI,QAAQ,CAAC2D,MAAM,CAAC,CAAC,CAACQ,GAAG,CAAC,CAAC,CAACR,MAAM,EAAEuD,KAAK,CAAC;sBAAA,IAAAmB,qBAAA;sBAAA,oBACxG1L,OAAA,CAACX,gBAAgB;wBAEfyL,OAAO,eACL9K,OAAA,CAACZ,QAAQ;0BACP2L,OAAO,EAAE,EAAAW,qBAAA,GAAArH,WAAW,CAACoH,eAAe,CAAC,cAAAC,qBAAA,uBAA5BA,qBAAA,CAA8BrI,QAAQ,CAAC2D,MAAM,CAAC,KAAI,KAAM;0BACjEgE,QAAQ,EAAEA,CAAA,KAAMlE,YAAY,CAAC2E,eAAe,EAAEzE,MAAM,CAAE;0BACtDiE,QAAQ,EAAElH,MAAO;0BACjByG,IAAI,EAAC;wBAAO;0BAAA5B,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACb,CACF;wBACDwB,KAAK,EAAEA;sBAAM,GATRvD,MAAM;wBAAA4B,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAUZ,CAAC;oBAAA,CACL;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACC,CAAC;gBAAA,GAtDE0C,eAAe;kBAAA7C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAuDpB,CAAC;cAEV,CAAC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CACN;UAAA,GAnJOjG,UAAU;YAAA8F,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAoJf,CAAC;QAEV,CAAC,CAAC;MAAA,GA/LMS,WAAW;QAAAZ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAgMhB,CACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAGN/I,OAAA,CAACd,GAAG;MAACqJ,EAAE,EAAE;QAAE4C,EAAE,EAAE,CAAC;QAAEnC,OAAO,EAAE,MAAM;QAAEC,cAAc,EAAE;MAAW,CAAE;MAAAP,QAAA,eAC9D1I,OAAA,CAACT,MAAM;QACLoJ,OAAO,EAAC,WAAW;QACnBY,KAAK,EAAC,SAAS;QACfoC,OAAO,EAAEpE,UAAW;QACpB0D,QAAQ,EAAElH,MAAM,IAAIF,OAAQ;QAAA6E,QAAA,EAE3B3E,MAAM,GAAG,WAAW,GAAG;MAAkB;QAAA6E,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACpC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACF,CAAC;AAEX;;AAEA;AAAAtF,EAAA,CAjjBwBF,UAAU;EAAA,QAEF3D,SAAS;AAAA;AAAAgM,EAAA,GAFjBrI,UAAU;AAkjBlCA,UAAU,CAACsI,SAAS,GAAG;EACrBrI,IAAI,EAAEvE,SAAS,CAAC6M,KAAK,CAAC;IACpBlI,GAAG,EAAE3E,SAAS,CAAC8M,MAAM;IACrBC,IAAI,EAAE/M,SAAS,CAAC8M,MAAM;IACtBE,KAAK,EAAEhN,SAAS,CAAC8M,MAAM;IACvB1H,WAAW,EAAEpF,SAAS,CAACiN;EACzB,CAAC,CAAC;EACFC,IAAI,EAAElN,SAAS,CAACmN,MAAM;EACtBC,OAAO,EAAEpN,SAAS,CAACqN;AACrB,CAAC;;AAED;AACA/I,UAAU,CAACgJ,YAAY,GAAG;EACxB/I,IAAI,EAAE;AACR,CAAC;AAAC,IAAAoI,EAAA;AAAAY,YAAA,CAAAZ,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}