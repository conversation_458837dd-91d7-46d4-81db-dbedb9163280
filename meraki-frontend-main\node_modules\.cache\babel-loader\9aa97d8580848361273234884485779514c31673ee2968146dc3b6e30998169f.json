{"ast": null, "code": "var _jsxFileName = \"E:\\\\Suraj Patil\\\\Organization_Management_System\\\\meraki-frontend-main\\\\src\\\\screens\\\\ActivityTimeline\\\\WorkSchedule\\\\Components\\\\WorkScheduleForm.jsx\",\n  _s = $RefreshSig$();\nimport React, { useEffect } from 'react';\nimport { Dialog, DialogTitle, DialogContent, DialogActions, Button, Typography, Box, MenuItem, FormControl, InputLabel, Select, Checkbox, FormControlLabel, Grid } from '@mui/material';\nimport { useFormik } from 'formik';\nimport { useDispatch, useSelector } from 'react-redux';\nimport { toast } from 'react-toastify';\nimport PropTypes from 'prop-types';\nimport dayjs from 'dayjs';\nimport Input from 'components/Input';\nimport SelectField from 'components/SelectField';\nimport { UserActions, GeneralActions } from 'slices/actions';\nimport { GeneralSelector, UserSelector } from 'selectors';\nimport { SCHEDULE_TEMPLATES, DEFAULT_WORK_SCHEDULE, TIME_OPTIONS } from 'constants/workSchedule';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst WorkScheduleForm = ({\n  open,\n  onClose,\n  selectedUser,\n  selectedDate\n}) => {\n  _s();\n  const dispatch = useDispatch();\n  const users = useSelector(UserSelector.getUsers());\n  const success = useSelector(GeneralSelector.success(UserActions.updateUser.type));\n  useEffect(() => {\n    if (success) {\n      toast.success('Work schedule updated successfully!', {\n        position: \"top-right\",\n        autoClose: 3000,\n        closeOnClick: true\n      });\n      onClose();\n      dispatch(GeneralActions.removeSuccess([UserActions.updateUser.type]));\n    }\n  }, [success, onClose, dispatch]);\n  const formik = useFormik({\n    initialValues: {\n      selectedUser: (selectedUser === null || selectedUser === void 0 ? void 0 : selectedUser._id) || '',\n      scheduleTemplate: DEFAULT_WORK_SCHEDULE.scheduleTemplate,\n      shiftStart: selectedDate || dayjs().format('YYYY-MM-DD'),\n      shiftEnd: selectedDate || dayjs().format('YYYY-MM-DD'),\n      startTime: DEFAULT_WORK_SCHEDULE.startTime,\n      endTime: DEFAULT_WORK_SCHEDULE.endTime,\n      minimumHours: DEFAULT_WORK_SCHEDULE.minimumHours,\n      saveAsTemplate: false,\n      templateName: '',\n      repeatShift: false,\n      daysToRepeat: [],\n      repeatEndDate: dayjs().add(7, 'days').format('YYYY-MM-DD')\n    },\n    onSubmit: values => handleSubmit(values)\n  });\n  const handleSubmit = values => {\n    const targetUser = users.find(user => user._id === values.selectedUser);\n    if (!targetUser) {\n      toast.error('Please select a user');\n      return;\n    }\n\n    // Calculate hours\n    const calculatedHours = calculateHours(values.startTime, values.endTime);\n\n    // Create work schedule object\n    const workSchedule = {\n      scheduleTemplate: values.scheduleTemplate,\n      shiftStart: new Date(values.shiftStart),\n      shiftEnd: new Date(values.shiftEnd),\n      startTime: values.startTime,\n      endTime: values.endTime,\n      minimumHours: parseFloat(values.minimumHours) || calculatedHours\n    };\n    const params = {\n      id: targetUser._id,\n      workSchedule: workSchedule\n    };\n    dispatch(UserActions.updateUser(params));\n  };\n  const calculateHours = (startTime, endTime) => {\n    const [startHour, startMin] = startTime.split(':').map(Number);\n    const [endHour, endMin] = endTime.split(':').map(Number);\n    let startMinutes = startHour * 60 + startMin;\n    let endMinutes = endHour * 60 + endMin;\n\n    // Handle overnight shifts\n    if (endMinutes <= startMinutes) {\n      endMinutes += 24 * 60;\n    }\n    const diffMinutes = endMinutes - startMinutes;\n    return (diffMinutes / 60).toFixed(2);\n  };\n  const handleFieldChange = (field, value) => {\n    formik.setFieldValue(field, value);\n\n    // Auto-calculate hours when time changes\n    if (field === 'startTime' || field === 'endTime') {\n      const startTime = field === 'startTime' ? value : formik.values.startTime;\n      const endTime = field === 'endTime' ? value : formik.values.endTime;\n      if (startTime && endTime) {\n        const calculatedHours = calculateHours(startTime, endTime);\n        formik.setFieldValue('minimumHours', calculatedHours);\n      }\n    }\n\n    // Auto-detect night shift\n    if (field === 'startTime') {\n      const hour = parseInt(value.split(':')[0], 10);\n      if (hour >= 22 || hour < 6) {\n        formik.setFieldValue('scheduleTemplate', 'night_shift');\n      } else {\n        formik.setFieldValue('scheduleTemplate', 'day_shift');\n      }\n    }\n  };\n  const daysOfWeek = [{\n    value: 1,\n    label: 'MO'\n  }, {\n    value: 2,\n    label: 'TU'\n  }, {\n    value: 3,\n    label: 'WE'\n  }, {\n    value: 4,\n    label: 'TH'\n  }, {\n    value: 5,\n    label: 'FR'\n  }, {\n    value: 6,\n    label: 'SA'\n  }, {\n    value: 0,\n    label: 'SU'\n  }];\n  return /*#__PURE__*/_jsxDEV(Dialog, {\n    open: open,\n    onClose: onClose,\n    maxWidth: \"md\",\n    fullWidth: true,\n    children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n      children: /*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h6\",\n        children: \"Add Schedule\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 146,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 145,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n      children: /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: 'flex',\n          flexDirection: 'column',\n          gap: 2,\n          mt: 2\n        },\n        children: [/*#__PURE__*/_jsxDEV(SelectField, {\n          label: \"Select Teams\",\n          name: \"selectedUser\",\n          value: formik.values.selectedUser,\n          onChange: e => handleFieldChange('selectedUser', e.target.value),\n          required: true,\n          children: [/*#__PURE__*/_jsxDEV(MenuItem, {\n            value: \"\",\n            children: \"Select teams\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 159,\n            columnNumber: 13\n          }, this), users.map(user => /*#__PURE__*/_jsxDEV(MenuItem, {\n            value: user._id,\n            children: user.name || 'Unknown User'\n          }, user._id, false, {\n            fileName: _jsxFileName,\n            lineNumber: 161,\n            columnNumber: 15\n          }, this))]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 152,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(SelectField, {\n          label: \"Members\",\n          name: \"members\",\n          value: \"\",\n          disabled: true,\n          children: /*#__PURE__*/_jsxDEV(MenuItem, {\n            value: \"\",\n            children: \"Members\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 174,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 168,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(SelectField, {\n          label: \"Schedule Template\",\n          name: \"scheduleTemplate\",\n          value: formik.values.scheduleTemplate,\n          onChange: e => handleFieldChange('scheduleTemplate', e.target.value),\n          required: true,\n          children: SCHEDULE_TEMPLATES.map(template => /*#__PURE__*/_jsxDEV(MenuItem, {\n            value: template.value,\n            children: template.label\n          }, template.value, false, {\n            fileName: _jsxFileName,\n            lineNumber: 186,\n            columnNumber: 15\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 178,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          container: true,\n          spacing: 2,\n          children: [/*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 6,\n            children: /*#__PURE__*/_jsxDEV(Input, {\n              label: \"Shift Starts\",\n              name: \"shiftStart\",\n              type: \"date\",\n              value: formik.values.shiftStart,\n              onChange: e => handleFieldChange('shiftStart', e.target.value),\n              required: true,\n              InputLabelProps: {\n                shrink: true\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 195,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 194,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 6,\n            children: /*#__PURE__*/_jsxDEV(Input, {\n              label: \"Shift Ends\",\n              name: \"shiftEnd\",\n              type: \"date\",\n              value: formik.values.shiftEnd,\n              onChange: e => handleFieldChange('shiftEnd', e.target.value),\n              required: true,\n              InputLabelProps: {\n                shrink: true\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 206,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 205,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 193,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          container: true,\n          spacing: 2,\n          children: [/*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 6,\n            children: /*#__PURE__*/_jsxDEV(SelectField, {\n              label: \"Start Time\",\n              name: \"startTime\",\n              value: formik.values.startTime,\n              onChange: e => handleFieldChange('startTime', e.target.value),\n              required: true,\n              children: TIME_OPTIONS.map(option => /*#__PURE__*/_jsxDEV(MenuItem, {\n                value: option.value,\n                children: option.label\n              }, option.value, false, {\n                fileName: _jsxFileName,\n                lineNumber: 229,\n                columnNumber: 19\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 221,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 220,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 6,\n            children: /*#__PURE__*/_jsxDEV(SelectField, {\n              label: \"End Time\",\n              name: \"endTime\",\n              value: formik.values.endTime,\n              onChange: e => handleFieldChange('endTime', e.target.value),\n              required: true,\n              children: TIME_OPTIONS.map(option => /*#__PURE__*/_jsxDEV(MenuItem, {\n                value: option.value,\n                children: option.label\n              }, option.value, false, {\n                fileName: _jsxFileName,\n                lineNumber: 244,\n                columnNumber: 19\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 236,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 235,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 219,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Input, {\n          label: \"Minimum Hours\",\n          name: \"minimumHours\",\n          type: \"number\",\n          step: \"0.1\",\n          value: formik.values.minimumHours,\n          onChange: e => handleFieldChange('minimumHours', e.target.value),\n          required: true,\n          helperText: `Calculated: ${calculateHours(formik.values.startTime, formik.values.endTime)} hours`\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 253,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(FormControlLabel, {\n          control: /*#__PURE__*/_jsxDEV(Checkbox, {\n            checked: formik.values.saveAsTemplate,\n            onChange: e => handleFieldChange('saveAsTemplate', e.target.checked)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 267,\n            columnNumber: 15\n          }, this),\n          label: \"Save as template\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 265,\n          columnNumber: 11\n        }, this), formik.values.saveAsTemplate && /*#__PURE__*/_jsxDEV(Input, {\n          label: \"Schedule template name\",\n          name: \"templateName\",\n          value: formik.values.templateName,\n          onChange: e => handleFieldChange('templateName', e.target.value),\n          placeholder: \"Schedule Name\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 276,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(FormControlLabel, {\n          control: /*#__PURE__*/_jsxDEV(Checkbox, {\n            checked: formik.values.repeatShift,\n            onChange: e => handleFieldChange('repeatShift', e.target.checked)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 288,\n            columnNumber: 15\n          }, this),\n          label: \"Repeat Shift\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 286,\n          columnNumber: 11\n        }, this), formik.values.repeatShift && /*#__PURE__*/_jsxDEV(_Fragment, {\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            children: \"Days to repeat\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 298,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              display: 'flex',\n              gap: 1\n            },\n            children: daysOfWeek.map(day => /*#__PURE__*/_jsxDEV(FormControlLabel, {\n              control: /*#__PURE__*/_jsxDEV(Checkbox, {\n                checked: formik.values.daysToRepeat.includes(day.value),\n                onChange: e => {\n                  const newDays = e.target.checked ? [...formik.values.daysToRepeat, day.value] : formik.values.daysToRepeat.filter(d => d !== day.value);\n                  handleFieldChange('daysToRepeat', newDays);\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 304,\n                columnNumber: 23\n              }, this),\n              label: day.label\n            }, day.value, false, {\n              fileName: _jsxFileName,\n              lineNumber: 301,\n              columnNumber: 19\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 299,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Input, {\n            label: \"Repeat end date\",\n            name: \"repeatEndDate\",\n            type: \"date\",\n            value: formik.values.repeatEndDate,\n            onChange: e => handleFieldChange('repeatEndDate', e.target.value),\n            InputLabelProps: {\n              shrink: true\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 319,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 150,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 149,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(DialogActions, {\n      children: [/*#__PURE__*/_jsxDEV(Button, {\n        onClick: onClose,\n        color: \"secondary\",\n        children: \"Close\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 333,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        onClick: formik.handleSubmit,\n        variant: \"contained\",\n        color: \"primary\",\n        children: \"Save\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 336,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 332,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 144,\n    columnNumber: 5\n  }, this);\n};\n_s(WorkScheduleForm, \"WLjMxfTb9kVLDh8hqa+IalOkvWs=\", false, function () {\n  return [useDispatch, useSelector, useSelector, useFormik];\n});\n_c = WorkScheduleForm;\nWorkScheduleForm.propTypes = {\n  open: PropTypes.bool.isRequired,\n  onClose: PropTypes.func.isRequired,\n  selectedUser: PropTypes.object,\n  selectedDate: PropTypes.string\n};\nexport default WorkScheduleForm;\nvar _c;\n$RefreshReg$(_c, \"WorkScheduleForm\");", "map": {"version": 3, "names": ["React", "useEffect", "Dialog", "DialogTitle", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DialogActions", "<PERSON><PERSON>", "Typography", "Box", "MenuItem", "FormControl", "InputLabel", "Select", "Checkbox", "FormControlLabel", "Grid", "useFormik", "useDispatch", "useSelector", "toast", "PropTypes", "dayjs", "Input", "SelectField", "UserActions", "GeneralActions", "GeneralSelector", "UserSelector", "SCHEDULE_TEMPLATES", "DEFAULT_WORK_SCHEDULE", "TIME_OPTIONS", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "WorkScheduleForm", "open", "onClose", "selected<PERSON>ser", "selectedDate", "_s", "dispatch", "users", "getUsers", "success", "updateUser", "type", "position", "autoClose", "closeOnClick", "removeSuccess", "formik", "initialValues", "_id", "scheduleTemplate", "shiftStart", "format", "shiftEnd", "startTime", "endTime", "minimumHours", "saveAsTemplate", "templateName", "repeatShift", "daysToRepeat", "repeatEndDate", "add", "onSubmit", "values", "handleSubmit", "targetUser", "find", "user", "error", "calculatedHours", "calculateHours", "workSchedule", "Date", "parseFloat", "params", "id", "startHour", "startMin", "split", "map", "Number", "endHour", "endMin", "startMinutes", "endMinutes", "diffMinutes", "toFixed", "handleFieldChange", "field", "value", "setFieldValue", "hour", "parseInt", "daysOfWeek", "label", "max<PERSON><PERSON><PERSON>", "fullWidth", "children", "variant", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "sx", "display", "flexDirection", "gap", "mt", "name", "onChange", "e", "target", "required", "disabled", "template", "container", "spacing", "item", "xs", "InputLabelProps", "shrink", "option", "step", "helperText", "control", "checked", "placeholder", "day", "includes", "newDays", "filter", "d", "onClick", "color", "_c", "propTypes", "bool", "isRequired", "func", "object", "string", "$RefreshReg$"], "sources": ["E:/Suraj Patil/Organization_Management_System/meraki-frontend-main/src/screens/ActivityTimeline/WorkSchedule/Components/WorkScheduleForm.jsx"], "sourcesContent": ["import React, { useEffect } from 'react';\r\nimport {\r\n  <PERSON>alog,\r\n  DialogTitle,\r\n  DialogContent,\r\n  DialogActions,\r\n  Button,\r\n  Typography,\r\n  Box,\r\n  MenuItem,\r\n  FormControl,\r\n  InputLabel,\r\n  Select,\r\n  Checkbox,\r\n  FormControlLabel,\r\n  Grid\r\n} from '@mui/material';\r\nimport { useFormik } from 'formik';\r\nimport { useDispatch, useSelector } from 'react-redux';\r\nimport { toast } from 'react-toastify';\r\nimport PropTypes from 'prop-types';\r\nimport dayjs from 'dayjs';\r\nimport Input from 'components/Input';\r\nimport SelectField from 'components/SelectField';\r\nimport { UserActions, GeneralActions } from 'slices/actions';\r\nimport { GeneralSelector, UserSelector } from 'selectors';\r\nimport { SCHEDULE_TEMPLATES, DEFAULT_WORK_SCHEDULE, TIME_OPTIONS } from 'constants/workSchedule';\r\n\r\nconst WorkScheduleForm = ({ open, onClose, selectedUser, selectedDate }) => {\r\n  const dispatch = useDispatch();\r\n  const users = useSelector(UserSelector.getUsers());\r\n  const success = useSelector(GeneralSelector.success(UserActions.updateUser.type));\r\n\r\n  useEffect(() => {\r\n    if (success) {\r\n      toast.success('Work schedule updated successfully!', {\r\n        position: \"top-right\",\r\n        autoClose: 3000,\r\n        closeOnClick: true,\r\n      });\r\n      onClose();\r\n      dispatch(GeneralActions.removeSuccess([UserActions.updateUser.type]));\r\n    }\r\n  }, [success, onClose, dispatch]);\r\n\r\n  const formik = useFormik({\r\n    initialValues: {\r\n      selectedUser: selectedUser?._id || '',\r\n      scheduleTemplate: DEFAULT_WORK_SCHEDULE.scheduleTemplate,\r\n      shiftStart: selectedDate || dayjs().format('YYYY-MM-DD'),\r\n      shiftEnd: selectedDate || dayjs().format('YYYY-MM-DD'),\r\n      startTime: DEFAULT_WORK_SCHEDULE.startTime,\r\n      endTime: DEFAULT_WORK_SCHEDULE.endTime,\r\n      minimumHours: DEFAULT_WORK_SCHEDULE.minimumHours,\r\n      saveAsTemplate: false,\r\n      templateName: '',\r\n      repeatShift: false,\r\n      daysToRepeat: [],\r\n      repeatEndDate: dayjs().add(7, 'days').format('YYYY-MM-DD')\r\n    },\r\n    onSubmit: (values) => handleSubmit(values)\r\n  });\r\n\r\n  const handleSubmit = (values) => {\r\n    const targetUser = users.find(user => user._id === values.selectedUser);\r\n    if (!targetUser) {\r\n      toast.error('Please select a user');\r\n      return;\r\n    }\r\n\r\n    // Calculate hours\r\n    const calculatedHours = calculateHours(values.startTime, values.endTime);\r\n\r\n    // Create work schedule object\r\n    const workSchedule = {\r\n      scheduleTemplate: values.scheduleTemplate,\r\n      shiftStart: new Date(values.shiftStart),\r\n      shiftEnd: new Date(values.shiftEnd),\r\n      startTime: values.startTime,\r\n      endTime: values.endTime,\r\n      minimumHours: parseFloat(values.minimumHours) || calculatedHours\r\n    };\r\n\r\n    const params = {\r\n      id: targetUser._id,\r\n      workSchedule: workSchedule\r\n    };\r\n\r\n    dispatch(UserActions.updateUser(params));\r\n  };\r\n\r\n  const calculateHours = (startTime, endTime) => {\r\n    const [startHour, startMin] = startTime.split(':').map(Number);\r\n    const [endHour, endMin] = endTime.split(':').map(Number);\r\n    \r\n    let startMinutes = (startHour * 60) + startMin;\r\n    let endMinutes = (endHour * 60) + endMin;\r\n    \r\n    // Handle overnight shifts\r\n    if (endMinutes <= startMinutes) {\r\n      endMinutes += (24 * 60);\r\n    }\r\n    \r\n    const diffMinutes = endMinutes - startMinutes;\r\n    return (diffMinutes / 60).toFixed(2);\r\n  };\r\n\r\n  const handleFieldChange = (field, value) => {\r\n    formik.setFieldValue(field, value);\r\n    \r\n    // Auto-calculate hours when time changes\r\n    if (field === 'startTime' || field === 'endTime') {\r\n      const startTime = field === 'startTime' ? value : formik.values.startTime;\r\n      const endTime = field === 'endTime' ? value : formik.values.endTime;\r\n      \r\n      if (startTime && endTime) {\r\n        const calculatedHours = calculateHours(startTime, endTime);\r\n        formik.setFieldValue('minimumHours', calculatedHours);\r\n      }\r\n    }\r\n    \r\n    // Auto-detect night shift\r\n    if (field === 'startTime') {\r\n      const hour = parseInt(value.split(':')[0], 10);\r\n      if (hour >= 22 || hour < 6) {\r\n        formik.setFieldValue('scheduleTemplate', 'night_shift');\r\n      } else {\r\n        formik.setFieldValue('scheduleTemplate', 'day_shift');\r\n      }\r\n    }\r\n  };\r\n\r\n  const daysOfWeek = [\r\n    { value: 1, label: 'MO' },\r\n    { value: 2, label: 'TU' },\r\n    { value: 3, label: 'WE' },\r\n    { value: 4, label: 'TH' },\r\n    { value: 5, label: 'FR' },\r\n    { value: 6, label: 'SA' },\r\n    { value: 0, label: 'SU' }\r\n  ];\r\n\r\n  return (\r\n    <Dialog open={open} onClose={onClose} maxWidth=\"md\" fullWidth>\r\n      <DialogTitle>\r\n        <Typography variant=\"h6\">Add Schedule</Typography>\r\n      </DialogTitle>\r\n      \r\n      <DialogContent>\r\n        <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2, mt: 2 }}>\r\n          {/* User Selection */}\r\n          <SelectField\r\n            label=\"Select Teams\"\r\n            name=\"selectedUser\"\r\n            value={formik.values.selectedUser}\r\n            onChange={(e) => handleFieldChange('selectedUser', e.target.value)}\r\n            required\r\n          >\r\n            <MenuItem value=\"\">Select teams</MenuItem>\r\n            {users.map((user) => (\r\n              <MenuItem key={user._id} value={user._id}>\r\n                {user.name || 'Unknown User'}\r\n              </MenuItem>\r\n            ))}\r\n          </SelectField>\r\n\r\n          {/* Members Selection - Placeholder */}\r\n          <SelectField\r\n            label=\"Members\"\r\n            name=\"members\"\r\n            value=\"\"\r\n            disabled\r\n          >\r\n            <MenuItem value=\"\">Members</MenuItem>\r\n          </SelectField>\r\n\r\n          {/* Schedule Template */}\r\n          <SelectField\r\n            label=\"Schedule Template\"\r\n            name=\"scheduleTemplate\"\r\n            value={formik.values.scheduleTemplate}\r\n            onChange={(e) => handleFieldChange('scheduleTemplate', e.target.value)}\r\n            required\r\n          >\r\n            {SCHEDULE_TEMPLATES.map((template) => (\r\n              <MenuItem key={template.value} value={template.value}>\r\n                {template.label}\r\n              </MenuItem>\r\n            ))}\r\n          </SelectField>\r\n\r\n          {/* Shift Dates */}\r\n          <Grid container spacing={2}>\r\n            <Grid item xs={6}>\r\n              <Input\r\n                label=\"Shift Starts\"\r\n                name=\"shiftStart\"\r\n                type=\"date\"\r\n                value={formik.values.shiftStart}\r\n                onChange={(e) => handleFieldChange('shiftStart', e.target.value)}\r\n                required\r\n                InputLabelProps={{ shrink: true }}\r\n              />\r\n            </Grid>\r\n            <Grid item xs={6}>\r\n              <Input\r\n                label=\"Shift Ends\"\r\n                name=\"shiftEnd\"\r\n                type=\"date\"\r\n                value={formik.values.shiftEnd}\r\n                onChange={(e) => handleFieldChange('shiftEnd', e.target.value)}\r\n                required\r\n                InputLabelProps={{ shrink: true }}\r\n              />\r\n            </Grid>\r\n          </Grid>\r\n\r\n          {/* Time Range */}\r\n          <Grid container spacing={2}>\r\n            <Grid item xs={6}>\r\n              <SelectField\r\n                label=\"Start Time\"\r\n                name=\"startTime\"\r\n                value={formik.values.startTime}\r\n                onChange={(e) => handleFieldChange('startTime', e.target.value)}\r\n                required\r\n              >\r\n                {TIME_OPTIONS.map((option) => (\r\n                  <MenuItem key={option.value} value={option.value}>\r\n                    {option.label}\r\n                  </MenuItem>\r\n                ))}\r\n              </SelectField>\r\n            </Grid>\r\n            <Grid item xs={6}>\r\n              <SelectField\r\n                label=\"End Time\"\r\n                name=\"endTime\"\r\n                value={formik.values.endTime}\r\n                onChange={(e) => handleFieldChange('endTime', e.target.value)}\r\n                required\r\n              >\r\n                {TIME_OPTIONS.map((option) => (\r\n                  <MenuItem key={option.value} value={option.value}>\r\n                    {option.label}\r\n                  </MenuItem>\r\n                ))}\r\n              </SelectField>\r\n            </Grid>\r\n          </Grid>\r\n\r\n          {/* Minimum Hours */}\r\n          <Input\r\n            label=\"Minimum Hours\"\r\n            name=\"minimumHours\"\r\n            type=\"number\"\r\n            step=\"0.1\"\r\n            value={formik.values.minimumHours}\r\n            onChange={(e) => handleFieldChange('minimumHours', e.target.value)}\r\n            required\r\n            helperText={`Calculated: ${calculateHours(formik.values.startTime, formik.values.endTime)} hours`}\r\n          />\r\n\r\n          {/* Save as Template */}\r\n          <FormControlLabel\r\n            control={\r\n              <Checkbox\r\n                checked={formik.values.saveAsTemplate}\r\n                onChange={(e) => handleFieldChange('saveAsTemplate', e.target.checked)}\r\n              />\r\n            }\r\n            label=\"Save as template\"\r\n          />\r\n\r\n          {formik.values.saveAsTemplate && (\r\n            <Input\r\n              label=\"Schedule template name\"\r\n              name=\"templateName\"\r\n              value={formik.values.templateName}\r\n              onChange={(e) => handleFieldChange('templateName', e.target.value)}\r\n              placeholder=\"Schedule Name\"\r\n            />\r\n          )}\r\n\r\n          {/* Repeat Shift */}\r\n          <FormControlLabel\r\n            control={\r\n              <Checkbox\r\n                checked={formik.values.repeatShift}\r\n                onChange={(e) => handleFieldChange('repeatShift', e.target.checked)}\r\n              />\r\n            }\r\n            label=\"Repeat Shift\"\r\n          />\r\n\r\n          {formik.values.repeatShift && (\r\n            <>\r\n              <Typography variant=\"body2\">Days to repeat</Typography>\r\n              <Box sx={{ display: 'flex', gap: 1 }}>\r\n                {daysOfWeek.map((day) => (\r\n                  <FormControlLabel\r\n                    key={day.value}\r\n                    control={\r\n                      <Checkbox\r\n                        checked={formik.values.daysToRepeat.includes(day.value)}\r\n                        onChange={(e) => {\r\n                          const newDays = e.target.checked\r\n                            ? [...formik.values.daysToRepeat, day.value]\r\n                            : formik.values.daysToRepeat.filter(d => d !== day.value);\r\n                          handleFieldChange('daysToRepeat', newDays);\r\n                        }}\r\n                      />\r\n                    }\r\n                    label={day.label}\r\n                  />\r\n                ))}\r\n              </Box>\r\n\r\n              <Input\r\n                label=\"Repeat end date\"\r\n                name=\"repeatEndDate\"\r\n                type=\"date\"\r\n                value={formik.values.repeatEndDate}\r\n                onChange={(e) => handleFieldChange('repeatEndDate', e.target.value)}\r\n                InputLabelProps={{ shrink: true }}\r\n              />\r\n            </>\r\n          )}\r\n        </Box>\r\n      </DialogContent>\r\n\r\n      <DialogActions>\r\n        <Button onClick={onClose} color=\"secondary\">\r\n          Close\r\n        </Button>\r\n        <Button \r\n          onClick={formik.handleSubmit} \r\n          variant=\"contained\" \r\n          color=\"primary\"\r\n        >\r\n          Save\r\n        </Button>\r\n      </DialogActions>\r\n    </Dialog>\r\n  );\r\n};\r\n\r\nWorkScheduleForm.propTypes = {\r\n  open: PropTypes.bool.isRequired,\r\n  onClose: PropTypes.func.isRequired,\r\n  selectedUser: PropTypes.object,\r\n  selectedDate: PropTypes.string\r\n};\r\n\r\nexport default WorkScheduleForm;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,QAAQ,OAAO;AACxC,SACEC,MAAM,EACNC,WAAW,EACXC,aAAa,EACbC,aAAa,EACbC,MAAM,EACNC,UAAU,EACVC,GAAG,EACHC,QAAQ,EACRC,WAAW,EACXC,UAAU,EACVC,MAAM,EACNC,QAAQ,EACRC,gBAAgB,EAChBC,IAAI,QACC,eAAe;AACtB,SAASC,SAAS,QAAQ,QAAQ;AAClC,SAASC,WAAW,EAAEC,WAAW,QAAQ,aAAa;AACtD,SAASC,KAAK,QAAQ,gBAAgB;AACtC,OAAOC,SAAS,MAAM,YAAY;AAClC,OAAOC,KAAK,MAAM,OAAO;AACzB,OAAOC,KAAK,MAAM,kBAAkB;AACpC,OAAOC,WAAW,MAAM,wBAAwB;AAChD,SAASC,WAAW,EAAEC,cAAc,QAAQ,gBAAgB;AAC5D,SAASC,eAAe,EAAEC,YAAY,QAAQ,WAAW;AACzD,SAASC,kBAAkB,EAAEC,qBAAqB,EAAEC,YAAY,QAAQ,wBAAwB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAEjG,MAAMC,gBAAgB,GAAGA,CAAC;EAAEC,IAAI;EAAEC,OAAO;EAAEC,YAAY;EAAEC;AAAa,CAAC,KAAK;EAAAC,EAAA;EAC1E,MAAMC,QAAQ,GAAGxB,WAAW,CAAC,CAAC;EAC9B,MAAMyB,KAAK,GAAGxB,WAAW,CAACS,YAAY,CAACgB,QAAQ,CAAC,CAAC,CAAC;EAClD,MAAMC,OAAO,GAAG1B,WAAW,CAACQ,eAAe,CAACkB,OAAO,CAACpB,WAAW,CAACqB,UAAU,CAACC,IAAI,CAAC,CAAC;EAEjF7C,SAAS,CAAC,MAAM;IACd,IAAI2C,OAAO,EAAE;MACXzB,KAAK,CAACyB,OAAO,CAAC,qCAAqC,EAAE;QACnDG,QAAQ,EAAE,WAAW;QACrBC,SAAS,EAAE,IAAI;QACfC,YAAY,EAAE;MAChB,CAAC,CAAC;MACFZ,OAAO,CAAC,CAAC;MACTI,QAAQ,CAAChB,cAAc,CAACyB,aAAa,CAAC,CAAC1B,WAAW,CAACqB,UAAU,CAACC,IAAI,CAAC,CAAC,CAAC;IACvE;EACF,CAAC,EAAE,CAACF,OAAO,EAAEP,OAAO,EAAEI,QAAQ,CAAC,CAAC;EAEhC,MAAMU,MAAM,GAAGnC,SAAS,CAAC;IACvBoC,aAAa,EAAE;MACbd,YAAY,EAAE,CAAAA,YAAY,aAAZA,YAAY,uBAAZA,YAAY,CAAEe,GAAG,KAAI,EAAE;MACrCC,gBAAgB,EAAEzB,qBAAqB,CAACyB,gBAAgB;MACxDC,UAAU,EAAEhB,YAAY,IAAIlB,KAAK,CAAC,CAAC,CAACmC,MAAM,CAAC,YAAY,CAAC;MACxDC,QAAQ,EAAElB,YAAY,IAAIlB,KAAK,CAAC,CAAC,CAACmC,MAAM,CAAC,YAAY,CAAC;MACtDE,SAAS,EAAE7B,qBAAqB,CAAC6B,SAAS;MAC1CC,OAAO,EAAE9B,qBAAqB,CAAC8B,OAAO;MACtCC,YAAY,EAAE/B,qBAAqB,CAAC+B,YAAY;MAChDC,cAAc,EAAE,KAAK;MACrBC,YAAY,EAAE,EAAE;MAChBC,WAAW,EAAE,KAAK;MAClBC,YAAY,EAAE,EAAE;MAChBC,aAAa,EAAE5C,KAAK,CAAC,CAAC,CAAC6C,GAAG,CAAC,CAAC,EAAE,MAAM,CAAC,CAACV,MAAM,CAAC,YAAY;IAC3D,CAAC;IACDW,QAAQ,EAAGC,MAAM,IAAKC,YAAY,CAACD,MAAM;EAC3C,CAAC,CAAC;EAEF,MAAMC,YAAY,GAAID,MAAM,IAAK;IAC/B,MAAME,UAAU,GAAG5B,KAAK,CAAC6B,IAAI,CAACC,IAAI,IAAIA,IAAI,CAACnB,GAAG,KAAKe,MAAM,CAAC9B,YAAY,CAAC;IACvE,IAAI,CAACgC,UAAU,EAAE;MACfnD,KAAK,CAACsD,KAAK,CAAC,sBAAsB,CAAC;MACnC;IACF;;IAEA;IACA,MAAMC,eAAe,GAAGC,cAAc,CAACP,MAAM,CAACV,SAAS,EAAEU,MAAM,CAACT,OAAO,CAAC;;IAExE;IACA,MAAMiB,YAAY,GAAG;MACnBtB,gBAAgB,EAAEc,MAAM,CAACd,gBAAgB;MACzCC,UAAU,EAAE,IAAIsB,IAAI,CAACT,MAAM,CAACb,UAAU,CAAC;MACvCE,QAAQ,EAAE,IAAIoB,IAAI,CAACT,MAAM,CAACX,QAAQ,CAAC;MACnCC,SAAS,EAAEU,MAAM,CAACV,SAAS;MAC3BC,OAAO,EAAES,MAAM,CAACT,OAAO;MACvBC,YAAY,EAAEkB,UAAU,CAACV,MAAM,CAACR,YAAY,CAAC,IAAIc;IACnD,CAAC;IAED,MAAMK,MAAM,GAAG;MACbC,EAAE,EAAEV,UAAU,CAACjB,GAAG;MAClBuB,YAAY,EAAEA;IAChB,CAAC;IAEDnC,QAAQ,CAACjB,WAAW,CAACqB,UAAU,CAACkC,MAAM,CAAC,CAAC;EAC1C,CAAC;EAED,MAAMJ,cAAc,GAAGA,CAACjB,SAAS,EAAEC,OAAO,KAAK;IAC7C,MAAM,CAACsB,SAAS,EAAEC,QAAQ,CAAC,GAAGxB,SAAS,CAACyB,KAAK,CAAC,GAAG,CAAC,CAACC,GAAG,CAACC,MAAM,CAAC;IAC9D,MAAM,CAACC,OAAO,EAAEC,MAAM,CAAC,GAAG5B,OAAO,CAACwB,KAAK,CAAC,GAAG,CAAC,CAACC,GAAG,CAACC,MAAM,CAAC;IAExD,IAAIG,YAAY,GAAIP,SAAS,GAAG,EAAE,GAAIC,QAAQ;IAC9C,IAAIO,UAAU,GAAIH,OAAO,GAAG,EAAE,GAAIC,MAAM;;IAExC;IACA,IAAIE,UAAU,IAAID,YAAY,EAAE;MAC9BC,UAAU,IAAK,EAAE,GAAG,EAAG;IACzB;IAEA,MAAMC,WAAW,GAAGD,UAAU,GAAGD,YAAY;IAC7C,OAAO,CAACE,WAAW,GAAG,EAAE,EAAEC,OAAO,CAAC,CAAC,CAAC;EACtC,CAAC;EAED,MAAMC,iBAAiB,GAAGA,CAACC,KAAK,EAAEC,KAAK,KAAK;IAC1C3C,MAAM,CAAC4C,aAAa,CAACF,KAAK,EAAEC,KAAK,CAAC;;IAElC;IACA,IAAID,KAAK,KAAK,WAAW,IAAIA,KAAK,KAAK,SAAS,EAAE;MAChD,MAAMnC,SAAS,GAAGmC,KAAK,KAAK,WAAW,GAAGC,KAAK,GAAG3C,MAAM,CAACiB,MAAM,CAACV,SAAS;MACzE,MAAMC,OAAO,GAAGkC,KAAK,KAAK,SAAS,GAAGC,KAAK,GAAG3C,MAAM,CAACiB,MAAM,CAACT,OAAO;MAEnE,IAAID,SAAS,IAAIC,OAAO,EAAE;QACxB,MAAMe,eAAe,GAAGC,cAAc,CAACjB,SAAS,EAAEC,OAAO,CAAC;QAC1DR,MAAM,CAAC4C,aAAa,CAAC,cAAc,EAAErB,eAAe,CAAC;MACvD;IACF;;IAEA;IACA,IAAImB,KAAK,KAAK,WAAW,EAAE;MACzB,MAAMG,IAAI,GAAGC,QAAQ,CAACH,KAAK,CAACX,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC;MAC9C,IAAIa,IAAI,IAAI,EAAE,IAAIA,IAAI,GAAG,CAAC,EAAE;QAC1B7C,MAAM,CAAC4C,aAAa,CAAC,kBAAkB,EAAE,aAAa,CAAC;MACzD,CAAC,MAAM;QACL5C,MAAM,CAAC4C,aAAa,CAAC,kBAAkB,EAAE,WAAW,CAAC;MACvD;IACF;EACF,CAAC;EAED,MAAMG,UAAU,GAAG,CACjB;IAAEJ,KAAK,EAAE,CAAC;IAAEK,KAAK,EAAE;EAAK,CAAC,EACzB;IAAEL,KAAK,EAAE,CAAC;IAAEK,KAAK,EAAE;EAAK,CAAC,EACzB;IAAEL,KAAK,EAAE,CAAC;IAAEK,KAAK,EAAE;EAAK,CAAC,EACzB;IAAEL,KAAK,EAAE,CAAC;IAAEK,KAAK,EAAE;EAAK,CAAC,EACzB;IAAEL,KAAK,EAAE,CAAC;IAAEK,KAAK,EAAE;EAAK,CAAC,EACzB;IAAEL,KAAK,EAAE,CAAC;IAAEK,KAAK,EAAE;EAAK,CAAC,EACzB;IAAEL,KAAK,EAAE,CAAC;IAAEK,KAAK,EAAE;EAAK,CAAC,CAC1B;EAED,oBACEnE,OAAA,CAAC9B,MAAM;IAACkC,IAAI,EAAEA,IAAK;IAACC,OAAO,EAAEA,OAAQ;IAAC+D,QAAQ,EAAC,IAAI;IAACC,SAAS;IAAAC,QAAA,gBAC3DtE,OAAA,CAAC7B,WAAW;MAAAmG,QAAA,eACVtE,OAAA,CAACzB,UAAU;QAACgG,OAAO,EAAC,IAAI;QAAAD,QAAA,EAAC;MAAY;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACvC,CAAC,eAEd3E,OAAA,CAAC5B,aAAa;MAAAkG,QAAA,eACZtE,OAAA,CAACxB,GAAG;QAACoG,EAAE,EAAE;UAAEC,OAAO,EAAE,MAAM;UAAEC,aAAa,EAAE,QAAQ;UAAEC,GAAG,EAAE,CAAC;UAAEC,EAAE,EAAE;QAAE,CAAE;QAAAV,QAAA,gBAEnEtE,OAAA,CAACT,WAAW;UACV4E,KAAK,EAAC,cAAc;UACpBc,IAAI,EAAC,cAAc;UACnBnB,KAAK,EAAE3C,MAAM,CAACiB,MAAM,CAAC9B,YAAa;UAClC4E,QAAQ,EAAGC,CAAC,IAAKvB,iBAAiB,CAAC,cAAc,EAAEuB,CAAC,CAACC,MAAM,CAACtB,KAAK,CAAE;UACnEuB,QAAQ;UAAAf,QAAA,gBAERtE,OAAA,CAACvB,QAAQ;YAACqF,KAAK,EAAC,EAAE;YAAAQ,QAAA,EAAC;UAAY;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAU,CAAC,EACzCjE,KAAK,CAAC0C,GAAG,CAAEZ,IAAI,iBACdxC,OAAA,CAACvB,QAAQ;YAAgBqF,KAAK,EAAEtB,IAAI,CAACnB,GAAI;YAAAiD,QAAA,EACtC9B,IAAI,CAACyC,IAAI,IAAI;UAAc,GADfzC,IAAI,CAACnB,GAAG;YAAAmD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAEb,CACX,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACS,CAAC,eAGd3E,OAAA,CAACT,WAAW;UACV4E,KAAK,EAAC,SAAS;UACfc,IAAI,EAAC,SAAS;UACdnB,KAAK,EAAC,EAAE;UACRwB,QAAQ;UAAAhB,QAAA,eAERtE,OAAA,CAACvB,QAAQ;YAACqF,KAAK,EAAC,EAAE;YAAAQ,QAAA,EAAC;UAAO;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAU;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1B,CAAC,eAGd3E,OAAA,CAACT,WAAW;UACV4E,KAAK,EAAC,mBAAmB;UACzBc,IAAI,EAAC,kBAAkB;UACvBnB,KAAK,EAAE3C,MAAM,CAACiB,MAAM,CAACd,gBAAiB;UACtC4D,QAAQ,EAAGC,CAAC,IAAKvB,iBAAiB,CAAC,kBAAkB,EAAEuB,CAAC,CAACC,MAAM,CAACtB,KAAK,CAAE;UACvEuB,QAAQ;UAAAf,QAAA,EAEP1E,kBAAkB,CAACwD,GAAG,CAAEmC,QAAQ,iBAC/BvF,OAAA,CAACvB,QAAQ;YAAsBqF,KAAK,EAAEyB,QAAQ,CAACzB,KAAM;YAAAQ,QAAA,EAClDiB,QAAQ,CAACpB;UAAK,GADFoB,QAAQ,CAACzB,KAAK;YAAAU,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAEnB,CACX;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACS,CAAC,eAGd3E,OAAA,CAACjB,IAAI;UAACyG,SAAS;UAACC,OAAO,EAAE,CAAE;UAAAnB,QAAA,gBACzBtE,OAAA,CAACjB,IAAI;YAAC2G,IAAI;YAACC,EAAE,EAAE,CAAE;YAAArB,QAAA,eACftE,OAAA,CAACV,KAAK;cACJ6E,KAAK,EAAC,cAAc;cACpBc,IAAI,EAAC,YAAY;cACjBnE,IAAI,EAAC,MAAM;cACXgD,KAAK,EAAE3C,MAAM,CAACiB,MAAM,CAACb,UAAW;cAChC2D,QAAQ,EAAGC,CAAC,IAAKvB,iBAAiB,CAAC,YAAY,EAAEuB,CAAC,CAACC,MAAM,CAACtB,KAAK,CAAE;cACjEuB,QAAQ;cACRO,eAAe,EAAE;gBAAEC,MAAM,EAAE;cAAK;YAAE;cAAArB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eACP3E,OAAA,CAACjB,IAAI;YAAC2G,IAAI;YAACC,EAAE,EAAE,CAAE;YAAArB,QAAA,eACftE,OAAA,CAACV,KAAK;cACJ6E,KAAK,EAAC,YAAY;cAClBc,IAAI,EAAC,UAAU;cACfnE,IAAI,EAAC,MAAM;cACXgD,KAAK,EAAE3C,MAAM,CAACiB,MAAM,CAACX,QAAS;cAC9ByD,QAAQ,EAAGC,CAAC,IAAKvB,iBAAiB,CAAC,UAAU,EAAEuB,CAAC,CAACC,MAAM,CAACtB,KAAK,CAAE;cAC/DuB,QAAQ;cACRO,eAAe,EAAE;gBAAEC,MAAM,EAAE;cAAK;YAAE;cAAArB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGP3E,OAAA,CAACjB,IAAI;UAACyG,SAAS;UAACC,OAAO,EAAE,CAAE;UAAAnB,QAAA,gBACzBtE,OAAA,CAACjB,IAAI;YAAC2G,IAAI;YAACC,EAAE,EAAE,CAAE;YAAArB,QAAA,eACftE,OAAA,CAACT,WAAW;cACV4E,KAAK,EAAC,YAAY;cAClBc,IAAI,EAAC,WAAW;cAChBnB,KAAK,EAAE3C,MAAM,CAACiB,MAAM,CAACV,SAAU;cAC/BwD,QAAQ,EAAGC,CAAC,IAAKvB,iBAAiB,CAAC,WAAW,EAAEuB,CAAC,CAACC,MAAM,CAACtB,KAAK,CAAE;cAChEuB,QAAQ;cAAAf,QAAA,EAEPxE,YAAY,CAACsD,GAAG,CAAE0C,MAAM,iBACvB9F,OAAA,CAACvB,QAAQ;gBAAoBqF,KAAK,EAAEgC,MAAM,CAAChC,KAAM;gBAAAQ,QAAA,EAC9CwB,MAAM,CAAC3B;cAAK,GADA2B,MAAM,CAAChC,KAAK;gBAAAU,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAEjB,CACX;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACS;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC,eACP3E,OAAA,CAACjB,IAAI;YAAC2G,IAAI;YAACC,EAAE,EAAE,CAAE;YAAArB,QAAA,eACftE,OAAA,CAACT,WAAW;cACV4E,KAAK,EAAC,UAAU;cAChBc,IAAI,EAAC,SAAS;cACdnB,KAAK,EAAE3C,MAAM,CAACiB,MAAM,CAACT,OAAQ;cAC7BuD,QAAQ,EAAGC,CAAC,IAAKvB,iBAAiB,CAAC,SAAS,EAAEuB,CAAC,CAACC,MAAM,CAACtB,KAAK,CAAE;cAC9DuB,QAAQ;cAAAf,QAAA,EAEPxE,YAAY,CAACsD,GAAG,CAAE0C,MAAM,iBACvB9F,OAAA,CAACvB,QAAQ;gBAAoBqF,KAAK,EAAEgC,MAAM,CAAChC,KAAM;gBAAAQ,QAAA,EAC9CwB,MAAM,CAAC3B;cAAK,GADA2B,MAAM,CAAChC,KAAK;gBAAAU,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAEjB,CACX;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACS;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGP3E,OAAA,CAACV,KAAK;UACJ6E,KAAK,EAAC,eAAe;UACrBc,IAAI,EAAC,cAAc;UACnBnE,IAAI,EAAC,QAAQ;UACbiF,IAAI,EAAC,KAAK;UACVjC,KAAK,EAAE3C,MAAM,CAACiB,MAAM,CAACR,YAAa;UAClCsD,QAAQ,EAAGC,CAAC,IAAKvB,iBAAiB,CAAC,cAAc,EAAEuB,CAAC,CAACC,MAAM,CAACtB,KAAK,CAAE;UACnEuB,QAAQ;UACRW,UAAU,EAAE,eAAerD,cAAc,CAACxB,MAAM,CAACiB,MAAM,CAACV,SAAS,EAAEP,MAAM,CAACiB,MAAM,CAACT,OAAO,CAAC;QAAS;UAAA6C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnG,CAAC,eAGF3E,OAAA,CAAClB,gBAAgB;UACfmH,OAAO,eACLjG,OAAA,CAACnB,QAAQ;YACPqH,OAAO,EAAE/E,MAAM,CAACiB,MAAM,CAACP,cAAe;YACtCqD,QAAQ,EAAGC,CAAC,IAAKvB,iBAAiB,CAAC,gBAAgB,EAAEuB,CAAC,CAACC,MAAM,CAACc,OAAO;UAAE;YAAA1B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxE,CACF;UACDR,KAAK,EAAC;QAAkB;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzB,CAAC,EAEDxD,MAAM,CAACiB,MAAM,CAACP,cAAc,iBAC3B7B,OAAA,CAACV,KAAK;UACJ6E,KAAK,EAAC,wBAAwB;UAC9Bc,IAAI,EAAC,cAAc;UACnBnB,KAAK,EAAE3C,MAAM,CAACiB,MAAM,CAACN,YAAa;UAClCoD,QAAQ,EAAGC,CAAC,IAAKvB,iBAAiB,CAAC,cAAc,EAAEuB,CAAC,CAACC,MAAM,CAACtB,KAAK,CAAE;UACnEqC,WAAW,EAAC;QAAe;UAAA3B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC5B,CACF,eAGD3E,OAAA,CAAClB,gBAAgB;UACfmH,OAAO,eACLjG,OAAA,CAACnB,QAAQ;YACPqH,OAAO,EAAE/E,MAAM,CAACiB,MAAM,CAACL,WAAY;YACnCmD,QAAQ,EAAGC,CAAC,IAAKvB,iBAAiB,CAAC,aAAa,EAAEuB,CAAC,CAACC,MAAM,CAACc,OAAO;UAAE;YAAA1B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrE,CACF;UACDR,KAAK,EAAC;QAAc;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrB,CAAC,EAEDxD,MAAM,CAACiB,MAAM,CAACL,WAAW,iBACxB/B,OAAA,CAAAE,SAAA;UAAAoE,QAAA,gBACEtE,OAAA,CAACzB,UAAU;YAACgG,OAAO,EAAC,OAAO;YAAAD,QAAA,EAAC;UAAc;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACvD3E,OAAA,CAACxB,GAAG;YAACoG,EAAE,EAAE;cAAEC,OAAO,EAAE,MAAM;cAAEE,GAAG,EAAE;YAAE,CAAE;YAAAT,QAAA,EAClCJ,UAAU,CAACd,GAAG,CAAEgD,GAAG,iBAClBpG,OAAA,CAAClB,gBAAgB;cAEfmH,OAAO,eACLjG,OAAA,CAACnB,QAAQ;gBACPqH,OAAO,EAAE/E,MAAM,CAACiB,MAAM,CAACJ,YAAY,CAACqE,QAAQ,CAACD,GAAG,CAACtC,KAAK,CAAE;gBACxDoB,QAAQ,EAAGC,CAAC,IAAK;kBACf,MAAMmB,OAAO,GAAGnB,CAAC,CAACC,MAAM,CAACc,OAAO,GAC5B,CAAC,GAAG/E,MAAM,CAACiB,MAAM,CAACJ,YAAY,EAAEoE,GAAG,CAACtC,KAAK,CAAC,GAC1C3C,MAAM,CAACiB,MAAM,CAACJ,YAAY,CAACuE,MAAM,CAACC,CAAC,IAAIA,CAAC,KAAKJ,GAAG,CAACtC,KAAK,CAAC;kBAC3DF,iBAAiB,CAAC,cAAc,EAAE0C,OAAO,CAAC;gBAC5C;cAAE;gBAAA9B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CACF;cACDR,KAAK,EAAEiC,GAAG,CAACjC;YAAM,GAZZiC,GAAG,CAACtC,KAAK;cAAAU,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAaf,CACF;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eAEN3E,OAAA,CAACV,KAAK;YACJ6E,KAAK,EAAC,iBAAiB;YACvBc,IAAI,EAAC,eAAe;YACpBnE,IAAI,EAAC,MAAM;YACXgD,KAAK,EAAE3C,MAAM,CAACiB,MAAM,CAACH,aAAc;YACnCiD,QAAQ,EAAGC,CAAC,IAAKvB,iBAAiB,CAAC,eAAe,EAAEuB,CAAC,CAACC,MAAM,CAACtB,KAAK,CAAE;YACpE8B,eAAe,EAAE;cAAEC,MAAM,EAAE;YAAK;UAAE;YAAArB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnC,CAAC;QAAA,eACF,CACH;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACO,CAAC,eAEhB3E,OAAA,CAAC3B,aAAa;MAAAiG,QAAA,gBACZtE,OAAA,CAAC1B,MAAM;QAACmI,OAAO,EAAEpG,OAAQ;QAACqG,KAAK,EAAC,WAAW;QAAApC,QAAA,EAAC;MAE5C;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eACT3E,OAAA,CAAC1B,MAAM;QACLmI,OAAO,EAAEtF,MAAM,CAACkB,YAAa;QAC7BkC,OAAO,EAAC,WAAW;QACnBmC,KAAK,EAAC,SAAS;QAAApC,QAAA,EAChB;MAED;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACI,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACV,CAAC;AAEb,CAAC;AAACnE,EAAA,CA7TIL,gBAAgB;EAAA,QACHlB,WAAW,EACdC,WAAW,EACTA,WAAW,EAcZF,SAAS;AAAA;AAAA2H,EAAA,GAjBpBxG,gBAAgB;AA+TtBA,gBAAgB,CAACyG,SAAS,GAAG;EAC3BxG,IAAI,EAAEhB,SAAS,CAACyH,IAAI,CAACC,UAAU;EAC/BzG,OAAO,EAAEjB,SAAS,CAAC2H,IAAI,CAACD,UAAU;EAClCxG,YAAY,EAAElB,SAAS,CAAC4H,MAAM;EAC9BzG,YAAY,EAAEnB,SAAS,CAAC6H;AAC1B,CAAC;AAED,eAAe9G,gBAAgB;AAAC,IAAAwG,EAAA;AAAAO,YAAA,CAAAP,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}