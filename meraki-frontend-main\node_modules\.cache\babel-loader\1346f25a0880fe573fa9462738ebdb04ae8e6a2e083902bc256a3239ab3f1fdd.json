{"ast": null, "code": "var _jsxFileName = \"E:\\\\Suraj Patil\\\\Organization_Management_System\\\\meraki-frontend-main\\\\src\\\\routes.js\",\n  _s = $RefreshSig$();\nimport React, { useEffect, useState, lazy, Suspense } from \"react\";\nimport { Route, Switch, Redirect } from \"react-router-dom\";\nimport PermissionRoute from \"components/PermissionRoute\";\nimport { useSelector } from \"react-redux\";\nimport { UserSelector } from \"./selectors\";\nimport { features } from \"constants/permission\";\nimport LoadingScreen from \"./components/LoadingScreen\";\n\n// Layouts\nimport MainLayout from \"./layouts/MainLayout\";\nimport AuthLayout from \"./layouts/AuthLayout\";\n\n// Eagerly loaded components (small and frequently used)\nimport NotFound from \"components/NotFound\";\nimport AccessDenied from \"components/AccessDenied\";\n\n// Lazy loaded components\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Login = /*#__PURE__*/lazy(_c = () => import(\"./screens/Auth/Login\"));\n_c2 = Login;\nconst Dashboard = /*#__PURE__*/lazy(_c3 = () => import(\"./screens/Dashboard\"));\n_c4 = Dashboard;\nconst Profile = /*#__PURE__*/lazy(_c5 = () => import(\"./screens/Profile\"));\n\n// User management\n_c6 = Profile;\nconst User = /*#__PURE__*/lazy(_c7 = () => import(\"screens/User\"));\n_c8 = User;\nconst CreateUser = /*#__PURE__*/lazy(_c9 = () => import(\"./screens/User/Create\"));\n_c0 = CreateUser;\nconst FormUser = /*#__PURE__*/lazy(_c1 = () => import(\"./screens/User/Form\"));\n\n// Department\n_c10 = FormUser;\nconst Department = /*#__PURE__*/lazy(_c11 = () => import(\"screens/Department\"));\n_c12 = Department;\nconst FormDepartment = /*#__PURE__*/lazy(_c13 = () => import(\"./screens/Department/Form\"));\n\n// Designation\n_c14 = FormDepartment;\nconst Designation = /*#__PURE__*/lazy(_c15 = () => import(\"./screens/Designation\"));\n_c16 = Designation;\nconst FormDesignation = /*#__PURE__*/lazy(_c17 = () => import(\"./screens/Designation/Form\"));\n\n// Attendance\n_c18 = FormDesignation;\nconst Attendance = /*#__PURE__*/lazy(_c19 = () => import(\"./screens/Attendance\"));\n_c20 = Attendance;\nconst FormAttendance = /*#__PURE__*/lazy(_c21 = () => import(\"./screens/Attendance/Form\"));\n\n// Expenses\n_c22 = FormAttendance;\nconst Expenses = /*#__PURE__*/lazy(_c23 = () => import(\"./screens/Expenses\"));\n_c24 = Expenses;\nconst FormExpenses = /*#__PURE__*/lazy(_c25 = () => import(\"./screens/Expenses/Form\"));\n\n// Reports\n_c26 = FormExpenses;\nconst Report = /*#__PURE__*/lazy(_c27 = () => import(\"./screens/Report\"));\n\n// Leave management\n_c28 = Report;\nconst Leaves = /*#__PURE__*/lazy(_c29 = () => import(\"./screens/Leave\"));\n_c30 = Leaves;\nconst FormLeave = /*#__PURE__*/lazy(_c31 = () => import(\"./screens/Leave/Form\"));\n_c32 = FormLeave;\nconst LeaveReport = /*#__PURE__*/lazy(_c33 = () => import(\"screens/LeaveReport/LeaveReport\"));\n_c34 = LeaveReport;\nconst Approval = /*#__PURE__*/lazy(_c35 = () => import(\"screens/LeaveApproval/Approval\"));\n_c36 = Approval;\nconst LeaveConfiguration = /*#__PURE__*/lazy(_c37 = () => import(\"screens/LeaveConfiguration/LeaveConfiguration\"));\n_c38 = LeaveConfiguration;\nconst LeaveCalendar = /*#__PURE__*/lazy(_c39 = () => import(\"screens/LeaveCalendar/LeaveCalendar\"));\n\n// Settings\n_c40 = LeaveCalendar;\nconst Setting = /*#__PURE__*/lazy(_c41 = () => import(\"./screens/Setting\"));\n\n// Timeline\n_c42 = Setting;\nconst Timeline = /*#__PURE__*/lazy(_c43 = () => import(\"screens/Timeline\"));\n_c44 = Timeline;\nconst Overivew = /*#__PURE__*/lazy(_c45 = () => import(\"screens/ActivityTimeline/Overivew\"));\n_c46 = Overivew;\nconst TimeRequest = /*#__PURE__*/lazy(_c47 = () => import(\"screens/ActivityTimeline/TimeRequest\"));\n_c48 = TimeRequest;\nconst TaskRequest = /*#__PURE__*/lazy(_c49 = () => import(\"./screens/TaskRequest\"));\n_c50 = TaskRequest;\nconst WorkSchedule = /*#__PURE__*/lazy(_c51 = () => import(\"screens/WorkSchedule\"));\n\n// Project management\n_c52 = WorkSchedule;\nconst ProductList = /*#__PURE__*/lazy(_c53 = () => import(\"screens/Product/ProductList\"));\n_c54 = ProductList;\nconst ProductOverview = /*#__PURE__*/lazy(_c55 = () => import(\"screens/Product/ProductOverview\"));\n_c56 = ProductOverview;\nconst ProductTimesheet = /*#__PURE__*/lazy(_c57 = () => import(\"screens/Product/ProductTimesheet\"));\n_c58 = ProductTimesheet;\nconst Client = /*#__PURE__*/lazy(_c59 = () => import(\"screens/Client/Client\"));\n_c60 = Client;\nconst Tasklist = /*#__PURE__*/lazy(_c61 = () => import(\"screens/Product/Tasklist\"));\n_c62 = Tasklist;\nconst ProductListStaff = /*#__PURE__*/lazy(_c63 = () => import(\"screens/Product/Staff/ProductListStaff\"));\n_c64 = ProductListStaff;\nconst TaskHistoryAdmin = /*#__PURE__*/lazy(_c65 = () => import(\"screens/Product/TaskHistoryAdmin\"));\n_c66 = TaskHistoryAdmin;\nconst TaskListWithNote = /*#__PURE__*/lazy(_c67 = () => import(\"screens/Product/Staff/TaskListWithNote\"));\n\n// Sprint management\n_c68 = TaskListWithNote;\nconst SprintPage = /*#__PURE__*/lazy(_c69 = () => import(\"screens/Sprints/pages/SprintPage\"));\n_c70 = SprintPage;\nconst UserSprintPage = /*#__PURE__*/lazy(_c71 = () => import(\"screens/Sprints/pages/UserSprintPage\"));\n_c72 = UserSprintPage;\nconst SprintTasksPage = /*#__PURE__*/lazy(_c73 = () => import(\"screens/Sprints/pages/SprintTasksPage\"));\n_c74 = SprintTasksPage;\nconst PrivateRoutes = [\n// User routes\n{\n  path: \"/app/user\",\n  component: User,\n  permission: {\n    feat: features.user,\n    act: 'read'\n  }\n}, {\n  path: \"/app/user/create\",\n  component: CreateUser,\n  permission: {\n    feat: features.user,\n    act: 'create'\n  }\n}, {\n  path: \"/app/user/update/:id\",\n  component: FormUser,\n  permission: {\n    feat: features.user,\n    act: 'update'\n  }\n},\n// Department routes\n{\n  path: \"/app/department\",\n  component: Department,\n  permission: {\n    feat: features.department,\n    act: 'read'\n  }\n}, {\n  path: \"/app/department/create\",\n  component: FormDepartment,\n  permission: {\n    feat: features.department,\n    act: 'create'\n  }\n}, {\n  path: \"/app/department/update/:id\",\n  component: FormDepartment,\n  permission: {\n    feat: features.department,\n    act: 'update'\n  }\n},\n// Designation routes\n{\n  path: \"/app/designation\",\n  component: Designation,\n  permission: {\n    feat: features.designation,\n    act: 'read'\n  }\n}, {\n  path: \"/app/designation/create\",\n  component: FormDesignation,\n  permission: {\n    feat: features.designation,\n    act: 'create'\n  }\n}, {\n  path: \"/app/designation/update/:id\",\n  component: FormDesignation,\n  permission: {\n    feat: features.designation,\n    act: 'update'\n  }\n},\n// Attendance routes\n{\n  path: \"/app/attendance\",\n  component: Attendance,\n  permission: {\n    feat: \"attendance\",\n    act: \"read\"\n  }\n}, {\n  path: \"/app/attendance/create\",\n  component: FormAttendance,\n  permission: {\n    feat: features.attendance,\n    act: 'create'\n  }\n}, {\n  path: \"/app/attendance/update/:id\",\n  component: FormAttendance,\n  permission: {\n    feat: features.attendance,\n    act: 'update'\n  }\n},\n// Admin Expenses routes\n{\n  path: \"/app/expenses\",\n  component: Expenses,\n  permission: {\n    feat: features.expense,\n    act: 'read'\n  }\n}, {\n  path: \"/app/expenses/create\",\n  component: FormExpenses,\n  permission: {\n    feat: features.expense,\n    act: 'create'\n  }\n}, {\n  path: \"/app/expenses/update/:id\",\n  component: FormExpenses,\n  permission: {\n    feat: features.expense,\n    act: 'update'\n  }\n},\n// Redirect user expense routes to main expense routes\n{\n  path: \"/app/user/expenses\",\n  component: () => /*#__PURE__*/_jsxDEV(Redirect, {\n    to: \"/app/expenses\"\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 168,\n    columnNumber: 26\n  }, this),\n  permission: {\n    feat: \"expense\",\n    act: \"read\"\n  }\n}, {\n  path: \"/app/user/expenses/create\",\n  component: () => /*#__PURE__*/_jsxDEV(Redirect, {\n    to: \"/app/expenses/create\"\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 173,\n    columnNumber: 26\n  }, this),\n  permission: {\n    feat: \"expense\",\n    act: \"create\"\n  }\n}, {\n  path: \"/app/user/expenses/update/:id\",\n  component: props => /*#__PURE__*/_jsxDEV(Redirect, {\n    to: `/app/expenses/update/${props.match.params.id}`\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 178,\n    columnNumber: 31\n  }, this),\n  permission: {\n    feat: \"expense\",\n    act: \"update\"\n  }\n},\n// Leave routes\n{\n  path: \"/app/leave\",\n  component: Leaves,\n  role: \"admin\",\n  permission: {\n    feat: features.leave,\n    act: 'read'\n  }\n}, {\n  path: \"/app/user/leave\",\n  component: Leaves,\n  permission: {\n    feat: \"leave\",\n    act: \"read\"\n  }\n}, {\n  path: \"/app/leave/create\",\n  component: FormLeave,\n  permission: {\n    feat: features.leave,\n    act: 'create'\n  }\n}, {\n  path: \"/app/leave/update/:id\",\n  component: FormLeave,\n  permission: {\n    feat: features.leave,\n    act: 'update'\n  }\n}, {\n  path: \"/app/leave/report\",\n  component: LeaveReport,\n  permission: {\n    feat: features.leavereport,\n    act: 'read'\n  }\n}, {\n  path: \"/app/leave/approval\",\n  component: Approval,\n  permission: {\n    feat: features.approve,\n    act: 'read'\n  }\n}, {\n  path: \"/app/leave/configuration\",\n  component: LeaveConfiguration,\n  permission: {\n    feat: features.configuration,\n    act: 'read'\n  }\n}, {\n  path: \"/app/leave/calendar\",\n  component: LeaveCalendar,\n  permission: {\n    feat: features.calendar,\n    act: 'read'\n  }\n},\n// Project routes\n{\n  path: '/app/project/list',\n  component: ProductList,\n  permission: {\n    feat: features.projectlist,\n    act: 'read'\n  }\n}, {\n  path: '/app/project/overview',\n  component: ProductOverview,\n  permission: {\n    feat: features.projectoverview,\n    act: 'read'\n  }\n}, {\n  path: \"/app/timesheet\",\n  component: Leaves,\n  role: \"admin\",\n  permission: {\n    feat: features.leave,\n    act: 'read'\n  }\n},\n// Timeline routes\n{\n  path: \"/app/timeline/overview\",\n  component: Overivew,\n  permission: {\n    feat: features.overview,\n    act: 'read'\n  }\n}, {\n  path: \"/app/timeline/request\",\n  component: TimeRequest,\n  permission: {\n    feat: features.timerequest,\n    act: 'read'\n  }\n}, {\n  path: \"/app/timeline/taskrequest\",\n  component: TaskRequest,\n  permission: {\n    feat: features.taskrequest,\n    act: 'read'\n  }\n}, {\n  path: \"/app/timeline/workschedule\",\n  component: WorkSchedule,\n  permission: {\n    feat: features.workschedule,\n    act: 'read'\n  }\n},\n// Other routes\n{\n  path: \"/app/report\",\n  component: Report,\n  permission: {\n    feat: features.report,\n    act: 'read'\n  }\n}, {\n  path: \"/app/setting\",\n  component: Setting,\n  permission: {\n    feat: features.setting,\n    act: 'read'\n  }\n},\n// Profile route\n{\n  path: \"/app/profile\",\n  component: Profile,\n  permission: {\n    feat: 'Profile',\n    act: 'read'\n  }\n},\n// Dashboard routes - no permission required (hardcoded access)\n{\n  path: \"/app/dashboard\",\n  component: Dashboard\n}, {\n  path: \"/app/admin-dashboard\",\n  component: Dashboard\n}, {\n  path: \"/app/user-dashboard\",\n  component: Dashboard\n},\n// Timeline routes\n{\n  path: \"/app/timeline\",\n  component: Timeline,\n  permission: {\n    feat: features.timeline,\n    act: 'read'\n  }\n}, {\n  path: \"/app/user/timeline\",\n  component: Timeline,\n  permission: {\n    feat: \"Timeline\",\n    act: \"read\"\n  }\n}, {\n  path: \"/app/project/timesheet\",\n  component: ProductTimesheet,\n  permission: {\n    feat: features.projecttimesheet,\n    act: 'read'\n  }\n},\n// Client and Task routes\n{\n  path: '/app/client',\n  component: Client,\n  permission: {\n    feat: features.client,\n    act: 'read'\n  }\n}, {\n  path: '/app/project/update/:data',\n  component: TaskHistoryAdmin,\n  permission: {\n    feat: features.projects,\n    act: 'update'\n  }\n}, {\n  path: '/app/tasks',\n  component: Tasklist,\n  permission: {\n    feat: \"My Tasks\",\n    act: \"read\"\n  }\n}, {\n  path: '/app/user/projects',\n  component: ProductListStaff,\n  permission: {\n    feat: \"Projects\",\n    act: \"read\"\n  }\n}, {\n  path: '/app/user/project/overview',\n  component: ProductOverview,\n  permission: {\n    feat: \"projectoverview\",\n    act: \"read\"\n  }\n}, {\n  path: '/app/user/project/timesheet',\n  component: ProductTimesheet,\n  permission: {\n    feat: \"projecttimesheet\",\n    act: \"read\"\n  }\n}, {\n  path: '/app/user/tasklist/note/:data',\n  component: TaskListWithNote,\n  permission: {\n    feat: features.tasknote,\n    act: 'read'\n  }\n},\n// Sprint routes\n{\n  path: '/app/sprint',\n  component: SprintPage,\n  permission: {\n    feat: \"Sprint\",\n    act: \"read_all\"\n  }\n}, {\n  path: '/app/sprint/form',\n  component: SprintPage,\n  permission: {\n    feat: \"Sprint\",\n    act: \"create\"\n  }\n}, {\n  path: '/app/sprint/form/:id',\n  component: SprintPage,\n  permission: {\n    feat: \"Sprint\",\n    act: \"update\"\n  }\n}, {\n  path: '/app/sprint/:sprintId/tasks',\n  component: SprintTasksPage,\n  permission: {\n    feat: \"Sprint\",\n    act: \"read_all\"\n  }\n}, {\n  path: '/app/user/sprint',\n  component: UserSprintPage,\n  permission: {\n    feat: \"Sprint\",\n    act: \"read_self\"\n  }\n}, {\n  path: '/app/user/sprint/:sprintId/tasks',\n  component: SprintTasksPage,\n  permission: {\n    feat: \"Sprint\",\n    act: \"read_self\"\n  }\n}];\n\n// Loading component for suspense fallback\nconst Loader = () => /*#__PURE__*/_jsxDEV(LoadingScreen, {}, void 0, false, {\n  fileName: _jsxFileName,\n  lineNumber: 386,\n  columnNumber: 22\n}, this);\n_c75 = Loader;\nexport default function Routes() {\n  _s();\n  const [role, setUserRole] = useState([]);\n  const profile = useSelector(UserSelector.profile());\n  useEffect(() => {\n    if (profile) {\n      setUserRole(profile.role || []);\n    }\n  }, [profile]);\n  return /*#__PURE__*/_jsxDEV(Suspense, {\n    fallback: /*#__PURE__*/_jsxDEV(Loader, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 400,\n      columnNumber: 29\n    }, this),\n    children: /*#__PURE__*/_jsxDEV(Switch, {\n      children: [/*#__PURE__*/_jsxDEV(Route, {\n        path: \"/app\",\n        children: /*#__PURE__*/_jsxDEV(MainLayout, {\n          children: /*#__PURE__*/_jsxDEV(Suspense, {\n            fallback: /*#__PURE__*/_jsxDEV(Loader, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 405,\n              columnNumber: 45\n            }, this),\n            children: /*#__PURE__*/_jsxDEV(Switch, {\n              children: [/*#__PURE__*/_jsxDEV(Route, {\n                exact: true,\n                path: \"/app/dashboard\",\n                component: Dashboard\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 408,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(Route, {\n                exact: true,\n                path: \"/app/profile\",\n                component: Profile\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 411,\n                columnNumber: 33\n              }, this), PrivateRoutes.map((route, i) => {\n                // All routes now use PermissionRoute\n                if (route.permission) {\n                  return /*#__PURE__*/_jsxDEV(PermissionRoute, {\n                    exact: true,\n                    path: route.path,\n                    component: route.component,\n                    permission: route.permission\n                  }, i, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 418,\n                    columnNumber: 45\n                  }, this);\n                }\n\n                // If no permission specified (should be rare), use regular Route\n                return /*#__PURE__*/_jsxDEV(Route, {\n                  exact: true,\n                  path: route.path,\n                  component: route.component\n                }, i, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 430,\n                  columnNumber: 41\n                }, this);\n              }), /*#__PURE__*/_jsxDEV(Route, {\n                component: NotFound\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 440,\n                columnNumber: 33\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 406,\n              columnNumber: 29\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 405,\n            columnNumber: 25\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 404,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 403,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(Route, {\n        exact: true,\n        path: \"/\",\n        children: /*#__PURE__*/_jsxDEV(AuthLayout, {\n          children: /*#__PURE__*/_jsxDEV(Suspense, {\n            fallback: /*#__PURE__*/_jsxDEV(Loader, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 449,\n              columnNumber: 45\n            }, this),\n            children: /*#__PURE__*/_jsxDEV(Switch, {\n              children: /*#__PURE__*/_jsxDEV(Route, {\n                exact: true,\n                path: \"/\",\n                component: Login\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 451,\n                columnNumber: 33\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 450,\n              columnNumber: 29\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 449,\n            columnNumber: 25\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 448,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 447,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(Route, {\n        path: \"/access-denied\",\n        children: /*#__PURE__*/_jsxDEV(MainLayout, {\n          children: /*#__PURE__*/_jsxDEV(AccessDenied, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 460,\n            columnNumber: 25\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 459,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 458,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(Route, {\n        path: \"/not-found\",\n        children: /*#__PURE__*/_jsxDEV(MainLayout, {\n          children: /*#__PURE__*/_jsxDEV(NotFound, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 467,\n            columnNumber: 25\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 466,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 465,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(Route, {\n        children: /*#__PURE__*/_jsxDEV(MainLayout, {\n          children: /*#__PURE__*/_jsxDEV(NotFound, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 474,\n            columnNumber: 25\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 473,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 472,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 401,\n      columnNumber: 13\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 400,\n    columnNumber: 9\n  }, this);\n}\n_s(Routes, \"tjdlTI4VjeqiMbvk0Bny/1HdC3A=\", false, function () {\n  return [useSelector];\n});\n_c76 = Routes;\nvar _c, _c2, _c3, _c4, _c5, _c6, _c7, _c8, _c9, _c0, _c1, _c10, _c11, _c12, _c13, _c14, _c15, _c16, _c17, _c18, _c19, _c20, _c21, _c22, _c23, _c24, _c25, _c26, _c27, _c28, _c29, _c30, _c31, _c32, _c33, _c34, _c35, _c36, _c37, _c38, _c39, _c40, _c41, _c42, _c43, _c44, _c45, _c46, _c47, _c48, _c49, _c50, _c51, _c52, _c53, _c54, _c55, _c56, _c57, _c58, _c59, _c60, _c61, _c62, _c63, _c64, _c65, _c66, _c67, _c68, _c69, _c70, _c71, _c72, _c73, _c74, _c75, _c76;\n$RefreshReg$(_c, \"Login$lazy\");\n$RefreshReg$(_c2, \"Login\");\n$RefreshReg$(_c3, \"Dashboard$lazy\");\n$RefreshReg$(_c4, \"Dashboard\");\n$RefreshReg$(_c5, \"Profile$lazy\");\n$RefreshReg$(_c6, \"Profile\");\n$RefreshReg$(_c7, \"User$lazy\");\n$RefreshReg$(_c8, \"User\");\n$RefreshReg$(_c9, \"CreateUser$lazy\");\n$RefreshReg$(_c0, \"CreateUser\");\n$RefreshReg$(_c1, \"FormUser$lazy\");\n$RefreshReg$(_c10, \"FormUser\");\n$RefreshReg$(_c11, \"Department$lazy\");\n$RefreshReg$(_c12, \"Department\");\n$RefreshReg$(_c13, \"FormDepartment$lazy\");\n$RefreshReg$(_c14, \"FormDepartment\");\n$RefreshReg$(_c15, \"Designation$lazy\");\n$RefreshReg$(_c16, \"Designation\");\n$RefreshReg$(_c17, \"FormDesignation$lazy\");\n$RefreshReg$(_c18, \"FormDesignation\");\n$RefreshReg$(_c19, \"Attendance$lazy\");\n$RefreshReg$(_c20, \"Attendance\");\n$RefreshReg$(_c21, \"FormAttendance$lazy\");\n$RefreshReg$(_c22, \"FormAttendance\");\n$RefreshReg$(_c23, \"Expenses$lazy\");\n$RefreshReg$(_c24, \"Expenses\");\n$RefreshReg$(_c25, \"FormExpenses$lazy\");\n$RefreshReg$(_c26, \"FormExpenses\");\n$RefreshReg$(_c27, \"Report$lazy\");\n$RefreshReg$(_c28, \"Report\");\n$RefreshReg$(_c29, \"Leaves$lazy\");\n$RefreshReg$(_c30, \"Leaves\");\n$RefreshReg$(_c31, \"FormLeave$lazy\");\n$RefreshReg$(_c32, \"FormLeave\");\n$RefreshReg$(_c33, \"LeaveReport$lazy\");\n$RefreshReg$(_c34, \"LeaveReport\");\n$RefreshReg$(_c35, \"Approval$lazy\");\n$RefreshReg$(_c36, \"Approval\");\n$RefreshReg$(_c37, \"LeaveConfiguration$lazy\");\n$RefreshReg$(_c38, \"LeaveConfiguration\");\n$RefreshReg$(_c39, \"LeaveCalendar$lazy\");\n$RefreshReg$(_c40, \"LeaveCalendar\");\n$RefreshReg$(_c41, \"Setting$lazy\");\n$RefreshReg$(_c42, \"Setting\");\n$RefreshReg$(_c43, \"Timeline$lazy\");\n$RefreshReg$(_c44, \"Timeline\");\n$RefreshReg$(_c45, \"Overivew$lazy\");\n$RefreshReg$(_c46, \"Overivew\");\n$RefreshReg$(_c47, \"TimeRequest$lazy\");\n$RefreshReg$(_c48, \"TimeRequest\");\n$RefreshReg$(_c49, \"TaskRequest$lazy\");\n$RefreshReg$(_c50, \"TaskRequest\");\n$RefreshReg$(_c51, \"WorkSchedule$lazy\");\n$RefreshReg$(_c52, \"WorkSchedule\");\n$RefreshReg$(_c53, \"ProductList$lazy\");\n$RefreshReg$(_c54, \"ProductList\");\n$RefreshReg$(_c55, \"ProductOverview$lazy\");\n$RefreshReg$(_c56, \"ProductOverview\");\n$RefreshReg$(_c57, \"ProductTimesheet$lazy\");\n$RefreshReg$(_c58, \"ProductTimesheet\");\n$RefreshReg$(_c59, \"Client$lazy\");\n$RefreshReg$(_c60, \"Client\");\n$RefreshReg$(_c61, \"Tasklist$lazy\");\n$RefreshReg$(_c62, \"Tasklist\");\n$RefreshReg$(_c63, \"ProductListStaff$lazy\");\n$RefreshReg$(_c64, \"ProductListStaff\");\n$RefreshReg$(_c65, \"TaskHistoryAdmin$lazy\");\n$RefreshReg$(_c66, \"TaskHistoryAdmin\");\n$RefreshReg$(_c67, \"TaskListWithNote$lazy\");\n$RefreshReg$(_c68, \"TaskListWithNote\");\n$RefreshReg$(_c69, \"SprintPage$lazy\");\n$RefreshReg$(_c70, \"SprintPage\");\n$RefreshReg$(_c71, \"UserSprintPage$lazy\");\n$RefreshReg$(_c72, \"UserSprintPage\");\n$RefreshReg$(_c73, \"SprintTasksPage$lazy\");\n$RefreshReg$(_c74, \"SprintTasksPage\");\n$RefreshReg$(_c75, \"Loader\");\n$RefreshReg$(_c76, \"Routes\");", "map": {"version": 3, "names": ["React", "useEffect", "useState", "lazy", "Suspense", "Route", "Switch", "Redirect", "PermissionRoute", "useSelector", "UserSelector", "features", "LoadingScreen", "MainLayout", "AuthLayout", "NotFound", "AccessDenied", "jsxDEV", "_jsxDEV", "<PERSON><PERSON>", "_c", "_c2", "Dashboard", "_c3", "_c4", "Profile", "_c5", "_c6", "User", "_c7", "_c8", "CreateUser", "_c9", "_c0", "FormUser", "_c1", "_c10", "Department", "_c11", "_c12", "FormDepartment", "_c13", "_c14", "Designation", "_c15", "_c16", "FormDesignation", "_c17", "_c18", "Attendance", "_c19", "_c20", "FormAttendance", "_c21", "_c22", "Expenses", "_c23", "_c24", "FormExpenses", "_c25", "_c26", "Report", "_c27", "_c28", "Leaves", "_c29", "_c30", "FormLeave", "_c31", "_c32", "LeaveReport", "_c33", "_c34", "Approval", "_c35", "_c36", "LeaveConfiguration", "_c37", "_c38", "LeaveCalendar", "_c39", "_c40", "Setting", "_c41", "_c42", "Timeline", "_c43", "_c44", "<PERSON><PERSON>w", "_c45", "_c46", "TimeRequest", "_c47", "_c48", "TaskRequest", "_c49", "_c50", "WorkSchedule", "_c51", "_c52", "ProductList", "_c53", "_c54", "ProductOverview", "_c55", "_c56", "ProductTimesheet", "_c57", "_c58", "Client", "_c59", "_c60", "Tasklist", "_c61", "_c62", "ProductListStaff", "_c63", "_c64", "TaskHistoryAdmin", "_c65", "_c66", "TaskListWithNote", "_c67", "_c68", "SprintPage", "_c69", "_c70", "UserSprintPage", "_c71", "_c72", "SprintTasksPage", "_c73", "_c74", "PrivateRoutes", "path", "component", "permission", "feat", "user", "act", "department", "designation", "attendance", "expense", "to", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "props", "match", "params", "id", "role", "leave", "leavereport", "approve", "configuration", "calendar", "projectlist", "projectoverview", "overview", "timerequest", "taskrequest", "workschedule", "report", "setting", "timeline", "projecttimesheet", "client", "projects", "tasknote", "Loader", "_c75", "Routes", "_s", "setUserRole", "profile", "fallback", "children", "exact", "map", "route", "i", "_c76", "$RefreshReg$"], "sources": ["E:/Suraj Patil/Organization_Management_System/meraki-frontend-main/src/routes.js"], "sourcesContent": ["import React, { useEffect, useState, lazy, Suspense } from \"react\";\r\nimport { Route, Switch, Redirect } from \"react-router-dom\";\r\nimport PermissionRoute from \"components/PermissionRoute\";\r\nimport { useSelector } from \"react-redux\";\r\nimport { UserSelector } from \"./selectors\";\r\nimport { features } from \"constants/permission\";\r\nimport LoadingScreen from \"./components/LoadingScreen\";\r\n\r\n// Layouts\r\nimport MainLayout from \"./layouts/MainLayout\";\r\nimport AuthLayout from \"./layouts/AuthLayout\";\r\n\r\n// Eagerly loaded components (small and frequently used)\r\nimport NotFound from \"components/NotFound\";\r\nimport AccessDenied from \"components/AccessDenied\";\r\n\r\n// Lazy loaded components\r\nconst Login = lazy(() => import(\"./screens/Auth/Login\"));\r\nconst Dashboard = lazy(() => import(\"./screens/Dashboard\"));\r\nconst Profile = lazy(() => import(\"./screens/Profile\"));\r\n\r\n// User management\r\nconst User = lazy(() => import(\"screens/User\"));\r\nconst CreateUser = lazy(() => import(\"./screens/User/Create\"));\r\nconst FormUser = lazy(() => import(\"./screens/User/Form\"));\r\n\r\n// Department\r\nconst Department = lazy(() => import(\"screens/Department\"));\r\nconst FormDepartment = lazy(() => import(\"./screens/Department/Form\"));\r\n\r\n// Designation\r\nconst Designation = lazy(() => import(\"./screens/Designation\"));\r\nconst FormDesignation = lazy(() => import(\"./screens/Designation/Form\"));\r\n\r\n// Attendance\r\nconst Attendance = lazy(() => import(\"./screens/Attendance\"));\r\nconst FormAttendance = lazy(() => import(\"./screens/Attendance/Form\"));\r\n\r\n// Expenses\r\nconst Expenses = lazy(() => import(\"./screens/Expenses\"));\r\nconst FormExpenses = lazy(() => import(\"./screens/Expenses/Form\"));\r\n\r\n// Reports\r\nconst Report = lazy(() => import(\"./screens/Report\"));\r\n\r\n// Leave management\r\nconst Leaves = lazy(() => import(\"./screens/Leave\"));\r\nconst FormLeave = lazy(() => import(\"./screens/Leave/Form\"));\r\nconst LeaveReport = lazy(() => import(\"screens/LeaveReport/LeaveReport\"));\r\nconst Approval = lazy(() => import(\"screens/LeaveApproval/Approval\"));\r\nconst LeaveConfiguration = lazy(() => import(\"screens/LeaveConfiguration/LeaveConfiguration\"));\r\nconst LeaveCalendar = lazy(() => import(\"screens/LeaveCalendar/LeaveCalendar\"));\r\n\r\n// Settings\r\nconst Setting = lazy(() => import(\"./screens/Setting\"));\r\n\r\n// Timeline\r\nconst Timeline = lazy(() => import(\"screens/Timeline\"));\r\nconst Overivew = lazy(() => import(\"screens/ActivityTimeline/Overivew\"));\r\nconst TimeRequest = lazy(() => import(\"screens/ActivityTimeline/TimeRequest\"));\r\nconst TaskRequest = lazy(() => import(\"./screens/TaskRequest\"));\r\nconst WorkSchedule = lazy(() => import(\"screens/WorkSchedule\"));\r\n\r\n// Project management\r\nconst ProductList = lazy(() => import(\"screens/Product/ProductList\"));\r\nconst ProductOverview = lazy(() => import(\"screens/Product/ProductOverview\"));\r\nconst ProductTimesheet = lazy(() => import(\"screens/Product/ProductTimesheet\"));\r\nconst Client = lazy(() => import(\"screens/Client/Client\"));\r\nconst Tasklist = lazy(() => import(\"screens/Product/Tasklist\"));\r\nconst ProductListStaff = lazy(() => import(\"screens/Product/Staff/ProductListStaff\"));\r\nconst TaskHistoryAdmin = lazy(() => import(\"screens/Product/TaskHistoryAdmin\"));\r\nconst TaskListWithNote = lazy(() => import(\"screens/Product/Staff/TaskListWithNote\"));\r\n\r\n// Sprint management\r\nconst SprintPage = lazy(() => import(\"screens/Sprints/pages/SprintPage\"));\r\nconst UserSprintPage = lazy(() => import(\"screens/Sprints/pages/UserSprintPage\"));\r\nconst SprintTasksPage = lazy(() => import(\"screens/Sprints/pages/SprintTasksPage\"));\r\n\r\nconst PrivateRoutes = [\r\n    // User routes\r\n    {\r\n        path: \"/app/user\",\r\n        component: User,\r\n        permission: { feat: features.user, act: 'read' }\r\n    },\r\n    {\r\n        path: \"/app/user/create\",\r\n        component: CreateUser,\r\n        permission: { feat: features.user, act: 'create' }\r\n    },\r\n    {\r\n        path: \"/app/user/update/:id\",\r\n        component: FormUser,\r\n        permission: { feat: features.user, act: 'update' }\r\n    },\r\n\r\n    // Department routes\r\n    {\r\n        path: \"/app/department\",\r\n        component: Department,\r\n        permission: { feat: features.department, act: 'read' }\r\n    },\r\n    {\r\n        path: \"/app/department/create\",\r\n        component: FormDepartment,\r\n        permission: { feat: features.department, act: 'create' }\r\n    },\r\n    {\r\n        path: \"/app/department/update/:id\",\r\n        component: FormDepartment,\r\n        permission: { feat: features.department, act: 'update' }\r\n    },\r\n\r\n    // Designation routes\r\n    {\r\n        path: \"/app/designation\",\r\n        component: Designation,\r\n        permission: { feat: features.designation, act: 'read' }\r\n    },\r\n    {\r\n        path: \"/app/designation/create\",\r\n        component: FormDesignation,\r\n        permission: { feat: features.designation, act: 'create' }\r\n    },\r\n    {\r\n        path: \"/app/designation/update/:id\",\r\n        component: FormDesignation,\r\n        permission: { feat: features.designation, act: 'update' }\r\n    },\r\n\r\n    // Attendance routes\r\n    {\r\n        path: \"/app/attendance\",\r\n        component: Attendance,\r\n        permission: { feat: \"attendance\", act: \"read\" }\r\n    },\r\n    {\r\n        path: \"/app/attendance/create\",\r\n        component: FormAttendance,\r\n        permission: { feat: features.attendance, act: 'create' }\r\n    },\r\n    {\r\n        path: \"/app/attendance/update/:id\",\r\n        component: FormAttendance,\r\n        permission: { feat: features.attendance, act: 'update' }\r\n    },\r\n\r\n    // Admin Expenses routes\r\n    {\r\n        path: \"/app/expenses\",\r\n        component: Expenses,\r\n        permission: { feat: features.expense, act: 'read' }\r\n    },\r\n    {\r\n        path: \"/app/expenses/create\",\r\n        component: FormExpenses,\r\n        permission: { feat: features.expense, act: 'create' }\r\n    },\r\n    {\r\n        path: \"/app/expenses/update/:id\",\r\n        component: FormExpenses,\r\n        permission: { feat: features.expense, act: 'update' }\r\n    },\r\n\r\n    // Redirect user expense routes to main expense routes\r\n    {\r\n        path: \"/app/user/expenses\",\r\n        component: () => <Redirect to=\"/app/expenses\" />,\r\n        permission: { feat: \"expense\", act: \"read\" }\r\n    },\r\n    {\r\n        path: \"/app/user/expenses/create\",\r\n        component: () => <Redirect to=\"/app/expenses/create\" />,\r\n        permission: { feat: \"expense\", act: \"create\" }\r\n    },\r\n    {\r\n        path: \"/app/user/expenses/update/:id\",\r\n        component: (props) => <Redirect to={`/app/expenses/update/${props.match.params.id}`} />,\r\n        permission: { feat: \"expense\", act: \"update\" }\r\n    },\r\n\r\n    // Leave routes\r\n    {\r\n        path: \"/app/leave\",\r\n        component: Leaves,\r\n        role: \"admin\",\r\n        permission: { feat: features.leave, act: 'read' }\r\n    },\r\n    {\r\n        path: \"/app/user/leave\",\r\n        component: Leaves,\r\n        permission: { feat: \"leave\", act: \"read\" }\r\n    },\r\n    {\r\n        path: \"/app/leave/create\",\r\n        component: FormLeave,\r\n        permission: { feat: features.leave, act: 'create' }\r\n    },\r\n    {\r\n        path: \"/app/leave/update/:id\",\r\n        component: FormLeave,\r\n        permission: { feat: features.leave, act: 'update' }\r\n    },\r\n    {\r\n        path: \"/app/leave/report\",\r\n        component: LeaveReport,\r\n        permission: { feat: features.leavereport, act: 'read' }\r\n    },\r\n    {\r\n        path: \"/app/leave/approval\",\r\n        component: Approval,\r\n        permission: { feat: features.approve, act: 'read' }\r\n    },\r\n    {\r\n        path: \"/app/leave/configuration\",\r\n        component: LeaveConfiguration,\r\n        permission: { feat: features.configuration, act: 'read' }\r\n    },\r\n    {\r\n        path: \"/app/leave/calendar\",\r\n        component: LeaveCalendar,\r\n        permission: { feat: features.calendar, act: 'read' }\r\n    },\r\n\r\n    // Project routes\r\n    {\r\n        path: '/app/project/list',\r\n        component: ProductList,\r\n        permission: { feat: features.projectlist, act: 'read' }\r\n    },\r\n    {\r\n        path: '/app/project/overview',\r\n        component: ProductOverview,\r\n        permission: { feat: features.projectoverview, act: 'read' }\r\n    },\r\n    {\r\n        path: \"/app/timesheet\",\r\n        component: Leaves,\r\n        role: \"admin\",\r\n        permission: { feat: features.leave, act: 'read' }\r\n    },\r\n\r\n    // Timeline routes\r\n    {\r\n        path: \"/app/timeline/overview\",\r\n        component: Overivew,\r\n        permission: { feat: features.overview, act: 'read' }\r\n    },\r\n    {\r\n        path: \"/app/timeline/request\",\r\n        component: TimeRequest,\r\n        permission: { feat: features.timerequest, act: 'read' }\r\n    },\r\n    {\r\n        path: \"/app/timeline/taskrequest\",\r\n        component: TaskRequest,\r\n        permission: { feat: features.taskrequest, act: 'read' }\r\n    },\r\n    {\r\n        path: \"/app/timeline/workschedule\",\r\n        component: WorkSchedule,\r\n        permission: { feat: features.workschedule, act: 'read' }\r\n    },\r\n\r\n    // Other routes\r\n    {\r\n        path: \"/app/report\",\r\n        component: Report,\r\n        permission: { feat: features.report, act: 'read' }\r\n    },\r\n    {\r\n        path: \"/app/setting\",\r\n        component: Setting,\r\n        permission: { feat: features.setting, act: 'read' }\r\n    },\r\n\r\n    // Profile route\r\n    {\r\n        path: \"/app/profile\",\r\n        component: Profile,\r\n        permission: { feat: 'Profile', act: 'read' }\r\n    },\r\n\r\n    // Dashboard routes - no permission required (hardcoded access)\r\n    {\r\n        path: \"/app/dashboard\",\r\n        component: Dashboard\r\n    },\r\n    {\r\n        path: \"/app/admin-dashboard\",\r\n        component: Dashboard\r\n    },\r\n    {\r\n        path: \"/app/user-dashboard\",\r\n        component: Dashboard\r\n    },\r\n\r\n    // Timeline routes\r\n    {\r\n        path: \"/app/timeline\",\r\n        component: Timeline,\r\n        permission: { feat: features.timeline, act: 'read' }\r\n    },\r\n    {\r\n        path: \"/app/user/timeline\",\r\n        component: Timeline,\r\n        permission: { feat: \"Timeline\", act: \"read\" }\r\n    },\r\n    {\r\n        path: \"/app/project/timesheet\",\r\n        component: ProductTimesheet,\r\n        permission: { feat: features.projecttimesheet, act: 'read' }\r\n    },\r\n\r\n    // Client and Task routes\r\n    {\r\n        path: '/app/client',\r\n        component: Client,\r\n        permission: { feat: features.client, act: 'read' }\r\n    },\r\n    {\r\n        path: '/app/project/update/:data',\r\n        component: TaskHistoryAdmin,\r\n        permission: { feat: features.projects, act: 'update' }\r\n    },\r\n    {\r\n        path: '/app/tasks',\r\n        component: Tasklist,\r\n        permission: { feat: \"My Tasks\", act: \"read\" }\r\n    },\r\n    {\r\n        path: '/app/user/projects',\r\n        component: ProductListStaff,\r\n        permission: { feat: \"Projects\", act: \"read\" }\r\n    },\r\n    {\r\n        path: '/app/user/project/overview',\r\n        component: ProductOverview,\r\n        permission: { feat: \"projectoverview\", act: \"read\" }\r\n    },\r\n    {\r\n        path: '/app/user/project/timesheet',\r\n        component: ProductTimesheet,\r\n        permission: { feat: \"projecttimesheet\", act: \"read\" }\r\n    },\r\n    {\r\n        path: '/app/user/tasklist/note/:data',\r\n        component: TaskListWithNote,\r\n        permission: { feat: features.tasknote, act: 'read' }\r\n    },\r\n\r\n    // Sprint routes\r\n    {\r\n        path: '/app/sprint',\r\n        component: SprintPage,\r\n        permission: { feat: \"Sprint\", act: \"read_all\" }\r\n    },\r\n    {\r\n        path: '/app/sprint/form',\r\n        component: SprintPage,\r\n        permission: { feat: \"Sprint\", act: \"create\" }\r\n    },\r\n    {\r\n        path: '/app/sprint/form/:id',\r\n        component: SprintPage,\r\n        permission: { feat: \"Sprint\", act: \"update\" }\r\n    },\r\n    {\r\n        path: '/app/sprint/:sprintId/tasks',\r\n        component: SprintTasksPage,\r\n        permission: { feat: \"Sprint\", act: \"read_all\" }\r\n    },\r\n    {\r\n        path: '/app/user/sprint',\r\n        component: UserSprintPage,\r\n        permission: { feat: \"Sprint\", act: \"read_self\" }\r\n    },\r\n    {\r\n        path: '/app/user/sprint/:sprintId/tasks',\r\n        component: SprintTasksPage,\r\n        permission: { feat: \"Sprint\", act: \"read_self\" }\r\n    },\r\n];\r\n\r\n// Loading component for suspense fallback\r\nconst Loader = () => <LoadingScreen />;\r\n\r\nexport default function Routes() {\r\nconst [role,setUserRole] = useState([]);\r\n    const profile = useSelector(UserSelector.profile());\r\n    \r\n\r\n    useEffect(() => {\r\n        if (profile) {\r\n            setUserRole(profile.role || []);\r\n        }\r\n    }, [profile]);\r\n\r\n    return (\r\n        <Suspense fallback={<Loader />}>\r\n            <Switch>\r\n                {/* Private routes that require authentication */}\r\n                <Route path=\"/app\">\r\n                    <MainLayout>\r\n                        <Suspense fallback={<Loader />}>\r\n                            <Switch>\r\n                                {/* Dashboard is always accessible */}\r\n                                <Route exact path=\"/app/dashboard\" component={Dashboard} />\r\n\r\n                                {/* Profile is always accessible */}\r\n                                <Route exact path=\"/app/profile\" component={Profile} />\r\n\r\n                                {/* Permission-based routes */}\r\n                                {PrivateRoutes.map((route, i) => {\r\n                                    // All routes now use PermissionRoute\r\n                                    if (route.permission) {\r\n                                        return (\r\n                                            <PermissionRoute\r\n                                                key={i}\r\n                                                exact\r\n                                                path={route.path}\r\n                                                component={route.component}\r\n                                                permission={route.permission}\r\n                                            />\r\n                                        );\r\n                                    }\r\n\r\n                                    // If no permission specified (should be rare), use regular Route\r\n                                    return (\r\n                                        <Route\r\n                                            key={i}\r\n                                            exact\r\n                                            path={route.path}\r\n                                            component={route.component}\r\n                                        />\r\n                                    );\r\n                                })}\r\n\r\n                                {/* Show NotFound component if no route matches */}\r\n                                <Route component={NotFound} />\r\n                            </Switch>\r\n                        </Suspense>\r\n                    </MainLayout>\r\n                </Route>\r\n\r\n                {/* Public routes */}\r\n                <Route exact path=\"/\">\r\n                    <AuthLayout>\r\n                        <Suspense fallback={<Loader />}>\r\n                            <Switch>\r\n                                <Route exact path=\"/\" component={Login} />\r\n                            </Switch>\r\n                        </Suspense>\r\n                    </AuthLayout>\r\n                </Route>\r\n\r\n                {/* Access Denied route */}\r\n                <Route path=\"/access-denied\">\r\n                    <MainLayout>\r\n                        <AccessDenied />\r\n                    </MainLayout>\r\n                </Route>\r\n\r\n                {/* 404 Not Found route */}\r\n                <Route path=\"/not-found\">\r\n                    <MainLayout>\r\n                        <NotFound />\r\n                    </MainLayout>\r\n                </Route>\r\n\r\n                {/* Redirect to not-found if no route matches */}\r\n                <Route>\r\n                    <MainLayout>\r\n                        <NotFound />\r\n                    </MainLayout>\r\n                </Route>\r\n            </Switch>\r\n        </Suspense>\r\n    );\r\n}"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,QAAQ,EAAEC,IAAI,EAAEC,QAAQ,QAAQ,OAAO;AAClE,SAASC,KAAK,EAAEC,MAAM,EAAEC,QAAQ,QAAQ,kBAAkB;AAC1D,OAAOC,eAAe,MAAM,4BAA4B;AACxD,SAASC,WAAW,QAAQ,aAAa;AACzC,SAASC,YAAY,QAAQ,aAAa;AAC1C,SAASC,QAAQ,QAAQ,sBAAsB;AAC/C,OAAOC,aAAa,MAAM,4BAA4B;;AAEtD;AACA,OAAOC,UAAU,MAAM,sBAAsB;AAC7C,OAAOC,UAAU,MAAM,sBAAsB;;AAE7C;AACA,OAAOC,QAAQ,MAAM,qBAAqB;AAC1C,OAAOC,YAAY,MAAM,yBAAyB;;AAElD;AAAA,SAAAC,MAAA,IAAAC,OAAA;AACA,MAAMC,KAAK,gBAAGhB,IAAI,CAAAiB,EAAA,GAACA,CAAA,KAAM,MAAM,CAAC,sBAAsB,CAAC,CAAC;AAACC,GAAA,GAAnDF,KAAK;AACX,MAAMG,SAAS,gBAAGnB,IAAI,CAAAoB,GAAA,GAACA,CAAA,KAAM,MAAM,CAAC,qBAAqB,CAAC,CAAC;AAACC,GAAA,GAAtDF,SAAS;AACf,MAAMG,OAAO,gBAAGtB,IAAI,CAAAuB,GAAA,GAACA,CAAA,KAAM,MAAM,CAAC,mBAAmB,CAAC,CAAC;;AAEvD;AAAAC,GAAA,GAFMF,OAAO;AAGb,MAAMG,IAAI,gBAAGzB,IAAI,CAAA0B,GAAA,GAACA,CAAA,KAAM,MAAM,CAAC,cAAc,CAAC,CAAC;AAACC,GAAA,GAA1CF,IAAI;AACV,MAAMG,UAAU,gBAAG5B,IAAI,CAAA6B,GAAA,GAACA,CAAA,KAAM,MAAM,CAAC,uBAAuB,CAAC,CAAC;AAACC,GAAA,GAAzDF,UAAU;AAChB,MAAMG,QAAQ,gBAAG/B,IAAI,CAAAgC,GAAA,GAACA,CAAA,KAAM,MAAM,CAAC,qBAAqB,CAAC,CAAC;;AAE1D;AAAAC,IAAA,GAFMF,QAAQ;AAGd,MAAMG,UAAU,gBAAGlC,IAAI,CAAAmC,IAAA,GAACA,CAAA,KAAM,MAAM,CAAC,oBAAoB,CAAC,CAAC;AAACC,IAAA,GAAtDF,UAAU;AAChB,MAAMG,cAAc,gBAAGrC,IAAI,CAAAsC,IAAA,GAACA,CAAA,KAAM,MAAM,CAAC,2BAA2B,CAAC,CAAC;;AAEtE;AAAAC,IAAA,GAFMF,cAAc;AAGpB,MAAMG,WAAW,gBAAGxC,IAAI,CAAAyC,IAAA,GAACA,CAAA,KAAM,MAAM,CAAC,uBAAuB,CAAC,CAAC;AAACC,IAAA,GAA1DF,WAAW;AACjB,MAAMG,eAAe,gBAAG3C,IAAI,CAAA4C,IAAA,GAACA,CAAA,KAAM,MAAM,CAAC,4BAA4B,CAAC,CAAC;;AAExE;AAAAC,IAAA,GAFMF,eAAe;AAGrB,MAAMG,UAAU,gBAAG9C,IAAI,CAAA+C,IAAA,GAACA,CAAA,KAAM,MAAM,CAAC,sBAAsB,CAAC,CAAC;AAACC,IAAA,GAAxDF,UAAU;AAChB,MAAMG,cAAc,gBAAGjD,IAAI,CAAAkD,IAAA,GAACA,CAAA,KAAM,MAAM,CAAC,2BAA2B,CAAC,CAAC;;AAEtE;AAAAC,IAAA,GAFMF,cAAc;AAGpB,MAAMG,QAAQ,gBAAGpD,IAAI,CAAAqD,IAAA,GAACA,CAAA,KAAM,MAAM,CAAC,oBAAoB,CAAC,CAAC;AAACC,IAAA,GAApDF,QAAQ;AACd,MAAMG,YAAY,gBAAGvD,IAAI,CAAAwD,IAAA,GAACA,CAAA,KAAM,MAAM,CAAC,yBAAyB,CAAC,CAAC;;AAElE;AAAAC,IAAA,GAFMF,YAAY;AAGlB,MAAMG,MAAM,gBAAG1D,IAAI,CAAA2D,IAAA,GAACA,CAAA,KAAM,MAAM,CAAC,kBAAkB,CAAC,CAAC;;AAErD;AAAAC,IAAA,GAFMF,MAAM;AAGZ,MAAMG,MAAM,gBAAG7D,IAAI,CAAA8D,IAAA,GAACA,CAAA,KAAM,MAAM,CAAC,iBAAiB,CAAC,CAAC;AAACC,IAAA,GAA/CF,MAAM;AACZ,MAAMG,SAAS,gBAAGhE,IAAI,CAAAiE,IAAA,GAACA,CAAA,KAAM,MAAM,CAAC,sBAAsB,CAAC,CAAC;AAACC,IAAA,GAAvDF,SAAS;AACf,MAAMG,WAAW,gBAAGnE,IAAI,CAAAoE,IAAA,GAACA,CAAA,KAAM,MAAM,CAAC,iCAAiC,CAAC,CAAC;AAACC,IAAA,GAApEF,WAAW;AACjB,MAAMG,QAAQ,gBAAGtE,IAAI,CAAAuE,IAAA,GAACA,CAAA,KAAM,MAAM,CAAC,gCAAgC,CAAC,CAAC;AAACC,IAAA,GAAhEF,QAAQ;AACd,MAAMG,kBAAkB,gBAAGzE,IAAI,CAAA0E,IAAA,GAACA,CAAA,KAAM,MAAM,CAAC,+CAA+C,CAAC,CAAC;AAACC,IAAA,GAAzFF,kBAAkB;AACxB,MAAMG,aAAa,gBAAG5E,IAAI,CAAA6E,IAAA,GAACA,CAAA,KAAM,MAAM,CAAC,qCAAqC,CAAC,CAAC;;AAE/E;AAAAC,IAAA,GAFMF,aAAa;AAGnB,MAAMG,OAAO,gBAAG/E,IAAI,CAAAgF,IAAA,GAACA,CAAA,KAAM,MAAM,CAAC,mBAAmB,CAAC,CAAC;;AAEvD;AAAAC,IAAA,GAFMF,OAAO;AAGb,MAAMG,QAAQ,gBAAGlF,IAAI,CAAAmF,IAAA,GAACA,CAAA,KAAM,MAAM,CAAC,kBAAkB,CAAC,CAAC;AAACC,IAAA,GAAlDF,QAAQ;AACd,MAAMG,QAAQ,gBAAGrF,IAAI,CAAAsF,IAAA,GAACA,CAAA,KAAM,MAAM,CAAC,mCAAmC,CAAC,CAAC;AAACC,IAAA,GAAnEF,QAAQ;AACd,MAAMG,WAAW,gBAAGxF,IAAI,CAAAyF,IAAA,GAACA,CAAA,KAAM,MAAM,CAAC,sCAAsC,CAAC,CAAC;AAACC,IAAA,GAAzEF,WAAW;AACjB,MAAMG,WAAW,gBAAG3F,IAAI,CAAA4F,IAAA,GAACA,CAAA,KAAM,MAAM,CAAC,uBAAuB,CAAC,CAAC;AAACC,IAAA,GAA1DF,WAAW;AACjB,MAAMG,YAAY,gBAAG9F,IAAI,CAAA+F,IAAA,GAACA,CAAA,KAAM,MAAM,CAAC,sBAAsB,CAAC,CAAC;;AAE/D;AAAAC,IAAA,GAFMF,YAAY;AAGlB,MAAMG,WAAW,gBAAGjG,IAAI,CAAAkG,IAAA,GAACA,CAAA,KAAM,MAAM,CAAC,6BAA6B,CAAC,CAAC;AAACC,IAAA,GAAhEF,WAAW;AACjB,MAAMG,eAAe,gBAAGpG,IAAI,CAAAqG,IAAA,GAACA,CAAA,KAAM,MAAM,CAAC,iCAAiC,CAAC,CAAC;AAACC,IAAA,GAAxEF,eAAe;AACrB,MAAMG,gBAAgB,gBAAGvG,IAAI,CAAAwG,IAAA,GAACA,CAAA,KAAM,MAAM,CAAC,kCAAkC,CAAC,CAAC;AAACC,IAAA,GAA1EF,gBAAgB;AACtB,MAAMG,MAAM,gBAAG1G,IAAI,CAAA2G,IAAA,GAACA,CAAA,KAAM,MAAM,CAAC,uBAAuB,CAAC,CAAC;AAACC,IAAA,GAArDF,MAAM;AACZ,MAAMG,QAAQ,gBAAG7G,IAAI,CAAA8G,IAAA,GAACA,CAAA,KAAM,MAAM,CAAC,0BAA0B,CAAC,CAAC;AAACC,IAAA,GAA1DF,QAAQ;AACd,MAAMG,gBAAgB,gBAAGhH,IAAI,CAAAiH,IAAA,GAACA,CAAA,KAAM,MAAM,CAAC,wCAAwC,CAAC,CAAC;AAACC,IAAA,GAAhFF,gBAAgB;AACtB,MAAMG,gBAAgB,gBAAGnH,IAAI,CAAAoH,IAAA,GAACA,CAAA,KAAM,MAAM,CAAC,kCAAkC,CAAC,CAAC;AAACC,IAAA,GAA1EF,gBAAgB;AACtB,MAAMG,gBAAgB,gBAAGtH,IAAI,CAAAuH,IAAA,GAACA,CAAA,KAAM,MAAM,CAAC,wCAAwC,CAAC,CAAC;;AAErF;AAAAC,IAAA,GAFMF,gBAAgB;AAGtB,MAAMG,UAAU,gBAAGzH,IAAI,CAAA0H,IAAA,GAACA,CAAA,KAAM,MAAM,CAAC,kCAAkC,CAAC,CAAC;AAACC,IAAA,GAApEF,UAAU;AAChB,MAAMG,cAAc,gBAAG5H,IAAI,CAAA6H,IAAA,GAACA,CAAA,KAAM,MAAM,CAAC,sCAAsC,CAAC,CAAC;AAACC,IAAA,GAA5EF,cAAc;AACpB,MAAMG,eAAe,gBAAG/H,IAAI,CAAAgI,IAAA,GAACA,CAAA,KAAM,MAAM,CAAC,uCAAuC,CAAC,CAAC;AAACC,IAAA,GAA9EF,eAAe;AAErB,MAAMG,aAAa,GAAG;AAClB;AACA;EACIC,IAAI,EAAE,WAAW;EACjBC,SAAS,EAAE3G,IAAI;EACf4G,UAAU,EAAE;IAAEC,IAAI,EAAE9H,QAAQ,CAAC+H,IAAI;IAAEC,GAAG,EAAE;EAAO;AACnD,CAAC,EACD;EACIL,IAAI,EAAE,kBAAkB;EACxBC,SAAS,EAAExG,UAAU;EACrByG,UAAU,EAAE;IAAEC,IAAI,EAAE9H,QAAQ,CAAC+H,IAAI;IAAEC,GAAG,EAAE;EAAS;AACrD,CAAC,EACD;EACIL,IAAI,EAAE,sBAAsB;EAC5BC,SAAS,EAAErG,QAAQ;EACnBsG,UAAU,EAAE;IAAEC,IAAI,EAAE9H,QAAQ,CAAC+H,IAAI;IAAEC,GAAG,EAAE;EAAS;AACrD,CAAC;AAED;AACA;EACIL,IAAI,EAAE,iBAAiB;EACvBC,SAAS,EAAElG,UAAU;EACrBmG,UAAU,EAAE;IAAEC,IAAI,EAAE9H,QAAQ,CAACiI,UAAU;IAAED,GAAG,EAAE;EAAO;AACzD,CAAC,EACD;EACIL,IAAI,EAAE,wBAAwB;EAC9BC,SAAS,EAAE/F,cAAc;EACzBgG,UAAU,EAAE;IAAEC,IAAI,EAAE9H,QAAQ,CAACiI,UAAU;IAAED,GAAG,EAAE;EAAS;AAC3D,CAAC,EACD;EACIL,IAAI,EAAE,4BAA4B;EAClCC,SAAS,EAAE/F,cAAc;EACzBgG,UAAU,EAAE;IAAEC,IAAI,EAAE9H,QAAQ,CAACiI,UAAU;IAAED,GAAG,EAAE;EAAS;AAC3D,CAAC;AAED;AACA;EACIL,IAAI,EAAE,kBAAkB;EACxBC,SAAS,EAAE5F,WAAW;EACtB6F,UAAU,EAAE;IAAEC,IAAI,EAAE9H,QAAQ,CAACkI,WAAW;IAAEF,GAAG,EAAE;EAAO;AAC1D,CAAC,EACD;EACIL,IAAI,EAAE,yBAAyB;EAC/BC,SAAS,EAAEzF,eAAe;EAC1B0F,UAAU,EAAE;IAAEC,IAAI,EAAE9H,QAAQ,CAACkI,WAAW;IAAEF,GAAG,EAAE;EAAS;AAC5D,CAAC,EACD;EACIL,IAAI,EAAE,6BAA6B;EACnCC,SAAS,EAAEzF,eAAe;EAC1B0F,UAAU,EAAE;IAAEC,IAAI,EAAE9H,QAAQ,CAACkI,WAAW;IAAEF,GAAG,EAAE;EAAS;AAC5D,CAAC;AAED;AACA;EACIL,IAAI,EAAE,iBAAiB;EACvBC,SAAS,EAAEtF,UAAU;EACrBuF,UAAU,EAAE;IAAEC,IAAI,EAAE,YAAY;IAAEE,GAAG,EAAE;EAAO;AAClD,CAAC,EACD;EACIL,IAAI,EAAE,wBAAwB;EAC9BC,SAAS,EAAEnF,cAAc;EACzBoF,UAAU,EAAE;IAAEC,IAAI,EAAE9H,QAAQ,CAACmI,UAAU;IAAEH,GAAG,EAAE;EAAS;AAC3D,CAAC,EACD;EACIL,IAAI,EAAE,4BAA4B;EAClCC,SAAS,EAAEnF,cAAc;EACzBoF,UAAU,EAAE;IAAEC,IAAI,EAAE9H,QAAQ,CAACmI,UAAU;IAAEH,GAAG,EAAE;EAAS;AAC3D,CAAC;AAED;AACA;EACIL,IAAI,EAAE,eAAe;EACrBC,SAAS,EAAEhF,QAAQ;EACnBiF,UAAU,EAAE;IAAEC,IAAI,EAAE9H,QAAQ,CAACoI,OAAO;IAAEJ,GAAG,EAAE;EAAO;AACtD,CAAC,EACD;EACIL,IAAI,EAAE,sBAAsB;EAC5BC,SAAS,EAAE7E,YAAY;EACvB8E,UAAU,EAAE;IAAEC,IAAI,EAAE9H,QAAQ,CAACoI,OAAO;IAAEJ,GAAG,EAAE;EAAS;AACxD,CAAC,EACD;EACIL,IAAI,EAAE,0BAA0B;EAChCC,SAAS,EAAE7E,YAAY;EACvB8E,UAAU,EAAE;IAAEC,IAAI,EAAE9H,QAAQ,CAACoI,OAAO;IAAEJ,GAAG,EAAE;EAAS;AACxD,CAAC;AAED;AACA;EACIL,IAAI,EAAE,oBAAoB;EAC1BC,SAAS,EAAEA,CAAA,kBAAMrH,OAAA,CAACX,QAAQ;IAACyI,EAAE,EAAC;EAAe;IAAAC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE,CAAC;EAChDZ,UAAU,EAAE;IAAEC,IAAI,EAAE,SAAS;IAAEE,GAAG,EAAE;EAAO;AAC/C,CAAC,EACD;EACIL,IAAI,EAAE,2BAA2B;EACjCC,SAAS,EAAEA,CAAA,kBAAMrH,OAAA,CAACX,QAAQ;IAACyI,EAAE,EAAC;EAAsB;IAAAC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE,CAAC;EACvDZ,UAAU,EAAE;IAAEC,IAAI,EAAE,SAAS;IAAEE,GAAG,EAAE;EAAS;AACjD,CAAC,EACD;EACIL,IAAI,EAAE,+BAA+B;EACrCC,SAAS,EAAGc,KAAK,iBAAKnI,OAAA,CAACX,QAAQ;IAACyI,EAAE,EAAE,wBAAwBK,KAAK,CAACC,KAAK,CAACC,MAAM,CAACC,EAAE;EAAG;IAAAP,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE,CAAC;EACvFZ,UAAU,EAAE;IAAEC,IAAI,EAAE,SAAS;IAAEE,GAAG,EAAE;EAAS;AACjD,CAAC;AAED;AACA;EACIL,IAAI,EAAE,YAAY;EAClBC,SAAS,EAAEvE,MAAM;EACjByF,IAAI,EAAE,OAAO;EACbjB,UAAU,EAAE;IAAEC,IAAI,EAAE9H,QAAQ,CAAC+I,KAAK;IAAEf,GAAG,EAAE;EAAO;AACpD,CAAC,EACD;EACIL,IAAI,EAAE,iBAAiB;EACvBC,SAAS,EAAEvE,MAAM;EACjBwE,UAAU,EAAE;IAAEC,IAAI,EAAE,OAAO;IAAEE,GAAG,EAAE;EAAO;AAC7C,CAAC,EACD;EACIL,IAAI,EAAE,mBAAmB;EACzBC,SAAS,EAAEpE,SAAS;EACpBqE,UAAU,EAAE;IAAEC,IAAI,EAAE9H,QAAQ,CAAC+I,KAAK;IAAEf,GAAG,EAAE;EAAS;AACtD,CAAC,EACD;EACIL,IAAI,EAAE,uBAAuB;EAC7BC,SAAS,EAAEpE,SAAS;EACpBqE,UAAU,EAAE;IAAEC,IAAI,EAAE9H,QAAQ,CAAC+I,KAAK;IAAEf,GAAG,EAAE;EAAS;AACtD,CAAC,EACD;EACIL,IAAI,EAAE,mBAAmB;EACzBC,SAAS,EAAEjE,WAAW;EACtBkE,UAAU,EAAE;IAAEC,IAAI,EAAE9H,QAAQ,CAACgJ,WAAW;IAAEhB,GAAG,EAAE;EAAO;AAC1D,CAAC,EACD;EACIL,IAAI,EAAE,qBAAqB;EAC3BC,SAAS,EAAE9D,QAAQ;EACnB+D,UAAU,EAAE;IAAEC,IAAI,EAAE9H,QAAQ,CAACiJ,OAAO;IAAEjB,GAAG,EAAE;EAAO;AACtD,CAAC,EACD;EACIL,IAAI,EAAE,0BAA0B;EAChCC,SAAS,EAAE3D,kBAAkB;EAC7B4D,UAAU,EAAE;IAAEC,IAAI,EAAE9H,QAAQ,CAACkJ,aAAa;IAAElB,GAAG,EAAE;EAAO;AAC5D,CAAC,EACD;EACIL,IAAI,EAAE,qBAAqB;EAC3BC,SAAS,EAAExD,aAAa;EACxByD,UAAU,EAAE;IAAEC,IAAI,EAAE9H,QAAQ,CAACmJ,QAAQ;IAAEnB,GAAG,EAAE;EAAO;AACvD,CAAC;AAED;AACA;EACIL,IAAI,EAAE,mBAAmB;EACzBC,SAAS,EAAEnC,WAAW;EACtBoC,UAAU,EAAE;IAAEC,IAAI,EAAE9H,QAAQ,CAACoJ,WAAW;IAAEpB,GAAG,EAAE;EAAO;AAC1D,CAAC,EACD;EACIL,IAAI,EAAE,uBAAuB;EAC7BC,SAAS,EAAEhC,eAAe;EAC1BiC,UAAU,EAAE;IAAEC,IAAI,EAAE9H,QAAQ,CAACqJ,eAAe;IAAErB,GAAG,EAAE;EAAO;AAC9D,CAAC,EACD;EACIL,IAAI,EAAE,gBAAgB;EACtBC,SAAS,EAAEvE,MAAM;EACjByF,IAAI,EAAE,OAAO;EACbjB,UAAU,EAAE;IAAEC,IAAI,EAAE9H,QAAQ,CAAC+I,KAAK;IAAEf,GAAG,EAAE;EAAO;AACpD,CAAC;AAED;AACA;EACIL,IAAI,EAAE,wBAAwB;EAC9BC,SAAS,EAAE/C,QAAQ;EACnBgD,UAAU,EAAE;IAAEC,IAAI,EAAE9H,QAAQ,CAACsJ,QAAQ;IAAEtB,GAAG,EAAE;EAAO;AACvD,CAAC,EACD;EACIL,IAAI,EAAE,uBAAuB;EAC7BC,SAAS,EAAE5C,WAAW;EACtB6C,UAAU,EAAE;IAAEC,IAAI,EAAE9H,QAAQ,CAACuJ,WAAW;IAAEvB,GAAG,EAAE;EAAO;AAC1D,CAAC,EACD;EACIL,IAAI,EAAE,2BAA2B;EACjCC,SAAS,EAAEzC,WAAW;EACtB0C,UAAU,EAAE;IAAEC,IAAI,EAAE9H,QAAQ,CAACwJ,WAAW;IAAExB,GAAG,EAAE;EAAO;AAC1D,CAAC,EACD;EACIL,IAAI,EAAE,4BAA4B;EAClCC,SAAS,EAAEtC,YAAY;EACvBuC,UAAU,EAAE;IAAEC,IAAI,EAAE9H,QAAQ,CAACyJ,YAAY;IAAEzB,GAAG,EAAE;EAAO;AAC3D,CAAC;AAED;AACA;EACIL,IAAI,EAAE,aAAa;EACnBC,SAAS,EAAE1E,MAAM;EACjB2E,UAAU,EAAE;IAAEC,IAAI,EAAE9H,QAAQ,CAAC0J,MAAM;IAAE1B,GAAG,EAAE;EAAO;AACrD,CAAC,EACD;EACIL,IAAI,EAAE,cAAc;EACpBC,SAAS,EAAErD,OAAO;EAClBsD,UAAU,EAAE;IAAEC,IAAI,EAAE9H,QAAQ,CAAC2J,OAAO;IAAE3B,GAAG,EAAE;EAAO;AACtD,CAAC;AAED;AACA;EACIL,IAAI,EAAE,cAAc;EACpBC,SAAS,EAAE9G,OAAO;EAClB+G,UAAU,EAAE;IAAEC,IAAI,EAAE,SAAS;IAAEE,GAAG,EAAE;EAAO;AAC/C,CAAC;AAED;AACA;EACIL,IAAI,EAAE,gBAAgB;EACtBC,SAAS,EAAEjH;AACf,CAAC,EACD;EACIgH,IAAI,EAAE,sBAAsB;EAC5BC,SAAS,EAAEjH;AACf,CAAC,EACD;EACIgH,IAAI,EAAE,qBAAqB;EAC3BC,SAAS,EAAEjH;AACf,CAAC;AAED;AACA;EACIgH,IAAI,EAAE,eAAe;EACrBC,SAAS,EAAElD,QAAQ;EACnBmD,UAAU,EAAE;IAAEC,IAAI,EAAE9H,QAAQ,CAAC4J,QAAQ;IAAE5B,GAAG,EAAE;EAAO;AACvD,CAAC,EACD;EACIL,IAAI,EAAE,oBAAoB;EAC1BC,SAAS,EAAElD,QAAQ;EACnBmD,UAAU,EAAE;IAAEC,IAAI,EAAE,UAAU;IAAEE,GAAG,EAAE;EAAO;AAChD,CAAC,EACD;EACIL,IAAI,EAAE,wBAAwB;EAC9BC,SAAS,EAAE7B,gBAAgB;EAC3B8B,UAAU,EAAE;IAAEC,IAAI,EAAE9H,QAAQ,CAAC6J,gBAAgB;IAAE7B,GAAG,EAAE;EAAO;AAC/D,CAAC;AAED;AACA;EACIL,IAAI,EAAE,aAAa;EACnBC,SAAS,EAAE1B,MAAM;EACjB2B,UAAU,EAAE;IAAEC,IAAI,EAAE9H,QAAQ,CAAC8J,MAAM;IAAE9B,GAAG,EAAE;EAAO;AACrD,CAAC,EACD;EACIL,IAAI,EAAE,2BAA2B;EACjCC,SAAS,EAAEjB,gBAAgB;EAC3BkB,UAAU,EAAE;IAAEC,IAAI,EAAE9H,QAAQ,CAAC+J,QAAQ;IAAE/B,GAAG,EAAE;EAAS;AACzD,CAAC,EACD;EACIL,IAAI,EAAE,YAAY;EAClBC,SAAS,EAAEvB,QAAQ;EACnBwB,UAAU,EAAE;IAAEC,IAAI,EAAE,UAAU;IAAEE,GAAG,EAAE;EAAO;AAChD,CAAC,EACD;EACIL,IAAI,EAAE,oBAAoB;EAC1BC,SAAS,EAAEpB,gBAAgB;EAC3BqB,UAAU,EAAE;IAAEC,IAAI,EAAE,UAAU;IAAEE,GAAG,EAAE;EAAO;AAChD,CAAC,EACD;EACIL,IAAI,EAAE,4BAA4B;EAClCC,SAAS,EAAEhC,eAAe;EAC1BiC,UAAU,EAAE;IAAEC,IAAI,EAAE,iBAAiB;IAAEE,GAAG,EAAE;EAAO;AACvD,CAAC,EACD;EACIL,IAAI,EAAE,6BAA6B;EACnCC,SAAS,EAAE7B,gBAAgB;EAC3B8B,UAAU,EAAE;IAAEC,IAAI,EAAE,kBAAkB;IAAEE,GAAG,EAAE;EAAO;AACxD,CAAC,EACD;EACIL,IAAI,EAAE,+BAA+B;EACrCC,SAAS,EAAEd,gBAAgB;EAC3Be,UAAU,EAAE;IAAEC,IAAI,EAAE9H,QAAQ,CAACgK,QAAQ;IAAEhC,GAAG,EAAE;EAAO;AACvD,CAAC;AAED;AACA;EACIL,IAAI,EAAE,aAAa;EACnBC,SAAS,EAAEX,UAAU;EACrBY,UAAU,EAAE;IAAEC,IAAI,EAAE,QAAQ;IAAEE,GAAG,EAAE;EAAW;AAClD,CAAC,EACD;EACIL,IAAI,EAAE,kBAAkB;EACxBC,SAAS,EAAEX,UAAU;EACrBY,UAAU,EAAE;IAAEC,IAAI,EAAE,QAAQ;IAAEE,GAAG,EAAE;EAAS;AAChD,CAAC,EACD;EACIL,IAAI,EAAE,sBAAsB;EAC5BC,SAAS,EAAEX,UAAU;EACrBY,UAAU,EAAE;IAAEC,IAAI,EAAE,QAAQ;IAAEE,GAAG,EAAE;EAAS;AAChD,CAAC,EACD;EACIL,IAAI,EAAE,6BAA6B;EACnCC,SAAS,EAAEL,eAAe;EAC1BM,UAAU,EAAE;IAAEC,IAAI,EAAE,QAAQ;IAAEE,GAAG,EAAE;EAAW;AAClD,CAAC,EACD;EACIL,IAAI,EAAE,kBAAkB;EACxBC,SAAS,EAAER,cAAc;EACzBS,UAAU,EAAE;IAAEC,IAAI,EAAE,QAAQ;IAAEE,GAAG,EAAE;EAAY;AACnD,CAAC,EACD;EACIL,IAAI,EAAE,kCAAkC;EACxCC,SAAS,EAAEL,eAAe;EAC1BM,UAAU,EAAE;IAAEC,IAAI,EAAE,QAAQ;IAAEE,GAAG,EAAE;EAAY;AACnD,CAAC,CACJ;;AAED;AACA,MAAMiC,MAAM,GAAGA,CAAA,kBAAM1J,OAAA,CAACN,aAAa;EAAAqI,QAAA,EAAAC,YAAA;EAAAC,UAAA;EAAAC,YAAA;AAAA,OAAE,CAAC;AAACyB,IAAA,GAAjCD,MAAM;AAEZ,eAAe,SAASE,MAAMA,CAAA,EAAG;EAAAC,EAAA;EACjC,MAAM,CAACtB,IAAI,EAACuB,WAAW,CAAC,GAAG9K,QAAQ,CAAC,EAAE,CAAC;EACnC,MAAM+K,OAAO,GAAGxK,WAAW,CAACC,YAAY,CAACuK,OAAO,CAAC,CAAC,CAAC;EAGnDhL,SAAS,CAAC,MAAM;IACZ,IAAIgL,OAAO,EAAE;MACTD,WAAW,CAACC,OAAO,CAACxB,IAAI,IAAI,EAAE,CAAC;IACnC;EACJ,CAAC,EAAE,CAACwB,OAAO,CAAC,CAAC;EAEb,oBACI/J,OAAA,CAACd,QAAQ;IAAC8K,QAAQ,eAAEhK,OAAA,CAAC0J,MAAM;MAAA3B,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAE;IAAA+B,QAAA,eAC3BjK,OAAA,CAACZ,MAAM;MAAA6K,QAAA,gBAEHjK,OAAA,CAACb,KAAK;QAACiI,IAAI,EAAC,MAAM;QAAA6C,QAAA,eACdjK,OAAA,CAACL,UAAU;UAAAsK,QAAA,eACPjK,OAAA,CAACd,QAAQ;YAAC8K,QAAQ,eAAEhK,OAAA,CAAC0J,MAAM;cAAA3B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAAA+B,QAAA,eAC3BjK,OAAA,CAACZ,MAAM;cAAA6K,QAAA,gBAEHjK,OAAA,CAACb,KAAK;gBAAC+K,KAAK;gBAAC9C,IAAI,EAAC,gBAAgB;gBAACC,SAAS,EAAEjH;cAAU;gBAAA2H,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAG3DlI,OAAA,CAACb,KAAK;gBAAC+K,KAAK;gBAAC9C,IAAI,EAAC,cAAc;gBAACC,SAAS,EAAE9G;cAAQ;gBAAAwH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,EAGtDf,aAAa,CAACgD,GAAG,CAAC,CAACC,KAAK,EAAEC,CAAC,KAAK;gBAC7B;gBACA,IAAID,KAAK,CAAC9C,UAAU,EAAE;kBAClB,oBACItH,OAAA,CAACV,eAAe;oBAEZ4K,KAAK;oBACL9C,IAAI,EAAEgD,KAAK,CAAChD,IAAK;oBACjBC,SAAS,EAAE+C,KAAK,CAAC/C,SAAU;oBAC3BC,UAAU,EAAE8C,KAAK,CAAC9C;kBAAW,GAJxB+C,CAAC;oBAAAtC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAKT,CAAC;gBAEV;;gBAEA;gBACA,oBACIlI,OAAA,CAACb,KAAK;kBAEF+K,KAAK;kBACL9C,IAAI,EAAEgD,KAAK,CAAChD,IAAK;kBACjBC,SAAS,EAAE+C,KAAK,CAAC/C;gBAAU,GAHtBgD,CAAC;kBAAAtC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAIT,CAAC;cAEV,CAAC,CAAC,eAGFlI,OAAA,CAACb,KAAK;gBAACkI,SAAS,EAAExH;cAAS;gBAAAkI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC1B;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC,eAGRlI,OAAA,CAACb,KAAK;QAAC+K,KAAK;QAAC9C,IAAI,EAAC,GAAG;QAAA6C,QAAA,eACjBjK,OAAA,CAACJ,UAAU;UAAAqK,QAAA,eACPjK,OAAA,CAACd,QAAQ;YAAC8K,QAAQ,eAAEhK,OAAA,CAAC0J,MAAM;cAAA3B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAAA+B,QAAA,eAC3BjK,OAAA,CAACZ,MAAM;cAAA6K,QAAA,eACHjK,OAAA,CAACb,KAAK;gBAAC+K,KAAK;gBAAC9C,IAAI,EAAC,GAAG;gBAACC,SAAS,EAAEpH;cAAM;gBAAA8H,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC,eAGRlI,OAAA,CAACb,KAAK;QAACiI,IAAI,EAAC,gBAAgB;QAAA6C,QAAA,eACxBjK,OAAA,CAACL,UAAU;UAAAsK,QAAA,eACPjK,OAAA,CAACF,YAAY;YAAAiI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACR;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC,eAGRlI,OAAA,CAACb,KAAK;QAACiI,IAAI,EAAC,YAAY;QAAA6C,QAAA,eACpBjK,OAAA,CAACL,UAAU;UAAAsK,QAAA,eACPjK,OAAA,CAACH,QAAQ;YAAAkI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC,eAGRlI,OAAA,CAACb,KAAK;QAAA8K,QAAA,eACFjK,OAAA,CAACL,UAAU;UAAAsK,QAAA,eACPjK,OAAA,CAACH,QAAQ;YAAAkI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEnB;AAAC2B,EAAA,CA5FuBD,MAAM;EAAA,QAEVrK,WAAW;AAAA;AAAA+K,IAAA,GAFPV,MAAM;AAAA,IAAA1J,EAAA,EAAAC,GAAA,EAAAE,GAAA,EAAAC,GAAA,EAAAE,GAAA,EAAAC,GAAA,EAAAE,GAAA,EAAAC,GAAA,EAAAE,GAAA,EAAAC,GAAA,EAAAE,GAAA,EAAAC,IAAA,EAAAE,IAAA,EAAAC,IAAA,EAAAE,IAAA,EAAAC,IAAA,EAAAE,IAAA,EAAAC,IAAA,EAAAE,IAAA,EAAAC,IAAA,EAAAE,IAAA,EAAAC,IAAA,EAAAE,IAAA,EAAAC,IAAA,EAAAE,IAAA,EAAAC,IAAA,EAAAE,IAAA,EAAAC,IAAA,EAAAE,IAAA,EAAAC,IAAA,EAAAE,IAAA,EAAAC,IAAA,EAAAE,IAAA,EAAAC,IAAA,EAAAE,IAAA,EAAAC,IAAA,EAAAE,IAAA,EAAAC,IAAA,EAAAE,IAAA,EAAAC,IAAA,EAAAE,IAAA,EAAAC,IAAA,EAAAE,IAAA,EAAAC,IAAA,EAAAE,IAAA,EAAAC,IAAA,EAAAE,IAAA,EAAAC,IAAA,EAAAE,IAAA,EAAAC,IAAA,EAAAE,IAAA,EAAAC,IAAA,EAAAE,IAAA,EAAAC,IAAA,EAAAE,IAAA,EAAAC,IAAA,EAAAE,IAAA,EAAAC,IAAA,EAAAE,IAAA,EAAAC,IAAA,EAAAE,IAAA,EAAAC,IAAA,EAAAE,IAAA,EAAAC,IAAA,EAAAE,IAAA,EAAAC,IAAA,EAAAE,IAAA,EAAAC,IAAA,EAAAE,IAAA,EAAAC,IAAA,EAAAE,IAAA,EAAAC,IAAA,EAAAE,IAAA,EAAAC,IAAA,EAAAE,IAAA,EAAAC,IAAA,EAAAyC,IAAA,EAAAW,IAAA;AAAAC,YAAA,CAAArK,EAAA;AAAAqK,YAAA,CAAApK,GAAA;AAAAoK,YAAA,CAAAlK,GAAA;AAAAkK,YAAA,CAAAjK,GAAA;AAAAiK,YAAA,CAAA/J,GAAA;AAAA+J,YAAA,CAAA9J,GAAA;AAAA8J,YAAA,CAAA5J,GAAA;AAAA4J,YAAA,CAAA3J,GAAA;AAAA2J,YAAA,CAAAzJ,GAAA;AAAAyJ,YAAA,CAAAxJ,GAAA;AAAAwJ,YAAA,CAAAtJ,GAAA;AAAAsJ,YAAA,CAAArJ,IAAA;AAAAqJ,YAAA,CAAAnJ,IAAA;AAAAmJ,YAAA,CAAAlJ,IAAA;AAAAkJ,YAAA,CAAAhJ,IAAA;AAAAgJ,YAAA,CAAA/I,IAAA;AAAA+I,YAAA,CAAA7I,IAAA;AAAA6I,YAAA,CAAA5I,IAAA;AAAA4I,YAAA,CAAA1I,IAAA;AAAA0I,YAAA,CAAAzI,IAAA;AAAAyI,YAAA,CAAAvI,IAAA;AAAAuI,YAAA,CAAAtI,IAAA;AAAAsI,YAAA,CAAApI,IAAA;AAAAoI,YAAA,CAAAnI,IAAA;AAAAmI,YAAA,CAAAjI,IAAA;AAAAiI,YAAA,CAAAhI,IAAA;AAAAgI,YAAA,CAAA9H,IAAA;AAAA8H,YAAA,CAAA7H,IAAA;AAAA6H,YAAA,CAAA3H,IAAA;AAAA2H,YAAA,CAAA1H,IAAA;AAAA0H,YAAA,CAAAxH,IAAA;AAAAwH,YAAA,CAAAvH,IAAA;AAAAuH,YAAA,CAAArH,IAAA;AAAAqH,YAAA,CAAApH,IAAA;AAAAoH,YAAA,CAAAlH,IAAA;AAAAkH,YAAA,CAAAjH,IAAA;AAAAiH,YAAA,CAAA/G,IAAA;AAAA+G,YAAA,CAAA9G,IAAA;AAAA8G,YAAA,CAAA5G,IAAA;AAAA4G,YAAA,CAAA3G,IAAA;AAAA2G,YAAA,CAAAzG,IAAA;AAAAyG,YAAA,CAAAxG,IAAA;AAAAwG,YAAA,CAAAtG,IAAA;AAAAsG,YAAA,CAAArG,IAAA;AAAAqG,YAAA,CAAAnG,IAAA;AAAAmG,YAAA,CAAAlG,IAAA;AAAAkG,YAAA,CAAAhG,IAAA;AAAAgG,YAAA,CAAA/F,IAAA;AAAA+F,YAAA,CAAA7F,IAAA;AAAA6F,YAAA,CAAA5F,IAAA;AAAA4F,YAAA,CAAA1F,IAAA;AAAA0F,YAAA,CAAAzF,IAAA;AAAAyF,YAAA,CAAAvF,IAAA;AAAAuF,YAAA,CAAAtF,IAAA;AAAAsF,YAAA,CAAApF,IAAA;AAAAoF,YAAA,CAAAnF,IAAA;AAAAmF,YAAA,CAAAjF,IAAA;AAAAiF,YAAA,CAAAhF,IAAA;AAAAgF,YAAA,CAAA9E,IAAA;AAAA8E,YAAA,CAAA7E,IAAA;AAAA6E,YAAA,CAAA3E,IAAA;AAAA2E,YAAA,CAAA1E,IAAA;AAAA0E,YAAA,CAAAxE,IAAA;AAAAwE,YAAA,CAAAvE,IAAA;AAAAuE,YAAA,CAAArE,IAAA;AAAAqE,YAAA,CAAApE,IAAA;AAAAoE,YAAA,CAAAlE,IAAA;AAAAkE,YAAA,CAAAjE,IAAA;AAAAiE,YAAA,CAAA/D,IAAA;AAAA+D,YAAA,CAAA9D,IAAA;AAAA8D,YAAA,CAAA5D,IAAA;AAAA4D,YAAA,CAAA3D,IAAA;AAAA2D,YAAA,CAAAzD,IAAA;AAAAyD,YAAA,CAAAxD,IAAA;AAAAwD,YAAA,CAAAtD,IAAA;AAAAsD,YAAA,CAAArD,IAAA;AAAAqD,YAAA,CAAAZ,IAAA;AAAAY,YAAA,CAAAD,IAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}