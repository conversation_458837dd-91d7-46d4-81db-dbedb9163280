{"ast": null, "code": "import { get } from \"../utils/api\";\nconst API_URL = \"https://meraki-backend-main.onrender.com/api\";\nconst GetTimelineRequests = async params => {\n  console.log(\"Get Timeline Reqiests Params \", params);\n  let result = await get(`${API_URL}/timeline/get`, params);\n  if (result) {\n    return result;\n  }\n};\n_c = GetTimelineRequests;\nasync function UpdateTimelineRequest(body) {\n  console.log(\"Update check out styatus \", body);\n  const token = localStorage.getItem(\"merakihr-token\");\n  const result = await fetch(`${API_URL}/timeline/update/${body.id}`, {\n    method: 'PATCH',\n    body: JSON.stringify(body),\n    headers: {\n      Authorization: `Bearer ${token}`,\n      'Content-Type': 'application/json'\n    }\n  });\n  if (result) {\n    return result;\n  }\n}\n_c2 = UpdateTimelineRequest;\nasync function CreateTimelineRequest(params) {\n  console.log(\"Time Line Request Create \", params);\n  const token = localStorage.getItem(\"merakihr-token\");\n  const result = await fetch(`${API_URL}/timeline/create`, {\n    method: 'POST',\n    body: JSON.stringify(params),\n    headers: {\n      Authorization: `Bearer ${token}`,\n      'Content-Type': 'application/json'\n    }\n  });\n  if (result) {\n    return result;\n  }\n}\n_c3 = CreateTimelineRequest;\nasync function GetTimelineRequestByDate(date, id) {\n  console.log(\"Get Timeline Request By Date  \", date);\n  const token = localStorage.getItem(\"merakihr-token\");\n  const result = await fetch(`${API_URL}/timeline/get/${date}/${id}`, {\n    method: 'GET',\n    headers: {\n      Authorization: `Bearer ${token}`,\n      'Content-Type': 'application/json'\n    }\n  });\n  console.log(\"Get Timeline Request By Date Result \", result);\n  if (result) {\n    return result;\n  }\n}\n_c4 = GetTimelineRequestByDate;\nasync function UpdateTaskTimelineRequest(id, taskId, body) {\n  console.log(\"Update Task Timeline Request \", id, body);\n  const token = localStorage.getItem(\"merakihr-token\");\n  const result = await fetch(`${API_URL}/timeline/update-task/${id}/${taskId}`, {\n    method: 'PATCH',\n    body: JSON.stringify(body),\n    headers: {\n      Authorization: `Bearer ${token}`,\n      'Content-Type': 'application/json'\n    }\n  });\n  if (result) {\n    return result;\n  }\n}\n_c5 = UpdateTaskTimelineRequest;\nasync function DeleteTaskTimelineRequest(id, taskId) {\n  console.log(\"Delete Task Timeline Request \", id);\n  const token = localStorage.getItem(\"merakihr-token\");\n  const result = await fetch(`${API_URL}/timeline/delete-task/${id}/${taskId}`, {\n    method: 'DELETE',\n    headers: {\n      Authorization: `Bearer ${token}`,\n      'Content-Type': 'application/json'\n    }\n  });\n  if (result) {\n    return result;\n  }\n}\n_c6 = DeleteTaskTimelineRequest;\nexport const TimelineService = {\n  GetTimelineRequests,\n  UpdateTimelineRequest,\n  CreateTimelineRequest,\n  GetTimelineRequestByDate,\n  UpdateTaskTimelineRequest,\n  DeleteTaskTimelineRequest\n};\nvar _c, _c2, _c3, _c4, _c5, _c6;\n$RefreshReg$(_c, \"GetTimelineRequests\");\n$RefreshReg$(_c2, \"UpdateTimelineRequest\");\n$RefreshReg$(_c3, \"CreateTimelineRequest\");\n$RefreshReg$(_c4, \"GetTimelineRequestByDate\");\n$RefreshReg$(_c5, \"UpdateTaskTimelineRequest\");\n$RefreshReg$(_c6, \"DeleteTaskTimelineRequest\");", "map": {"version": 3, "names": ["get", "API_URL", "GetTimelineRequests", "params", "console", "log", "result", "_c", "UpdateTimelineRequest", "body", "token", "localStorage", "getItem", "fetch", "id", "method", "JSON", "stringify", "headers", "Authorization", "_c2", "CreateTimelineRequest", "_c3", "GetTimelineRequestByDate", "date", "_c4", "UpdateTaskTimelineRequest", "taskId", "_c5", "DeleteTaskTimelineRequest", "_c6", "TimelineService", "$RefreshReg$"], "sources": ["E:/Suraj Patil/Organization_Management_System/meraki-frontend-main/src/services/TimelineService.js"], "sourcesContent": ["import {get} from \"../utils/api\";\r\n\r\nconst API_URL = \"https://meraki-backend-main.onrender.com/api\";\r\n\r\nconst GetTimelineRequests = async (params) => {\r\n    console.log(\"Get Timeline Reqiests Params \",params)\r\n\r\n      let result = await get(`${API_URL}/timeline/get`,params) \r\n\r\n      if(result) {\r\n        return result \r\n   }\r\n    \r\n};\r\n\r\nasync function UpdateTimelineRequest(body) {\r\n    console.log(\"Update check out styatus \",body)\r\n    const token = localStorage.getItem(\"merakihr-token\");\r\n    const result = await fetch(`${API_URL}/timeline/update/${body.id}`,{\r\n        method: 'PATCH',\r\n        body: JSON.stringify(body),\r\n         headers: {\r\n            Authorization: `Bearer ${token}`,\r\n            'Content-Type': 'application/json'\r\n        }\r\n    })\r\n    if(result) {\r\n         return result \r\n    }\r\n\r\n}\r\n\r\nasync function CreateTimelineRequest (params) { \r\n\r\n    console.log(\"Time Line Request Create \",params)\r\n    const token = localStorage.getItem(\"merakihr-token\");\r\n    const result = await fetch(`${API_URL}/timeline/create`, {\r\n        method: 'POST',\r\n         body: JSON.stringify(params),\r\n         headers: {\r\n            Authorization: `Bearer ${token}`,\r\n            'Content-Type': 'application/json'\r\n        }\r\n    })\r\n    if(result) {\r\n         return result \r\n    }\r\n}\r\n\r\nasync function GetTimelineRequestByDate(date,id) {\r\n\r\n    console.log(\"Get Timeline Request By Date  \",date)\r\n\r\n    const token = localStorage.getItem(\"merakihr-token\");\r\n    const result = await fetch(`${API_URL}/timeline/get/${date}/${id}`, {\r\n        method: 'GET',\r\n         headers: {\r\n            Authorization: `Bearer ${token}`,\r\n            'Content-Type': 'application/json'\r\n        }\r\n    })\r\n       console.log(\"Get Timeline Request By Date Result \",result)\r\n    if(result) {\r\n        return result\r\n    }\r\n}  \r\n\r\nasync function UpdateTaskTimelineRequest(id,taskId,body) {\r\n    console.log(\"Update Task Timeline Request \",id,body)\r\n    const token = localStorage.getItem(\"merakihr-token\");\r\n    const result = await fetch(`${API_URL}/timeline/update-task/${id}/${taskId}`,{\r\n        method: 'PATCH',\r\n        body: JSON.stringify(body),\r\n         headers: {\r\n            Authorization: `Bearer ${token}`,\r\n            'Content-Type': 'application/json'\r\n        }\r\n    })\r\n    if(result) {\r\n         return result \r\n    }\r\n}\r\n\r\nasync function DeleteTaskTimelineRequest(id,taskId) {\r\n    console.log(\"Delete Task Timeline Request \",id)\r\n    const token = localStorage.getItem(\"merakihr-token\");\r\n    const result = await fetch(`${API_URL}/timeline/delete-task/${id}/${taskId}`,{\r\n        method: 'DELETE',\r\n         headers: {\r\n            Authorization: `Bearer ${token}`,\r\n            'Content-Type': 'application/json'\r\n        }\r\n    })\r\n    if(result) {\r\n         return result \r\n    }\r\n}\r\n\r\n\r\nexport const TimelineService = {\r\n    GetTimelineRequests,\r\n    UpdateTimelineRequest,\r\n    CreateTimelineRequest,\r\n    GetTimelineRequestByDate,\r\n    UpdateTaskTimelineRequest,\r\n    DeleteTaskTimelineRequest\r\n\r\n};\r\n \r\n"], "mappings": "AAAA,SAAQA,GAAG,QAAO,cAAc;AAEhC,MAAMC,OAAO,GAAG,8CAA8C;AAE9D,MAAMC,mBAAmB,GAAG,MAAOC,MAAM,IAAK;EAC1CC,OAAO,CAACC,GAAG,CAAC,+BAA+B,EAACF,MAAM,CAAC;EAEjD,IAAIG,MAAM,GAAG,MAAMN,GAAG,CAAC,GAAGC,OAAO,eAAe,EAACE,MAAM,CAAC;EAExD,IAAGG,MAAM,EAAE;IACT,OAAOA,MAAM;EAClB;AAEH,CAAC;AAACC,EAAA,GATIL,mBAAmB;AAWzB,eAAeM,qBAAqBA,CAACC,IAAI,EAAE;EACvCL,OAAO,CAACC,GAAG,CAAC,2BAA2B,EAACI,IAAI,CAAC;EAC7C,MAAMC,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,gBAAgB,CAAC;EACpD,MAAMN,MAAM,GAAG,MAAMO,KAAK,CAAC,GAAGZ,OAAO,oBAAoBQ,IAAI,CAACK,EAAE,EAAE,EAAC;IAC/DC,MAAM,EAAE,OAAO;IACfN,IAAI,EAAEO,IAAI,CAACC,SAAS,CAACR,IAAI,CAAC;IACzBS,OAAO,EAAE;MACNC,aAAa,EAAE,UAAUT,KAAK,EAAE;MAChC,cAAc,EAAE;IACpB;EACJ,CAAC,CAAC;EACF,IAAGJ,MAAM,EAAE;IACN,OAAOA,MAAM;EAClB;AAEJ;AAACc,GAAA,GAfcZ,qBAAqB;AAiBpC,eAAea,qBAAqBA,CAAElB,MAAM,EAAE;EAE1CC,OAAO,CAACC,GAAG,CAAC,2BAA2B,EAACF,MAAM,CAAC;EAC/C,MAAMO,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,gBAAgB,CAAC;EACpD,MAAMN,MAAM,GAAG,MAAMO,KAAK,CAAC,GAAGZ,OAAO,kBAAkB,EAAE;IACrDc,MAAM,EAAE,MAAM;IACbN,IAAI,EAAEO,IAAI,CAACC,SAAS,CAACd,MAAM,CAAC;IAC5Be,OAAO,EAAE;MACNC,aAAa,EAAE,UAAUT,KAAK,EAAE;MAChC,cAAc,EAAE;IACpB;EACJ,CAAC,CAAC;EACF,IAAGJ,MAAM,EAAE;IACN,OAAOA,MAAM;EAClB;AACJ;AAACgB,GAAA,GAfcD,qBAAqB;AAiBpC,eAAeE,wBAAwBA,CAACC,IAAI,EAACV,EAAE,EAAE;EAE7CV,OAAO,CAACC,GAAG,CAAC,gCAAgC,EAACmB,IAAI,CAAC;EAElD,MAAMd,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,gBAAgB,CAAC;EACpD,MAAMN,MAAM,GAAG,MAAMO,KAAK,CAAC,GAAGZ,OAAO,iBAAiBuB,IAAI,IAAIV,EAAE,EAAE,EAAE;IAChEC,MAAM,EAAE,KAAK;IACZG,OAAO,EAAE;MACNC,aAAa,EAAE,UAAUT,KAAK,EAAE;MAChC,cAAc,EAAE;IACpB;EACJ,CAAC,CAAC;EACCN,OAAO,CAACC,GAAG,CAAC,sCAAsC,EAACC,MAAM,CAAC;EAC7D,IAAGA,MAAM,EAAE;IACP,OAAOA,MAAM;EACjB;AACJ;AAACmB,GAAA,GAhBcF,wBAAwB;AAkBvC,eAAeG,yBAAyBA,CAACZ,EAAE,EAACa,MAAM,EAAClB,IAAI,EAAE;EACrDL,OAAO,CAACC,GAAG,CAAC,+BAA+B,EAACS,EAAE,EAACL,IAAI,CAAC;EACpD,MAAMC,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,gBAAgB,CAAC;EACpD,MAAMN,MAAM,GAAG,MAAMO,KAAK,CAAC,GAAGZ,OAAO,yBAAyBa,EAAE,IAAIa,MAAM,EAAE,EAAC;IACzEZ,MAAM,EAAE,OAAO;IACfN,IAAI,EAAEO,IAAI,CAACC,SAAS,CAACR,IAAI,CAAC;IACzBS,OAAO,EAAE;MACNC,aAAa,EAAE,UAAUT,KAAK,EAAE;MAChC,cAAc,EAAE;IACpB;EACJ,CAAC,CAAC;EACF,IAAGJ,MAAM,EAAE;IACN,OAAOA,MAAM;EAClB;AACJ;AAACsB,GAAA,GAdcF,yBAAyB;AAgBxC,eAAeG,yBAAyBA,CAACf,EAAE,EAACa,MAAM,EAAE;EAChDvB,OAAO,CAACC,GAAG,CAAC,+BAA+B,EAACS,EAAE,CAAC;EAC/C,MAAMJ,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,gBAAgB,CAAC;EACpD,MAAMN,MAAM,GAAG,MAAMO,KAAK,CAAC,GAAGZ,OAAO,yBAAyBa,EAAE,IAAIa,MAAM,EAAE,EAAC;IACzEZ,MAAM,EAAE,QAAQ;IACfG,OAAO,EAAE;MACNC,aAAa,EAAE,UAAUT,KAAK,EAAE;MAChC,cAAc,EAAE;IACpB;EACJ,CAAC,CAAC;EACF,IAAGJ,MAAM,EAAE;IACN,OAAOA,MAAM;EAClB;AACJ;AAACwB,GAAA,GAbcD,yBAAyB;AAgBxC,OAAO,MAAME,eAAe,GAAG;EAC3B7B,mBAAmB;EACnBM,qBAAqB;EACrBa,qBAAqB;EACrBE,wBAAwB;EACxBG,yBAAyB;EACzBG;AAEJ,CAAC;AAAC,IAAAtB,EAAA,EAAAa,GAAA,EAAAE,GAAA,EAAAG,GAAA,EAAAG,GAAA,EAAAE,GAAA;AAAAE,YAAA,CAAAzB,EAAA;AAAAyB,YAAA,CAAAZ,GAAA;AAAAY,YAAA,CAAAV,GAAA;AAAAU,YAAA,CAAAP,GAAA;AAAAO,YAAA,CAAAJ,GAAA;AAAAI,YAAA,CAAAF,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}