{"ast": null, "code": "var _jsxFileName = \"E:\\\\Suraj Patil\\\\Organization_Management_System\\\\meraki-frontend-main\\\\src\\\\screens\\\\Sprints\\\\components\\\\SprintForm.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport PropTypes from 'prop-types';\nimport { useSelector, useDispatch } from 'react-redux';\nimport { Box, TextField, Button, Grid, FormControl, InputLabel, Select, MenuItem, FormHelperText, Typography } from '@mui/material';\nimport { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFns';\nimport { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';\nimport { DatePicker } from '@mui/x-date-pickers/DatePicker';\nimport { ProductSelector } from '../../../selectors/ProductSelector';\nimport { UserSelector } from '../../../selectors/UserSelector';\nimport { ProductActions } from '../../../slices/actions';\n\n/**\r\n * Sprint Form Component\r\n * \r\n * Form for creating or editing a sprint.\r\n */\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst SprintForm = ({\n  sprint,\n  onSubmit,\n  productId\n}) => {\n  _s();\n  const dispatch = useDispatch();\n  const products = useSelector(ProductSelector.getProducts());\n  const currentUser = useSelector(UserSelector.profile());\n  const isEditing = Boolean(sprint);\n  const [formData, setFormData] = useState({\n    name: '',\n    goal: '',\n    startDate: null,\n    endDate: null,\n    productId: productId || '',\n    status: 'planned'\n  });\n  const [errors, setErrors] = useState({});\n\n  // Fetch products when component mounts\n  useEffect(() => {\n    dispatch(ProductActions.getProducts());\n  }, [dispatch]);\n\n  // Initialize form with sprint data if editing\n  useEffect(() => {\n    if (sprint) {\n      setFormData({\n        name: sprint.name || '',\n        goal: sprint.goal || '',\n        startDate: sprint.startDate ? new Date(sprint.startDate) : null,\n        endDate: sprint.endDate ? new Date(sprint.endDate) : null,\n        productId: sprint.productId || productId || '',\n        status: sprint.status || 'planned'\n      });\n    } else {\n      setFormData({\n        name: '',\n        goal: '',\n        startDate: null,\n        endDate: null,\n        productId: productId || '',\n        status: 'planned'\n      });\n    }\n  }, [sprint, productId]);\n\n  // Handle form field changes\n  const handleChange = e => {\n    const {\n      name,\n      value\n    } = e.target;\n    setFormData({\n      ...formData,\n      [name]: value\n    });\n\n    // Clear error for this field\n    if (errors[name]) {\n      setErrors({\n        ...errors,\n        [name]: null\n      });\n    }\n  };\n\n  // Handle date changes\n  const handleDateChange = (name, date) => {\n    setFormData({\n      ...formData,\n      [name]: date\n    });\n\n    // Clear error for this field\n    if (errors[name]) {\n      setErrors({\n        ...errors,\n        [name]: null\n      });\n    }\n  };\n\n  // Validate form before submission\n  const validateForm = () => {\n    const newErrors = {};\n    if (!formData.name.trim()) {\n      newErrors.name = 'Sprint name is required';\n    }\n    if (!formData.goal.trim()) {\n      newErrors.goal = 'Sprint goal is required';\n    }\n\n    // Only validate product selection for new sprints\n    if (!isEditing && !formData.productId) {\n      newErrors.productId = 'Product is required';\n    }\n    if (!formData.startDate) {\n      newErrors.startDate = 'Start date is required';\n    }\n    if (!formData.endDate) {\n      newErrors.endDate = 'End date is required';\n    } else if (formData.startDate && formData.endDate && formData.endDate < formData.startDate) {\n      newErrors.endDate = 'End date must be after start date';\n    }\n    setErrors(newErrors);\n    return Object.keys(newErrors).length === 0;\n  };\n\n  // Handle form submission\n  const handleSubmit = e => {\n    e.preventDefault();\n    if (validateForm()) {\n      // Format dates for API\n      const formattedData = {\n        ...formData,\n        startDate: formData.startDate ? formData.startDate.toISOString() : null,\n        endDate: formData.endDate ? formData.endDate.toISOString() : null\n      };\n      onSubmit(formattedData);\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(Box, {\n    component: \"form\",\n    onSubmit: handleSubmit,\n    sx: {\n      mt: 2\n    },\n    children: /*#__PURE__*/_jsxDEV(Grid, {\n      container: true,\n      spacing: 3,\n      children: [/*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        md: 6,\n        children: /*#__PURE__*/_jsxDEV(TextField, {\n          fullWidth: true,\n          label: \"Sprint Name\",\n          name: \"name\",\n          value: formData.name,\n          onChange: handleChange,\n          error: Boolean(errors.name),\n          helperText: errors.name,\n          required: true\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 157,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 156,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        md: 6,\n        children: /*#__PURE__*/_jsxDEV(FormControl, {\n          fullWidth: true,\n          error: Boolean(errors.productId),\n          children: [/*#__PURE__*/_jsxDEV(InputLabel, {\n            id: \"product-label\",\n            children: \"Product\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 171,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Select, {\n            labelId: \"product-label\",\n            name: \"productId\",\n            value: formData.productId,\n            onChange: handleChange,\n            label: \"Product\",\n            disabled: isEditing || Boolean(productId) // Disable if editing or productId is provided as prop\n            ,\n            required: !isEditing,\n            children: [/*#__PURE__*/_jsxDEV(MenuItem, {\n              value: \"\",\n              children: \"Select a product\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 181,\n              columnNumber: 15\n            }, this), products && products.map(product => /*#__PURE__*/_jsxDEV(MenuItem, {\n              value: product._id,\n              children: product.productName\n            }, product._id, false, {\n              fileName: _jsxFileName,\n              lineNumber: 183,\n              columnNumber: 17\n            }, this))]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 172,\n            columnNumber: 13\n          }, this), errors.productId && /*#__PURE__*/_jsxDEV(FormHelperText, {\n            children: errors.productId\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 188,\n            columnNumber: 34\n          }, this), isEditing && !errors.productId && /*#__PURE__*/_jsxDEV(FormHelperText, {\n            children: \"Product cannot be changed after sprint creation\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 190,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 170,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 169,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        children: /*#__PURE__*/_jsxDEV(TextField, {\n          fullWidth: true,\n          label: \"Sprint Goal\",\n          name: \"goal\",\n          value: formData.goal,\n          onChange: handleChange,\n          multiline: true,\n          rows: 2,\n          error: Boolean(errors.goal),\n          helpertext: errors.goal,\n          required: true\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 196,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 195,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        children: /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"body2\",\n          color: \"textSecondary\",\n          children: [\"Created by: \", (currentUser === null || currentUser === void 0 ? void 0 : currentUser.name) || 'You']\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 211,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 210,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(LocalizationProvider, {\n        dateAdapter: AdapterDateFns,\n        children: [/*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          md: 6,\n          children: /*#__PURE__*/_jsxDEV(DatePicker, {\n            label: \"Start Date\",\n            value: formData.startDate,\n            onChange: date => handleDateChange('startDate', date),\n            renderInput: params => /*#__PURE__*/_jsxDEV(TextField, {\n              ...params,\n              fullWidth: true,\n              error: Boolean(errors.startDate),\n              helpertext: errors.startDate,\n              required: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 223,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 218,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 217,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          md: 6,\n          children: /*#__PURE__*/_jsxDEV(DatePicker, {\n            label: \"End Date\",\n            value: formData.endDate,\n            onChange: date => handleDateChange('endDate', date),\n            renderInput: params => /*#__PURE__*/_jsxDEV(TextField, {\n              ...params,\n              fullWidth: true,\n              error: Boolean(errors.endDate),\n              helpertext: errors.endDate,\n              required: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 240,\n              columnNumber: 17\n            }, this),\n            minDate: formData.startDate || undefined\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 235,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 234,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 216,\n        columnNumber: 9\n      }, this), sprint && /*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        children: /*#__PURE__*/_jsxDEV(FormControl, {\n          fullWidth: true,\n          children: [/*#__PURE__*/_jsxDEV(InputLabel, {\n            id: \"status-label\",\n            children: \"Status\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 256,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Select, {\n            labelId: \"status-label\",\n            name: \"status\",\n            value: formData.status,\n            onChange: handleChange,\n            label: \"Status\",\n            children: [/*#__PURE__*/_jsxDEV(MenuItem, {\n              value: \"planned\",\n              children: \"Planned\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 264,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n              value: \"active\",\n              children: \"Active\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 265,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n              value: \"completed\",\n              children: \"Completed\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 266,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 257,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 255,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 254,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        children: /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            display: 'flex',\n            justifyContent: 'flex-end'\n          },\n          children: /*#__PURE__*/_jsxDEV(Button, {\n            type: \"submit\",\n            variant: \"contained\",\n            color: \"primary\",\n            sx: {\n              mt: 2\n            },\n            children: sprint ? 'Update Sprint' : 'Create Sprint'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 274,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 273,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 272,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 155,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 154,\n    columnNumber: 5\n  }, this);\n};\n_s(SprintForm, \"9mA2AD0tXpc85RC0M21rbOT3CEs=\", false, function () {\n  return [useDispatch, useSelector, useSelector];\n});\n_c = SprintForm;\nSprintForm.propTypes = {\n  sprint: PropTypes.object,\n  onSubmit: PropTypes.func.isRequired,\n  productId: PropTypes.string\n};\nexport default SprintForm;\nvar _c;\n$RefreshReg$(_c, \"SprintForm\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "PropTypes", "useSelector", "useDispatch", "Box", "TextField", "<PERSON><PERSON>", "Grid", "FormControl", "InputLabel", "Select", "MenuItem", "FormHelperText", "Typography", "AdapterDateFns", "LocalizationProvider", "DatePicker", "ProductSelector", "UserSelector", "ProductActions", "jsxDEV", "_jsxDEV", "SprintForm", "sprint", "onSubmit", "productId", "_s", "dispatch", "products", "getProducts", "currentUser", "profile", "isEditing", "Boolean", "formData", "setFormData", "name", "goal", "startDate", "endDate", "status", "errors", "setErrors", "Date", "handleChange", "e", "value", "target", "handleDateChange", "date", "validateForm", "newErrors", "trim", "Object", "keys", "length", "handleSubmit", "preventDefault", "formattedData", "toISOString", "component", "sx", "mt", "children", "container", "spacing", "item", "xs", "md", "fullWidth", "label", "onChange", "error", "helperText", "required", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "id", "labelId", "disabled", "map", "product", "_id", "productName", "multiline", "rows", "helpertext", "variant", "color", "dateAdapter", "renderInput", "params", "minDate", "undefined", "display", "justifyContent", "type", "_c", "propTypes", "object", "func", "isRequired", "string", "$RefreshReg$"], "sources": ["E:/Suraj Patil/Organization_Management_System/meraki-frontend-main/src/screens/Sprints/components/SprintForm.jsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\r\nimport PropTypes from 'prop-types';\r\nimport { useSelector, useDispatch } from 'react-redux';\r\nimport {\r\n  Box,\r\n  TextField,\r\n  Button,\r\n  Grid,\r\n  FormControl,\r\n  InputLabel,\r\n  Select,\r\n  MenuItem,\r\n  FormHelperText,\r\n  Typography\r\n} from '@mui/material';\r\nimport { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFns';\r\nimport { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';\r\nimport { DatePicker } from '@mui/x-date-pickers/DatePicker';\r\nimport { ProductSelector } from '../../../selectors/ProductSelector';\r\nimport { UserSelector } from '../../../selectors/UserSelector';\r\nimport { ProductActions } from '../../../slices/actions';\r\n\r\n/**\r\n * Sprint Form Component\r\n * \r\n * Form for creating or editing a sprint.\r\n */\r\nconst SprintForm = ({ sprint, onSubmit, productId }) => {\r\n  const dispatch = useDispatch();\r\n  const products = useSelector(ProductSelector.getProducts());\r\n  const currentUser = useSelector(UserSelector.profile());\r\n  const isEditing = Boolean(sprint);\r\n  \r\n  const [formData, setFormData] = useState({\r\n    name: '',\r\n    goal: '',\r\n    startDate: null,\r\n    endDate: null,\r\n    productId: productId || '',\r\n    status: 'planned'\r\n  });\r\n  \r\n  const [errors, setErrors] = useState({});\r\n\r\n  // Fetch products when component mounts\r\n  useEffect(() => {\r\n    dispatch(ProductActions.getProducts());\r\n  }, [dispatch]);\r\n\r\n  // Initialize form with sprint data if editing\r\n  useEffect(() => {\r\n    if (sprint) {\r\n      setFormData({\r\n        name: sprint.name || '',\r\n        goal: sprint.goal || '',\r\n        startDate: sprint.startDate ? new Date(sprint.startDate) : null,\r\n        endDate: sprint.endDate ? new Date(sprint.endDate) : null,\r\n        productId: sprint.productId || productId || '',\r\n        status: sprint.status || 'planned'\r\n      });\r\n    } else {\r\n      setFormData({\r\n        name: '',\r\n        goal: '',\r\n        startDate: null,\r\n        endDate: null,\r\n        productId: productId || '',\r\n        status: 'planned'\r\n      });\r\n    }\r\n  }, [sprint, productId]);\r\n\r\n  // Handle form field changes\r\n  const handleChange = (e) => {\r\n    const { name, value } = e.target;\r\n    setFormData({\r\n      ...formData,\r\n      [name]: value\r\n    });\r\n    \r\n    // Clear error for this field\r\n    if (errors[name]) {\r\n      setErrors({\r\n        ...errors,\r\n        [name]: null\r\n      });\r\n    }\r\n  };\r\n\r\n  // Handle date changes\r\n  const handleDateChange = (name, date) => {\r\n    setFormData({\r\n      ...formData,\r\n      [name]: date\r\n    });\r\n    \r\n    // Clear error for this field\r\n    if (errors[name]) {\r\n      setErrors({\r\n        ...errors,\r\n        [name]: null\r\n      });\r\n    }\r\n  };\r\n\r\n  // Validate form before submission\r\n  const validateForm = () => {\r\n    const newErrors = {};\r\n    \r\n    if (!formData.name.trim()) {\r\n      newErrors.name = 'Sprint name is required';\r\n    }\r\n    \r\n    if (!formData.goal.trim()) {\r\n      newErrors.goal = 'Sprint goal is required';\r\n    }\r\n    \r\n    // Only validate product selection for new sprints\r\n    if (!isEditing && !formData.productId) {\r\n      newErrors.productId = 'Product is required';\r\n    }\r\n    \r\n    if (!formData.startDate) {\r\n      newErrors.startDate = 'Start date is required';\r\n    }\r\n    \r\n    if (!formData.endDate) {\r\n      newErrors.endDate = 'End date is required';\r\n    } else if (formData.startDate && formData.endDate && formData.endDate < formData.startDate) {\r\n      newErrors.endDate = 'End date must be after start date';\r\n    }\r\n    \r\n    setErrors(newErrors);\r\n    return Object.keys(newErrors).length === 0;\r\n  };\r\n\r\n  // Handle form submission\r\n  const handleSubmit = (e) => {\r\n    e.preventDefault();\r\n    \r\n    if (validateForm()) {\r\n      // Format dates for API\r\n      const formattedData = {\r\n        ...formData,\r\n        startDate: formData.startDate ? formData.startDate.toISOString() : null,\r\n        endDate: formData.endDate ? formData.endDate.toISOString() : null\r\n      };\r\n      \r\n      onSubmit(formattedData);\r\n    }\r\n  };\r\n\r\n  return (\r\n    <Box component=\"form\" onSubmit={handleSubmit} sx={{ mt: 2 }}>\r\n      <Grid container spacing={3}>\r\n        <Grid item xs={12} md={6}>\r\n          <TextField\r\n            fullWidth\r\n            label=\"Sprint Name\"\r\n            name=\"name\"\r\n            value={formData.name}\r\n            onChange={handleChange}\r\n            error={Boolean(errors.name)}\r\n            helperText={errors.name}\r\n            required\r\n          />\r\n        </Grid>\r\n        \r\n        <Grid item xs={12} md={6}>\r\n          <FormControl fullWidth error={Boolean(errors.productId)}>\r\n            <InputLabel id=\"product-label\">Product</InputLabel>\r\n            <Select\r\n              labelId=\"product-label\"\r\n              name=\"productId\"\r\n              value={formData.productId}\r\n              onChange={handleChange}\r\n              label=\"Product\"\r\n              disabled={isEditing || Boolean(productId)} // Disable if editing or productId is provided as prop\r\n              required={!isEditing}\r\n            >\r\n              <MenuItem value=\"\">Select a product</MenuItem>\r\n              {products && products.map(product => (\r\n                <MenuItem key={product._id} value={product._id}>\r\n                  {product.productName}\r\n                </MenuItem>\r\n              ))}\r\n            </Select>\r\n            {errors.productId && <FormHelperText>{errors.productId}</FormHelperText>}\r\n            {isEditing && !errors.productId && (\r\n              <FormHelperText>Product cannot be changed after sprint creation</FormHelperText>\r\n            )}\r\n          </FormControl>\r\n        </Grid>\r\n        \r\n        <Grid item xs={12}>\r\n          <TextField\r\n            fullWidth\r\n            label=\"Sprint Goal\"\r\n            name=\"goal\"\r\n            value={formData.goal}\r\n            onChange={handleChange}\r\n            multiline\r\n            rows={2}\r\n            error={Boolean(errors.goal)}\r\n            helpertext={errors.goal}\r\n            required\r\n          />\r\n        </Grid>\r\n        \r\n        <Grid item xs={12}>\r\n          <Typography variant=\"body2\" color=\"textSecondary\">\r\n            Created by: {currentUser?.name || 'You'}\r\n          </Typography>\r\n        </Grid>\r\n        \r\n        <LocalizationProvider dateAdapter={AdapterDateFns}>\r\n          <Grid item xs={12} md={6}>\r\n            <DatePicker\r\n              label=\"Start Date\"\r\n              value={formData.startDate}\r\n              onChange={(date) => handleDateChange('startDate', date)}\r\n              renderInput={(params) => (\r\n                <TextField\r\n                  {...params}\r\n                  fullWidth\r\n                  error={Boolean(errors.startDate)}\r\n                  helpertext={errors.startDate}\r\n                  required\r\n                />\r\n              )}\r\n            />\r\n          </Grid>\r\n          \r\n          <Grid item xs={12} md={6}>\r\n            <DatePicker\r\n              label=\"End Date\"\r\n              value={formData.endDate}\r\n              onChange={(date) => handleDateChange('endDate', date)}\r\n              renderInput={(params) => (\r\n                <TextField\r\n                  {...params}\r\n                  fullWidth\r\n                  error={Boolean(errors.endDate)}\r\n                  helpertext={errors.endDate}\r\n                  required\r\n                />\r\n              )}\r\n              minDate={formData.startDate || undefined}\r\n            />\r\n          </Grid>\r\n        </LocalizationProvider>\r\n        \r\n        {sprint && (\r\n          <Grid item xs={12}>\r\n            <FormControl fullWidth>\r\n              <InputLabel id=\"status-label\">Status</InputLabel>\r\n              <Select\r\n                labelId=\"status-label\"\r\n                name=\"status\"\r\n                value={formData.status}\r\n                onChange={handleChange}\r\n                label=\"Status\"\r\n              >\r\n                <MenuItem value=\"planned\">Planned</MenuItem>\r\n                <MenuItem value=\"active\">Active</MenuItem>\r\n                <MenuItem value=\"completed\">Completed</MenuItem>\r\n              </Select>\r\n            </FormControl>\r\n          </Grid>\r\n        )}\r\n        \r\n        <Grid item xs={12}>\r\n          <Box sx={{ display: 'flex', justifyContent: 'flex-end' }}>\r\n            <Button\r\n              type=\"submit\"\r\n              variant=\"contained\"\r\n              color=\"primary\"\r\n              sx={{ mt: 2 }}\r\n            >\r\n              {sprint ? 'Update Sprint' : 'Create Sprint'}\r\n            </Button>\r\n          </Box>\r\n        </Grid>\r\n      </Grid>\r\n    </Box>\r\n  );\r\n};\r\n\r\nSprintForm.propTypes = {\r\n  sprint: PropTypes.object,\r\n  onSubmit: PropTypes.func.isRequired,\r\n  productId: PropTypes.string\r\n};\r\n\r\nexport default SprintForm;"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,OAAOC,SAAS,MAAM,YAAY;AAClC,SAASC,WAAW,EAAEC,WAAW,QAAQ,aAAa;AACtD,SACEC,GAAG,EACHC,SAAS,EACTC,MAAM,EACNC,IAAI,EACJC,WAAW,EACXC,UAAU,EACVC,MAAM,EACNC,QAAQ,EACRC,cAAc,EACdC,UAAU,QACL,eAAe;AACtB,SAASC,cAAc,QAAQ,oCAAoC;AACnE,SAASC,oBAAoB,QAAQ,0CAA0C;AAC/E,SAASC,UAAU,QAAQ,gCAAgC;AAC3D,SAASC,eAAe,QAAQ,oCAAoC;AACpE,SAASC,YAAY,QAAQ,iCAAiC;AAC9D,SAASC,cAAc,QAAQ,yBAAyB;;AAExD;AACA;AACA;AACA;AACA;AAJA,SAAAC,MAAA,IAAAC,OAAA;AAKA,MAAMC,UAAU,GAAGA,CAAC;EAAEC,MAAM;EAAEC,QAAQ;EAAEC;AAAU,CAAC,KAAK;EAAAC,EAAA;EACtD,MAAMC,QAAQ,GAAGxB,WAAW,CAAC,CAAC;EAC9B,MAAMyB,QAAQ,GAAG1B,WAAW,CAACe,eAAe,CAACY,WAAW,CAAC,CAAC,CAAC;EAC3D,MAAMC,WAAW,GAAG5B,WAAW,CAACgB,YAAY,CAACa,OAAO,CAAC,CAAC,CAAC;EACvD,MAAMC,SAAS,GAAGC,OAAO,CAACV,MAAM,CAAC;EAEjC,MAAM,CAACW,QAAQ,EAAEC,WAAW,CAAC,GAAGpC,QAAQ,CAAC;IACvCqC,IAAI,EAAE,EAAE;IACRC,IAAI,EAAE,EAAE;IACRC,SAAS,EAAE,IAAI;IACfC,OAAO,EAAE,IAAI;IACbd,SAAS,EAAEA,SAAS,IAAI,EAAE;IAC1Be,MAAM,EAAE;EACV,CAAC,CAAC;EAEF,MAAM,CAACC,MAAM,EAAEC,SAAS,CAAC,GAAG3C,QAAQ,CAAC,CAAC,CAAC,CAAC;;EAExC;EACAC,SAAS,CAAC,MAAM;IACd2B,QAAQ,CAACR,cAAc,CAACU,WAAW,CAAC,CAAC,CAAC;EACxC,CAAC,EAAE,CAACF,QAAQ,CAAC,CAAC;;EAEd;EACA3B,SAAS,CAAC,MAAM;IACd,IAAIuB,MAAM,EAAE;MACVY,WAAW,CAAC;QACVC,IAAI,EAAEb,MAAM,CAACa,IAAI,IAAI,EAAE;QACvBC,IAAI,EAAEd,MAAM,CAACc,IAAI,IAAI,EAAE;QACvBC,SAAS,EAAEf,MAAM,CAACe,SAAS,GAAG,IAAIK,IAAI,CAACpB,MAAM,CAACe,SAAS,CAAC,GAAG,IAAI;QAC/DC,OAAO,EAAEhB,MAAM,CAACgB,OAAO,GAAG,IAAII,IAAI,CAACpB,MAAM,CAACgB,OAAO,CAAC,GAAG,IAAI;QACzDd,SAAS,EAAEF,MAAM,CAACE,SAAS,IAAIA,SAAS,IAAI,EAAE;QAC9Ce,MAAM,EAAEjB,MAAM,CAACiB,MAAM,IAAI;MAC3B,CAAC,CAAC;IACJ,CAAC,MAAM;MACLL,WAAW,CAAC;QACVC,IAAI,EAAE,EAAE;QACRC,IAAI,EAAE,EAAE;QACRC,SAAS,EAAE,IAAI;QACfC,OAAO,EAAE,IAAI;QACbd,SAAS,EAAEA,SAAS,IAAI,EAAE;QAC1Be,MAAM,EAAE;MACV,CAAC,CAAC;IACJ;EACF,CAAC,EAAE,CAACjB,MAAM,EAAEE,SAAS,CAAC,CAAC;;EAEvB;EACA,MAAMmB,YAAY,GAAIC,CAAC,IAAK;IAC1B,MAAM;MAAET,IAAI;MAAEU;IAAM,CAAC,GAAGD,CAAC,CAACE,MAAM;IAChCZ,WAAW,CAAC;MACV,GAAGD,QAAQ;MACX,CAACE,IAAI,GAAGU;IACV,CAAC,CAAC;;IAEF;IACA,IAAIL,MAAM,CAACL,IAAI,CAAC,EAAE;MAChBM,SAAS,CAAC;QACR,GAAGD,MAAM;QACT,CAACL,IAAI,GAAG;MACV,CAAC,CAAC;IACJ;EACF,CAAC;;EAED;EACA,MAAMY,gBAAgB,GAAGA,CAACZ,IAAI,EAAEa,IAAI,KAAK;IACvCd,WAAW,CAAC;MACV,GAAGD,QAAQ;MACX,CAACE,IAAI,GAAGa;IACV,CAAC,CAAC;;IAEF;IACA,IAAIR,MAAM,CAACL,IAAI,CAAC,EAAE;MAChBM,SAAS,CAAC;QACR,GAAGD,MAAM;QACT,CAACL,IAAI,GAAG;MACV,CAAC,CAAC;IACJ;EACF,CAAC;;EAED;EACA,MAAMc,YAAY,GAAGA,CAAA,KAAM;IACzB,MAAMC,SAAS,GAAG,CAAC,CAAC;IAEpB,IAAI,CAACjB,QAAQ,CAACE,IAAI,CAACgB,IAAI,CAAC,CAAC,EAAE;MACzBD,SAAS,CAACf,IAAI,GAAG,yBAAyB;IAC5C;IAEA,IAAI,CAACF,QAAQ,CAACG,IAAI,CAACe,IAAI,CAAC,CAAC,EAAE;MACzBD,SAAS,CAACd,IAAI,GAAG,yBAAyB;IAC5C;;IAEA;IACA,IAAI,CAACL,SAAS,IAAI,CAACE,QAAQ,CAACT,SAAS,EAAE;MACrC0B,SAAS,CAAC1B,SAAS,GAAG,qBAAqB;IAC7C;IAEA,IAAI,CAACS,QAAQ,CAACI,SAAS,EAAE;MACvBa,SAAS,CAACb,SAAS,GAAG,wBAAwB;IAChD;IAEA,IAAI,CAACJ,QAAQ,CAACK,OAAO,EAAE;MACrBY,SAAS,CAACZ,OAAO,GAAG,sBAAsB;IAC5C,CAAC,MAAM,IAAIL,QAAQ,CAACI,SAAS,IAAIJ,QAAQ,CAACK,OAAO,IAAIL,QAAQ,CAACK,OAAO,GAAGL,QAAQ,CAACI,SAAS,EAAE;MAC1Fa,SAAS,CAACZ,OAAO,GAAG,mCAAmC;IACzD;IAEAG,SAAS,CAACS,SAAS,CAAC;IACpB,OAAOE,MAAM,CAACC,IAAI,CAACH,SAAS,CAAC,CAACI,MAAM,KAAK,CAAC;EAC5C,CAAC;;EAED;EACA,MAAMC,YAAY,GAAIX,CAAC,IAAK;IAC1BA,CAAC,CAACY,cAAc,CAAC,CAAC;IAElB,IAAIP,YAAY,CAAC,CAAC,EAAE;MAClB;MACA,MAAMQ,aAAa,GAAG;QACpB,GAAGxB,QAAQ;QACXI,SAAS,EAAEJ,QAAQ,CAACI,SAAS,GAAGJ,QAAQ,CAACI,SAAS,CAACqB,WAAW,CAAC,CAAC,GAAG,IAAI;QACvEpB,OAAO,EAAEL,QAAQ,CAACK,OAAO,GAAGL,QAAQ,CAACK,OAAO,CAACoB,WAAW,CAAC,CAAC,GAAG;MAC/D,CAAC;MAEDnC,QAAQ,CAACkC,aAAa,CAAC;IACzB;EACF,CAAC;EAED,oBACErC,OAAA,CAACjB,GAAG;IAACwD,SAAS,EAAC,MAAM;IAACpC,QAAQ,EAAEgC,YAAa;IAACK,EAAE,EAAE;MAAEC,EAAE,EAAE;IAAE,CAAE;IAAAC,QAAA,eAC1D1C,OAAA,CAACd,IAAI;MAACyD,SAAS;MAACC,OAAO,EAAE,CAAE;MAAAF,QAAA,gBACzB1C,OAAA,CAACd,IAAI;QAAC2D,IAAI;QAACC,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAAAL,QAAA,eACvB1C,OAAA,CAAChB,SAAS;UACRgE,SAAS;UACTC,KAAK,EAAC,aAAa;UACnBlC,IAAI,EAAC,MAAM;UACXU,KAAK,EAAEZ,QAAQ,CAACE,IAAK;UACrBmC,QAAQ,EAAE3B,YAAa;UACvB4B,KAAK,EAAEvC,OAAO,CAACQ,MAAM,CAACL,IAAI,CAAE;UAC5BqC,UAAU,EAAEhC,MAAM,CAACL,IAAK;UACxBsC,QAAQ;QAAA;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACT;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eAEPzD,OAAA,CAACd,IAAI;QAAC2D,IAAI;QAACC,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAAAL,QAAA,eACvB1C,OAAA,CAACb,WAAW;UAAC6D,SAAS;UAACG,KAAK,EAAEvC,OAAO,CAACQ,MAAM,CAAChB,SAAS,CAAE;UAAAsC,QAAA,gBACtD1C,OAAA,CAACZ,UAAU;YAACsE,EAAE,EAAC,eAAe;YAAAhB,QAAA,EAAC;UAAO;YAAAY,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACnDzD,OAAA,CAACX,MAAM;YACLsE,OAAO,EAAC,eAAe;YACvB5C,IAAI,EAAC,WAAW;YAChBU,KAAK,EAAEZ,QAAQ,CAACT,SAAU;YAC1B8C,QAAQ,EAAE3B,YAAa;YACvB0B,KAAK,EAAC,SAAS;YACfW,QAAQ,EAAEjD,SAAS,IAAIC,OAAO,CAACR,SAAS,CAAE,CAAC;YAAA;YAC3CiD,QAAQ,EAAE,CAAC1C,SAAU;YAAA+B,QAAA,gBAErB1C,OAAA,CAACV,QAAQ;cAACmC,KAAK,EAAC,EAAE;cAAAiB,QAAA,EAAC;YAAgB;cAAAY,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAU,CAAC,EAC7ClD,QAAQ,IAAIA,QAAQ,CAACsD,GAAG,CAACC,OAAO,iBAC/B9D,OAAA,CAACV,QAAQ;cAAmBmC,KAAK,EAAEqC,OAAO,CAACC,GAAI;cAAArB,QAAA,EAC5CoB,OAAO,CAACE;YAAW,GADPF,OAAO,CAACC,GAAG;cAAAT,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAEhB,CACX,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI,CAAC,EACRrC,MAAM,CAAChB,SAAS,iBAAIJ,OAAA,CAACT,cAAc;YAAAmD,QAAA,EAAEtB,MAAM,CAAChB;UAAS;YAAAkD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAiB,CAAC,EACvE9C,SAAS,IAAI,CAACS,MAAM,CAAChB,SAAS,iBAC7BJ,OAAA,CAACT,cAAc;YAAAmD,QAAA,EAAC;UAA+C;YAAAY,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAgB,CAChF;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACU;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC,eAEPzD,OAAA,CAACd,IAAI;QAAC2D,IAAI;QAACC,EAAE,EAAE,EAAG;QAAAJ,QAAA,eAChB1C,OAAA,CAAChB,SAAS;UACRgE,SAAS;UACTC,KAAK,EAAC,aAAa;UACnBlC,IAAI,EAAC,MAAM;UACXU,KAAK,EAAEZ,QAAQ,CAACG,IAAK;UACrBkC,QAAQ,EAAE3B,YAAa;UACvB0C,SAAS;UACTC,IAAI,EAAE,CAAE;UACRf,KAAK,EAAEvC,OAAO,CAACQ,MAAM,CAACJ,IAAI,CAAE;UAC5BmD,UAAU,EAAE/C,MAAM,CAACJ,IAAK;UACxBqC,QAAQ;QAAA;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACT;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eAEPzD,OAAA,CAACd,IAAI;QAAC2D,IAAI;QAACC,EAAE,EAAE,EAAG;QAAAJ,QAAA,eAChB1C,OAAA,CAACR,UAAU;UAAC4E,OAAO,EAAC,OAAO;UAACC,KAAK,EAAC,eAAe;UAAA3B,QAAA,GAAC,cACpC,EAAC,CAAAjC,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAEM,IAAI,KAAI,KAAK;QAAA;UAAAuC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7B;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACT,CAAC,eAEPzD,OAAA,CAACN,oBAAoB;QAAC4E,WAAW,EAAE7E,cAAe;QAAAiD,QAAA,gBAChD1C,OAAA,CAACd,IAAI;UAAC2D,IAAI;UAACC,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,CAAE;UAAAL,QAAA,eACvB1C,OAAA,CAACL,UAAU;YACTsD,KAAK,EAAC,YAAY;YAClBxB,KAAK,EAAEZ,QAAQ,CAACI,SAAU;YAC1BiC,QAAQ,EAAGtB,IAAI,IAAKD,gBAAgB,CAAC,WAAW,EAAEC,IAAI,CAAE;YACxD2C,WAAW,EAAGC,MAAM,iBAClBxE,OAAA,CAAChB,SAAS;cAAA,GACJwF,MAAM;cACVxB,SAAS;cACTG,KAAK,EAAEvC,OAAO,CAACQ,MAAM,CAACH,SAAS,CAAE;cACjCkD,UAAU,EAAE/C,MAAM,CAACH,SAAU;cAC7BoC,QAAQ;YAAA;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT;UACD;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,eAEPzD,OAAA,CAACd,IAAI;UAAC2D,IAAI;UAACC,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,CAAE;UAAAL,QAAA,eACvB1C,OAAA,CAACL,UAAU;YACTsD,KAAK,EAAC,UAAU;YAChBxB,KAAK,EAAEZ,QAAQ,CAACK,OAAQ;YACxBgC,QAAQ,EAAGtB,IAAI,IAAKD,gBAAgB,CAAC,SAAS,EAAEC,IAAI,CAAE;YACtD2C,WAAW,EAAGC,MAAM,iBAClBxE,OAAA,CAAChB,SAAS;cAAA,GACJwF,MAAM;cACVxB,SAAS;cACTG,KAAK,EAAEvC,OAAO,CAACQ,MAAM,CAACF,OAAO,CAAE;cAC/BiD,UAAU,EAAE/C,MAAM,CAACF,OAAQ;cAC3BmC,QAAQ;YAAA;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CACD;YACFgB,OAAO,EAAE5D,QAAQ,CAACI,SAAS,IAAIyD;UAAU;YAAApB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1C;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACa,CAAC,EAEtBvD,MAAM,iBACLF,OAAA,CAACd,IAAI;QAAC2D,IAAI;QAACC,EAAE,EAAE,EAAG;QAAAJ,QAAA,eAChB1C,OAAA,CAACb,WAAW;UAAC6D,SAAS;UAAAN,QAAA,gBACpB1C,OAAA,CAACZ,UAAU;YAACsE,EAAE,EAAC,cAAc;YAAAhB,QAAA,EAAC;UAAM;YAAAY,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACjDzD,OAAA,CAACX,MAAM;YACLsE,OAAO,EAAC,cAAc;YACtB5C,IAAI,EAAC,QAAQ;YACbU,KAAK,EAAEZ,QAAQ,CAACM,MAAO;YACvB+B,QAAQ,EAAE3B,YAAa;YACvB0B,KAAK,EAAC,QAAQ;YAAAP,QAAA,gBAEd1C,OAAA,CAACV,QAAQ;cAACmC,KAAK,EAAC,SAAS;cAAAiB,QAAA,EAAC;YAAO;cAAAY,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAU,CAAC,eAC5CzD,OAAA,CAACV,QAAQ;cAACmC,KAAK,EAAC,QAAQ;cAAAiB,QAAA,EAAC;YAAM;cAAAY,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAU,CAAC,eAC1CzD,OAAA,CAACV,QAAQ;cAACmC,KAAK,EAAC,WAAW;cAAAiB,QAAA,EAAC;YAAS;cAAAY,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAU,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1C,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CACP,eAEDzD,OAAA,CAACd,IAAI;QAAC2D,IAAI;QAACC,EAAE,EAAE,EAAG;QAAAJ,QAAA,eAChB1C,OAAA,CAACjB,GAAG;UAACyD,EAAE,EAAE;YAAEmC,OAAO,EAAE,MAAM;YAAEC,cAAc,EAAE;UAAW,CAAE;UAAAlC,QAAA,eACvD1C,OAAA,CAACf,MAAM;YACL4F,IAAI,EAAC,QAAQ;YACbT,OAAO,EAAC,WAAW;YACnBC,KAAK,EAAC,SAAS;YACf7B,EAAE,EAAE;cAAEC,EAAE,EAAE;YAAE,CAAE;YAAAC,QAAA,EAEbxC,MAAM,GAAG,eAAe,GAAG;UAAe;YAAAoD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACJ,CAAC;AAEV,CAAC;AAACpD,EAAA,CAnQIJ,UAAU;EAAA,QACGnB,WAAW,EACXD,WAAW,EACRA,WAAW;AAAA;AAAAiG,EAAA,GAH3B7E,UAAU;AAqQhBA,UAAU,CAAC8E,SAAS,GAAG;EACrB7E,MAAM,EAAEtB,SAAS,CAACoG,MAAM;EACxB7E,QAAQ,EAAEvB,SAAS,CAACqG,IAAI,CAACC,UAAU;EACnC9E,SAAS,EAAExB,SAAS,CAACuG;AACvB,CAAC;AAED,eAAelF,UAAU;AAAC,IAAA6E,EAAA;AAAAM,YAAA,CAAAN,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}