{"ast": null, "code": "const API_URL = \"https://meraki-backend-main.onrender.com/api\";\nasync function createProduct(params) {\n  const token = localStorage.getItem(\"merakihr-token\");\n  console.log(\"Create Product Service \", params);\n  // Extract the data property if it exists (for when called from saga)\n  const data = params.data || params;\n  console.log(\"Sending data to backend:\", data);\n  try {\n    // Check if we need to add this product to a sprint\n    if (data.sprintId && data.addToSprint) {\n      const result = await fetch(`${API_URL}/product/create`, {\n        method: 'POST',\n        body: JSON.stringify(data),\n        headers: {\n          Authorization: `Bearer ${token}`,\n          'Content-Type': 'application/json'\n        }\n      });\n      if (!result.ok) {\n        throw new Error('Failed to create product');\n      }\n      const productData = await result.json();\n      const productId = productData.data._id;\n\n      // Now add the product to the sprint\n      await fetch(`${API_URL}/sprint/add-project`, {\n        method: 'POST',\n        body: JSON.stringify({\n          productId: productId,\n          sprintId: data.sprintId\n        }),\n        headers: {\n          Authorization: `Bearer ${token}`,\n          'Content-Type': 'application/json'\n        }\n      });\n      return result;\n    } else {\n      // Regular product creation without sprint\n      const result = await fetch(`${API_URL}/product/create`, {\n        method: 'POST',\n        body: JSON.stringify(data),\n        headers: {\n          Authorization: `Bearer ${token}`,\n          'Content-Type': 'application/json'\n        }\n      });\n      return result;\n    }\n  } catch (error) {\n    console.error(\"Error creating product:\", error);\n    throw error;\n  }\n}\nasync function updateProduct(id, body) {\n  const token = localStorage.getItem(\"merakihr-token\");\n  const result = await fetch(`${API_URL}/product/update/${id}`, {\n    method: 'PATCH',\n    body: JSON.stringify(body),\n    headers: {\n      Authorization: `Bearer ${token}`,\n      'Content-Type': 'application/json'\n    }\n  });\n  return result;\n}\nasync function deleteProduct(id) {\n  const token = localStorage.getItem(\"merakihr-token\");\n  const result = await fetch(`${API_URL}/product/delete/${id}`, {\n    method: 'DELETE',\n    headers: {\n      Authorization: `Bearer ${token}`,\n      'Content-Type': 'application/json'\n    }\n  });\n  return result;\n}\nasync function getProductByUserId(id) {\n  const token = localStorage.getItem(\"merakihr-token\");\n  const result = await fetch(`${API_URL}/product/${id}`, {\n    method: 'GET',\n    headers: {\n      Authorization: `Bearer ${token}`,\n      'Content-Type': 'application/json'\n    }\n  });\n  return result;\n}\nasync function getProducts(filter) {\n  const token = localStorage.getItem(\"merakihr-token\");\n  console.log(\"Fetching products... Token:\", token);\n  console.log(\"Filter:\", filter);\n  try {\n    // Build query string from filter if provided\n    let url = `${API_URL}/product`;\n    if (filter) {\n      const queryParams = new URLSearchParams();\n      if (filter.page) {\n        queryParams.append('page', filter.page);\n      }\n      if (filter.limit) {\n        queryParams.append('limit', filter.limit);\n      }\n      if (filter.sort) {\n        queryParams.append('sort', filter.sort);\n      }\n      const queryString = queryParams.toString();\n      if (queryString) {\n        url += `?${queryString}`;\n      }\n    }\n    console.log(\"Fetching from URL:\", url);\n    const response = await fetch(url, {\n      method: 'GET',\n      headers: {\n        Authorization: `Bearer ${token}`,\n        'Content-Type': 'application/json'\n      }\n    });\n    console.log(\"Response Status:\", response.status);\n    console.log(\"Response Headers:\", response.headers);\n    const data = await response.json();\n    console.log(\"API Response Data:\", data);\n    return data;\n  } catch (error) {\n    console.error(\"API Fetch Error:\", error);\n    return {\n      pagination: {},\n      data: []\n    }; // Return empty data on error\n  }\n}\nasync function createProductsTask(id, data) {\n  const token = localStorage.getItem(\"merakihr-token\");\n  console.log(\"Create ProductsTask Service \", id, data);\n  try {\n    // Create the task\n    const result = await fetch(`${API_URL}/product/create/task/${id}`, {\n      method: 'PATCH',\n      body: JSON.stringify(data),\n      headers: {\n        Authorization: `Bearer ${token}`,\n        'Content-Type': 'application/json'\n      }\n    });\n    if (!result.ok) {\n      throw new Error('Failed to create task');\n    }\n    const taskData = await result.json();\n\n    // If sprintId is provided, add the task to the sprint\n    if (data.sprintId && data.addToSprint) {\n      const taskId = taskData.data._id;\n      await fetch(`${API_URL}/sprint/add-task`, {\n        method: 'POST',\n        body: JSON.stringify({\n          productId: id,\n          taskId: taskId,\n          sprintId: data.sprintId\n        }),\n        headers: {\n          Authorization: `Bearer ${token}`,\n          'Content-Type': 'application/json'\n        }\n      });\n    }\n    return result;\n  } catch (error) {\n    console.error(\"Error creating task:\", error);\n    throw error;\n  }\n}\nasync function getProductById(id) {\n  const token = localStorage.getItem(\"merakihr-token\");\n  console.log(\"Get Product By Id \", id);\n  const result = await fetch(`${API_URL}/product/${id}`, {\n    method: 'GET',\n    headers: {\n      Authorization: `Bearer ${token}`,\n      'Content-Type': 'application/json'\n    }\n  });\n  return result;\n}\nasync function getProductsByUser(id) {\n  const token = localStorage.getItem(\"merakihr-token\");\n  console.log(\"Get Products By User \", id);\n  const result = await fetch(`${API_URL}/product/user/${id}`, {\n    method: 'GET',\n    headers: {\n      Authorization: `Bearer ${token}`,\n      'Content-Type': 'application/json'\n    }\n  });\n  return result;\n}\nasync function updateTask(productId, taskId, data) {\n  const token = localStorage.getItem(\"merakihr-token\");\n  console.log(\"Update Task \", productId, taskId, data);\n  try {\n    const result = await fetch(`${API_URL}/product/update/task/${productId}/${taskId}`, {\n      method: 'PATCH',\n      body: JSON.stringify(data),\n      headers: {\n        Authorization: `Bearer ${token}`,\n        'Content-Type': 'application/json'\n      }\n    });\n    if (!result.ok) {\n      const error = await result.json();\n      console.error(\"Error updating task:\", error);\n      throw new Error(`Failed to update task: ${error.message || result.statusText}`);\n    }\n    const responseData = await result.json();\n    console.log('Response Data:', responseData);\n    return responseData; // Returning the response if it's valid\n  } catch (error) {\n    console.error(\"Error in updateTask function:\", error);\n    throw error; // Rethrow the error for higher-level handling\n  }\n}\n\n// Start Task function\nasync function startTask(payload) {\n  const token = localStorage.getItem(\"merakihr-token\");\n  const {\n    taskId,\n    projectId,\n    date\n  } = payload;\n  console.log(\"Start Task Service\", projectId, taskId, \"Date:\", date);\n  try {\n    const result = await fetch(`${API_URL}/product/start-task/${projectId}/${taskId}`, {\n      method: 'PATCH',\n      body: JSON.stringify({\n        date\n      }),\n      headers: {\n        Authorization: `Bearer ${token}`,\n        'Content-Type': 'application/json'\n      }\n    });\n    if (!result.ok) {\n      const error = await result.json();\n      throw new Error(`Failed to start task: ${error.message || result.statusText}`);\n    }\n    return await result.json();\n  } catch (error) {\n    console.error(\"Error in startTask function:\", error);\n    throw error;\n  }\n}\n\n// Stop Task function\nasync function stopTask(payload) {\n  const token = localStorage.getItem(\"merakihr-token\");\n  const {\n    taskId,\n    projectId,\n    elapsedTime,\n    date,\n    pauseHistory\n  } = payload;\n  console.log(\"Stop Task Service\", projectId, taskId, \"Elapsed time:\", elapsedTime, \"Date:\", date);\n  try {\n    const result = await fetch(`${API_URL}/product/stop-task/${projectId}/${taskId}`, {\n      method: 'PATCH',\n      body: JSON.stringify({\n        elapsedTime,\n        date,\n        pauseHistory\n      }),\n      headers: {\n        Authorization: `Bearer ${token}`,\n        'Content-Type': 'application/json'\n      }\n    });\n    if (!result.ok) {\n      const error = await result.json();\n      throw new Error(`Failed to stop task: ${error.message || result.statusText}`);\n    }\n    return await result.json();\n  } catch (error) {\n    console.error(\"Error in stopTask function:\", error);\n    throw error;\n  }\n}\n\n// Pause Task function\nasync function pauseTask(payload) {\n  const token = localStorage.getItem(\"merakihr-token\");\n  const {\n    taskId,\n    projectId,\n    elapsedTime,\n    pauseTime,\n    date,\n    startTime\n  } = payload;\n  console.log(\"Pause Task Service\", projectId, taskId, \"Elapsed time:\", elapsedTime, \"Date:\", date);\n  try {\n    const result = await fetch(`${API_URL}/product/pause-task/${projectId}/${taskId}`, {\n      method: 'PATCH',\n      body: JSON.stringify({\n        elapsedTime,\n        pauseTime: pauseTime || new Date().toISOString(),\n        date,\n        startTime\n      }),\n      headers: {\n        Authorization: `Bearer ${token}`,\n        'Content-Type': 'application/json'\n      }\n    });\n    if (!result.ok) {\n      const error = await result.json();\n      throw new Error(`Failed to pause task: ${error.message || result.statusText}`);\n    }\n    return await result.json();\n  } catch (error) {\n    console.error(\"Error in pauseTask function:\", error);\n    throw error;\n  }\n}\nasync function getOnGoingProductsTasksToday() {\n  const token = localStorage.getItem(\"merakihr-token\");\n  const result = await fetch(`${API_URL}/product/ongoing/today`, {\n    method: 'GET',\n    headers: {\n      Authorization: `Bearer ${token}`,\n      'Content-Type': 'application/json'\n    }\n  });\n  return result;\n}\nexport const ProductService = {\n  createProduct,\n  updateProduct,\n  deleteProduct,\n  getProductByUserId,\n  getProducts,\n  createProductsTask,\n  getProductById,\n  getProductsByUser,\n  updateTask,\n  startTask,\n  stopTask,\n  getOnGoingProductsTasksToday,\n  pauseTask\n};", "map": {"version": 3, "names": ["API_URL", "createProduct", "params", "token", "localStorage", "getItem", "console", "log", "data", "sprintId", "addToSprint", "result", "fetch", "method", "body", "JSON", "stringify", "headers", "Authorization", "ok", "Error", "productData", "json", "productId", "_id", "error", "updateProduct", "id", "deleteProduct", "getProductByUserId", "getProducts", "filter", "url", "queryParams", "URLSearchParams", "page", "append", "limit", "sort", "queryString", "toString", "response", "status", "pagination", "createProductsTask", "taskData", "taskId", "getProductById", "getProductsByUser", "updateTask", "message", "statusText", "responseData", "startTask", "payload", "projectId", "date", "stopTask", "elapsedTime", "pauseHistory", "pauseTask", "pauseTime", "startTime", "Date", "toISOString", "getOnGoingProductsTasksToday", "ProductService"], "sources": ["E:/Suraj Patil/Organization_Management_System/meraki-frontend-main/src/services/ProductService.js"], "sourcesContent": ["\r\n\r\nconst API_URL = \"https://meraki-backend-main.onrender.com/api\";\r\n\r\nasync function createProduct(params) {\r\n    const token = localStorage.getItem(\"merakihr-token\");\r\n    console.log(\"Create Product Service \", params);\r\n    // Extract the data property if it exists (for when called from saga)\r\n    const data = params.data || params;\r\n    console.log(\"Sending data to backend:\", data);\r\n    \r\n    try {\r\n        // Check if we need to add this product to a sprint\r\n        if (data.sprintId && data.addToSprint) {\r\n            const result = await fetch(`${API_URL}/product/create`, {\r\n                method: 'POST',\r\n                body: JSON.stringify(data),\r\n                headers: {\r\n                    Authorization: `Bearer ${token}`,\r\n                    'Content-Type': 'application/json'\r\n                }\r\n            });\r\n            \r\n            if (!result.ok) {\r\n                throw new Error('Failed to create product');\r\n            }\r\n            \r\n            const productData = await result.json();\r\n            const productId = productData.data._id;\r\n            \r\n            // Now add the product to the sprint\r\n            await fetch(`${API_URL}/sprint/add-project`, {\r\n                method: 'POST',\r\n                body: JSON.stringify({\r\n                    productId: productId,\r\n                    sprintId: data.sprintId\r\n                }),\r\n                headers: {\r\n                    Authorization: `Bearer ${token}`,\r\n                    'Content-Type': 'application/json'\r\n                }\r\n            });\r\n            \r\n            return result;\r\n        } else {\r\n            // Regular product creation without sprint\r\n            const result = await fetch(`${API_URL}/product/create`, {\r\n                method: 'POST',\r\n                body: JSON.stringify(data),\r\n                headers: {\r\n                    Authorization: `Bearer ${token}`,\r\n                    'Content-Type': 'application/json'\r\n                }\r\n            });\r\n            return result;\r\n        }\r\n    } catch (error) {\r\n        console.error(\"Error creating product:\", error);\r\n        throw error;\r\n    }\r\n}\r\n\r\nasync function updateProduct(id, body) {\r\n    const token = localStorage.getItem(\"merakihr-token\");\r\n    const result = await fetch(`${API_URL}/product/update/${id}`, {\r\n        method: 'PATCH',\r\n        body: JSON.stringify(body),\r\n        headers: {\r\n            Authorization: `Bearer ${token}`,\r\n            'Content-Type': 'application/json'\r\n        }\r\n    });\r\n    return result;\r\n}\r\n\r\nasync function deleteProduct(id) {\r\n    const token = localStorage.getItem(\"merakihr-token\");\r\n    const result = await fetch(`${API_URL}/product/delete/${id}`, {\r\n        method: 'DELETE',\r\n        headers: {\r\n            Authorization: `Bearer ${token}`,\r\n            'Content-Type': 'application/json'\r\n        }\r\n    });\r\n    return result;\r\n}\r\n\r\nasync function getProductByUserId(id) {\r\n    const token = localStorage.getItem(\"merakihr-token\");\r\n    const result = await fetch(`${API_URL}/product/${id}`, {\r\n        method: 'GET',\r\n        headers: {\r\n            Authorization: `Bearer ${token}`,\r\n            'Content-Type': 'application/json'\r\n        }\r\n    });\r\n    return result;\r\n}\r\n\r\nasync function getProducts(filter) {\r\n    const token = localStorage.getItem(\"merakihr-token\");\r\n    console.log(\"Fetching products... Token:\", token);\r\n    console.log(\"Filter:\", filter);\r\n\r\n    try {\r\n        // Build query string from filter if provided\r\n        let url = `${API_URL}/product`;\r\n        if (filter) {\r\n            const queryParams = new URLSearchParams();\r\n            if (filter.page) {\r\n                queryParams.append('page', filter.page);\r\n            }\r\n            if (filter.limit) {\r\n                queryParams.append('limit', filter.limit);\r\n            }\r\n            if (filter.sort) {\r\n                queryParams.append('sort', filter.sort);\r\n            }\r\n\r\n            const queryString = queryParams.toString();\r\n            if (queryString) {\r\n                url += `?${queryString}`;\r\n            }\r\n        }\r\n\r\n        console.log(\"Fetching from URL:\", url);\r\n\r\n        const response = await fetch(url, {\r\n            method: 'GET',\r\n            headers: {\r\n                Authorization: `Bearer ${token}`,\r\n                'Content-Type': 'application/json'\r\n            }\r\n        });\r\n\r\n        console.log(\"Response Status:\", response.status);\r\n        console.log(\"Response Headers:\", response.headers);\r\n\r\n        const data = await response.json();\r\n        console.log(\"API Response Data:\", data);\r\n\r\n        return data;\r\n    } catch (error) {\r\n        console.error(\"API Fetch Error:\", error);\r\n        return { pagination: {}, data: [] }; // Return empty data on error\r\n    }\r\n}\r\n\r\nasync function createProductsTask(id, data) {\r\n    const token = localStorage.getItem(\"merakihr-token\");\r\n    console.log(\"Create ProductsTask Service \", id, data);\r\n    \r\n    try {\r\n        // Create the task\r\n        const result = await fetch(`${API_URL}/product/create/task/${id}`, {\r\n            method: 'PATCH',\r\n            body: JSON.stringify(data),\r\n            headers: {\r\n                Authorization: `Bearer ${token}`,\r\n                'Content-Type': 'application/json'\r\n            }\r\n        });\r\n        \r\n        if (!result.ok) {\r\n            throw new Error('Failed to create task');\r\n        }\r\n        \r\n        const taskData = await result.json();\r\n        \r\n        // If sprintId is provided, add the task to the sprint\r\n        if (data.sprintId && data.addToSprint) {\r\n            const taskId = taskData.data._id;\r\n            \r\n            await fetch(`${API_URL}/sprint/add-task`, {\r\n                method: 'POST',\r\n                body: JSON.stringify({\r\n                    productId: id,\r\n                    taskId: taskId,\r\n                    sprintId: data.sprintId\r\n                }),\r\n                headers: {\r\n                    Authorization: `Bearer ${token}`,\r\n                    'Content-Type': 'application/json'\r\n                }\r\n            });\r\n        }\r\n        \r\n        return result;\r\n    } catch (error) {\r\n        console.error(\"Error creating task:\", error);\r\n        throw error;\r\n    }\r\n}\r\n\r\nasync function getProductById(id) {\r\n    const token = localStorage.getItem(\"merakihr-token\");\r\n    console.log(\"Get Product By Id \", id);\r\n    const result = await fetch(`${API_URL}/product/${id}`, {\r\n        method: 'GET',\r\n        headers: {\r\n            Authorization: `Bearer ${token}`,\r\n            'Content-Type': 'application/json'\r\n        }\r\n    });\r\n    return result;\r\n}\r\n\r\nasync function getProductsByUser(id) {\r\n    const token = localStorage.getItem(\"merakihr-token\");\r\n    console.log(\"Get Products By User \", id);\r\n    const result = await fetch(`${API_URL}/product/user/${id}`, {\r\n        method: 'GET',\r\n        headers: {\r\n            Authorization: `Bearer ${token}`,\r\n            'Content-Type': 'application/json'\r\n        }\r\n    });\r\n    return result;\r\n}\r\n\r\nasync function updateTask(productId, taskId, data) {\r\n    const token = localStorage.getItem(\"merakihr-token\");\r\n    console.log(\"Update Task \", productId, taskId, data);\r\n\r\n    try {\r\n        const result = await fetch(`${API_URL}/product/update/task/${productId}/${taskId}`, {\r\n            method: 'PATCH',\r\n            body: JSON.stringify(data),\r\n            headers: {\r\n                Authorization: `Bearer ${token}`,\r\n                'Content-Type': 'application/json'\r\n            }\r\n        });\r\n\r\n        if (!result.ok) {\r\n            const error = await result.json();\r\n            console.error(\"Error updating task:\", error);\r\n            throw new Error(`Failed to update task: ${error.message || result.statusText}`);\r\n        }\r\n\r\n        const responseData = await result.json();\r\n        console.log('Response Data:', responseData);\r\n        return responseData; // Returning the response if it's valid\r\n    } catch (error) {\r\n        console.error(\"Error in updateTask function:\", error);\r\n        throw error; // Rethrow the error for higher-level handling\r\n    }\r\n}\r\n\r\n// Start Task function\r\nasync function startTask(payload) {\r\n    const token = localStorage.getItem(\"merakihr-token\");\r\n    const { taskId, projectId, date } = payload;\r\n    console.log(\"Start Task Service\", projectId, taskId, \"Date:\", date);\r\n    \r\n    try {\r\n        const result = await fetch(`${API_URL}/product/start-task/${projectId}/${taskId}`, {\r\n            method: 'PATCH',\r\n            body: JSON.stringify({ date }),\r\n            headers: {\r\n                Authorization: `Bearer ${token}`,\r\n                'Content-Type': 'application/json'\r\n            }\r\n        });\r\n        \r\n        if (!result.ok) {\r\n            const error = await result.json();\r\n            throw new Error(`Failed to start task: ${error.message || result.statusText}`);\r\n        }\r\n        \r\n        return await result.json();\r\n    } catch (error) {\r\n        console.error(\"Error in startTask function:\", error);\r\n        throw error;\r\n    }\r\n}\r\n\r\n// Stop Task function\r\nasync function stopTask(payload) {\r\n    const token = localStorage.getItem(\"merakihr-token\");\r\n    const { taskId, projectId, elapsedTime, date, pauseHistory } = payload;\r\n    console.log(\"Stop Task Service\", projectId, taskId, \"Elapsed time:\", elapsedTime, \"Date:\", date);\r\n    \r\n    try {\r\n        const result = await fetch(`${API_URL}/product/stop-task/${projectId}/${taskId}`, {\r\n            method: 'PATCH',\r\n            body: JSON.stringify({ \r\n                elapsedTime,\r\n                date,\r\n                pauseHistory\r\n            }),\r\n            headers: {\r\n                Authorization: `Bearer ${token}`,\r\n                'Content-Type': 'application/json'\r\n            }\r\n        });\r\n        \r\n        if (!result.ok) {\r\n            const error = await result.json();\r\n            throw new Error(`Failed to stop task: ${error.message || result.statusText}`);\r\n        }\r\n        \r\n        return await result.json();\r\n    } catch (error) {\r\n        console.error(\"Error in stopTask function:\", error);\r\n        throw error;\r\n    }\r\n}\r\n\r\n// Pause Task function\r\nasync function pauseTask(payload) {\r\n    const token = localStorage.getItem(\"merakihr-token\");\r\n    const { taskId, projectId, elapsedTime, pauseTime, date, startTime } = payload;\r\n    console.log(\"Pause Task Service\", projectId, taskId, \"Elapsed time:\", elapsedTime, \"Date:\", date);\r\n    \r\n    try {\r\n        const result = await fetch(`${API_URL}/product/pause-task/${projectId}/${taskId}`, {\r\n            method: 'PATCH',\r\n            body: JSON.stringify({ \r\n                elapsedTime,\r\n                pauseTime: pauseTime || new Date().toISOString(),\r\n                date,\r\n                startTime\r\n            }),\r\n            headers: {\r\n                Authorization: `Bearer ${token}`,\r\n                'Content-Type': 'application/json'\r\n            }\r\n        });\r\n        \r\n        if (!result.ok) {\r\n            const error = await result.json();\r\n            throw new Error(`Failed to pause task: ${error.message || result.statusText}`);\r\n        }\r\n        \r\n        return await result.json();\r\n    } catch (error) {\r\n        console.error(\"Error in pauseTask function:\", error);\r\n        throw error;\r\n    }\r\n}\r\n\r\nasync function getOnGoingProductsTasksToday() {\r\n    const token = localStorage.getItem(\"merakihr-token\");\r\n    const result = await fetch(`${API_URL}/product/ongoing/today`, {\r\n        method: 'GET',\r\n        headers: {\r\n            Authorization: `Bearer ${token}`,\r\n            'Content-Type': 'application/json'\r\n        }\r\n    });\r\n    return result;\r\n}\r\n\r\nexport const ProductService = {\r\n    createProduct,\r\n    updateProduct,\r\n    deleteProduct,\r\n    getProductByUserId,\r\n    getProducts,\r\n    createProductsTask,\r\n    getProductById,\r\n    getProductsByUser,\r\n    updateTask,\r\n    startTask,\r\n    stopTask,\r\n    getOnGoingProductsTasksToday,\r\n    pauseTask\r\n};"], "mappings": "AAEA,MAAMA,OAAO,GAAG,8CAA8C;AAE9D,eAAeC,aAAaA,CAACC,MAAM,EAAE;EACjC,MAAMC,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,gBAAgB,CAAC;EACpDC,OAAO,CAACC,GAAG,CAAC,yBAAyB,EAAEL,MAAM,CAAC;EAC9C;EACA,MAAMM,IAAI,GAAGN,MAAM,CAACM,IAAI,IAAIN,MAAM;EAClCI,OAAO,CAACC,GAAG,CAAC,0BAA0B,EAAEC,IAAI,CAAC;EAE7C,IAAI;IACA;IACA,IAAIA,IAAI,CAACC,QAAQ,IAAID,IAAI,CAACE,WAAW,EAAE;MACnC,MAAMC,MAAM,GAAG,MAAMC,KAAK,CAAC,GAAGZ,OAAO,iBAAiB,EAAE;QACpDa,MAAM,EAAE,MAAM;QACdC,IAAI,EAAEC,IAAI,CAACC,SAAS,CAACR,IAAI,CAAC;QAC1BS,OAAO,EAAE;UACLC,aAAa,EAAE,UAAUf,KAAK,EAAE;UAChC,cAAc,EAAE;QACpB;MACJ,CAAC,CAAC;MAEF,IAAI,CAACQ,MAAM,CAACQ,EAAE,EAAE;QACZ,MAAM,IAAIC,KAAK,CAAC,0BAA0B,CAAC;MAC/C;MAEA,MAAMC,WAAW,GAAG,MAAMV,MAAM,CAACW,IAAI,CAAC,CAAC;MACvC,MAAMC,SAAS,GAAGF,WAAW,CAACb,IAAI,CAACgB,GAAG;;MAEtC;MACA,MAAMZ,KAAK,CAAC,GAAGZ,OAAO,qBAAqB,EAAE;QACzCa,MAAM,EAAE,MAAM;QACdC,IAAI,EAAEC,IAAI,CAACC,SAAS,CAAC;UACjBO,SAAS,EAAEA,SAAS;UACpBd,QAAQ,EAAED,IAAI,CAACC;QACnB,CAAC,CAAC;QACFQ,OAAO,EAAE;UACLC,aAAa,EAAE,UAAUf,KAAK,EAAE;UAChC,cAAc,EAAE;QACpB;MACJ,CAAC,CAAC;MAEF,OAAOQ,MAAM;IACjB,CAAC,MAAM;MACH;MACA,MAAMA,MAAM,GAAG,MAAMC,KAAK,CAAC,GAAGZ,OAAO,iBAAiB,EAAE;QACpDa,MAAM,EAAE,MAAM;QACdC,IAAI,EAAEC,IAAI,CAACC,SAAS,CAACR,IAAI,CAAC;QAC1BS,OAAO,EAAE;UACLC,aAAa,EAAE,UAAUf,KAAK,EAAE;UAChC,cAAc,EAAE;QACpB;MACJ,CAAC,CAAC;MACF,OAAOQ,MAAM;IACjB;EACJ,CAAC,CAAC,OAAOc,KAAK,EAAE;IACZnB,OAAO,CAACmB,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;IAC/C,MAAMA,KAAK;EACf;AACJ;AAEA,eAAeC,aAAaA,CAACC,EAAE,EAAEb,IAAI,EAAE;EACnC,MAAMX,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,gBAAgB,CAAC;EACpD,MAAMM,MAAM,GAAG,MAAMC,KAAK,CAAC,GAAGZ,OAAO,mBAAmB2B,EAAE,EAAE,EAAE;IAC1Dd,MAAM,EAAE,OAAO;IACfC,IAAI,EAAEC,IAAI,CAACC,SAAS,CAACF,IAAI,CAAC;IAC1BG,OAAO,EAAE;MACLC,aAAa,EAAE,UAAUf,KAAK,EAAE;MAChC,cAAc,EAAE;IACpB;EACJ,CAAC,CAAC;EACF,OAAOQ,MAAM;AACjB;AAEA,eAAeiB,aAAaA,CAACD,EAAE,EAAE;EAC7B,MAAMxB,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,gBAAgB,CAAC;EACpD,MAAMM,MAAM,GAAG,MAAMC,KAAK,CAAC,GAAGZ,OAAO,mBAAmB2B,EAAE,EAAE,EAAE;IAC1Dd,MAAM,EAAE,QAAQ;IAChBI,OAAO,EAAE;MACLC,aAAa,EAAE,UAAUf,KAAK,EAAE;MAChC,cAAc,EAAE;IACpB;EACJ,CAAC,CAAC;EACF,OAAOQ,MAAM;AACjB;AAEA,eAAekB,kBAAkBA,CAACF,EAAE,EAAE;EAClC,MAAMxB,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,gBAAgB,CAAC;EACpD,MAAMM,MAAM,GAAG,MAAMC,KAAK,CAAC,GAAGZ,OAAO,YAAY2B,EAAE,EAAE,EAAE;IACnDd,MAAM,EAAE,KAAK;IACbI,OAAO,EAAE;MACLC,aAAa,EAAE,UAAUf,KAAK,EAAE;MAChC,cAAc,EAAE;IACpB;EACJ,CAAC,CAAC;EACF,OAAOQ,MAAM;AACjB;AAEA,eAAemB,WAAWA,CAACC,MAAM,EAAE;EAC/B,MAAM5B,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,gBAAgB,CAAC;EACpDC,OAAO,CAACC,GAAG,CAAC,6BAA6B,EAAEJ,KAAK,CAAC;EACjDG,OAAO,CAACC,GAAG,CAAC,SAAS,EAAEwB,MAAM,CAAC;EAE9B,IAAI;IACA;IACA,IAAIC,GAAG,GAAG,GAAGhC,OAAO,UAAU;IAC9B,IAAI+B,MAAM,EAAE;MACR,MAAME,WAAW,GAAG,IAAIC,eAAe,CAAC,CAAC;MACzC,IAAIH,MAAM,CAACI,IAAI,EAAE;QACbF,WAAW,CAACG,MAAM,CAAC,MAAM,EAAEL,MAAM,CAACI,IAAI,CAAC;MAC3C;MACA,IAAIJ,MAAM,CAACM,KAAK,EAAE;QACdJ,WAAW,CAACG,MAAM,CAAC,OAAO,EAAEL,MAAM,CAACM,KAAK,CAAC;MAC7C;MACA,IAAIN,MAAM,CAACO,IAAI,EAAE;QACbL,WAAW,CAACG,MAAM,CAAC,MAAM,EAAEL,MAAM,CAACO,IAAI,CAAC;MAC3C;MAEA,MAAMC,WAAW,GAAGN,WAAW,CAACO,QAAQ,CAAC,CAAC;MAC1C,IAAID,WAAW,EAAE;QACbP,GAAG,IAAI,IAAIO,WAAW,EAAE;MAC5B;IACJ;IAEAjC,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAEyB,GAAG,CAAC;IAEtC,MAAMS,QAAQ,GAAG,MAAM7B,KAAK,CAACoB,GAAG,EAAE;MAC9BnB,MAAM,EAAE,KAAK;MACbI,OAAO,EAAE;QACLC,aAAa,EAAE,UAAUf,KAAK,EAAE;QAChC,cAAc,EAAE;MACpB;IACJ,CAAC,CAAC;IAEFG,OAAO,CAACC,GAAG,CAAC,kBAAkB,EAAEkC,QAAQ,CAACC,MAAM,CAAC;IAChDpC,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAEkC,QAAQ,CAACxB,OAAO,CAAC;IAElD,MAAMT,IAAI,GAAG,MAAMiC,QAAQ,CAACnB,IAAI,CAAC,CAAC;IAClChB,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAEC,IAAI,CAAC;IAEvC,OAAOA,IAAI;EACf,CAAC,CAAC,OAAOiB,KAAK,EAAE;IACZnB,OAAO,CAACmB,KAAK,CAAC,kBAAkB,EAAEA,KAAK,CAAC;IACxC,OAAO;MAAEkB,UAAU,EAAE,CAAC,CAAC;MAAEnC,IAAI,EAAE;IAAG,CAAC,CAAC,CAAC;EACzC;AACJ;AAEA,eAAeoC,kBAAkBA,CAACjB,EAAE,EAAEnB,IAAI,EAAE;EACxC,MAAML,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,gBAAgB,CAAC;EACpDC,OAAO,CAACC,GAAG,CAAC,8BAA8B,EAAEoB,EAAE,EAAEnB,IAAI,CAAC;EAErD,IAAI;IACA;IACA,MAAMG,MAAM,GAAG,MAAMC,KAAK,CAAC,GAAGZ,OAAO,wBAAwB2B,EAAE,EAAE,EAAE;MAC/Dd,MAAM,EAAE,OAAO;MACfC,IAAI,EAAEC,IAAI,CAACC,SAAS,CAACR,IAAI,CAAC;MAC1BS,OAAO,EAAE;QACLC,aAAa,EAAE,UAAUf,KAAK,EAAE;QAChC,cAAc,EAAE;MACpB;IACJ,CAAC,CAAC;IAEF,IAAI,CAACQ,MAAM,CAACQ,EAAE,EAAE;MACZ,MAAM,IAAIC,KAAK,CAAC,uBAAuB,CAAC;IAC5C;IAEA,MAAMyB,QAAQ,GAAG,MAAMlC,MAAM,CAACW,IAAI,CAAC,CAAC;;IAEpC;IACA,IAAId,IAAI,CAACC,QAAQ,IAAID,IAAI,CAACE,WAAW,EAAE;MACnC,MAAMoC,MAAM,GAAGD,QAAQ,CAACrC,IAAI,CAACgB,GAAG;MAEhC,MAAMZ,KAAK,CAAC,GAAGZ,OAAO,kBAAkB,EAAE;QACtCa,MAAM,EAAE,MAAM;QACdC,IAAI,EAAEC,IAAI,CAACC,SAAS,CAAC;UACjBO,SAAS,EAAEI,EAAE;UACbmB,MAAM,EAAEA,MAAM;UACdrC,QAAQ,EAAED,IAAI,CAACC;QACnB,CAAC,CAAC;QACFQ,OAAO,EAAE;UACLC,aAAa,EAAE,UAAUf,KAAK,EAAE;UAChC,cAAc,EAAE;QACpB;MACJ,CAAC,CAAC;IACN;IAEA,OAAOQ,MAAM;EACjB,CAAC,CAAC,OAAOc,KAAK,EAAE;IACZnB,OAAO,CAACmB,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;IAC5C,MAAMA,KAAK;EACf;AACJ;AAEA,eAAesB,cAAcA,CAACpB,EAAE,EAAE;EAC9B,MAAMxB,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,gBAAgB,CAAC;EACpDC,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAEoB,EAAE,CAAC;EACrC,MAAMhB,MAAM,GAAG,MAAMC,KAAK,CAAC,GAAGZ,OAAO,YAAY2B,EAAE,EAAE,EAAE;IACnDd,MAAM,EAAE,KAAK;IACbI,OAAO,EAAE;MACLC,aAAa,EAAE,UAAUf,KAAK,EAAE;MAChC,cAAc,EAAE;IACpB;EACJ,CAAC,CAAC;EACF,OAAOQ,MAAM;AACjB;AAEA,eAAeqC,iBAAiBA,CAACrB,EAAE,EAAE;EACjC,MAAMxB,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,gBAAgB,CAAC;EACpDC,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAEoB,EAAE,CAAC;EACxC,MAAMhB,MAAM,GAAG,MAAMC,KAAK,CAAC,GAAGZ,OAAO,iBAAiB2B,EAAE,EAAE,EAAE;IACxDd,MAAM,EAAE,KAAK;IACbI,OAAO,EAAE;MACLC,aAAa,EAAE,UAAUf,KAAK,EAAE;MAChC,cAAc,EAAE;IACpB;EACJ,CAAC,CAAC;EACF,OAAOQ,MAAM;AACjB;AAEA,eAAesC,UAAUA,CAAC1B,SAAS,EAAEuB,MAAM,EAAEtC,IAAI,EAAE;EAC/C,MAAML,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,gBAAgB,CAAC;EACpDC,OAAO,CAACC,GAAG,CAAC,cAAc,EAAEgB,SAAS,EAAEuB,MAAM,EAAEtC,IAAI,CAAC;EAEpD,IAAI;IACA,MAAMG,MAAM,GAAG,MAAMC,KAAK,CAAC,GAAGZ,OAAO,wBAAwBuB,SAAS,IAAIuB,MAAM,EAAE,EAAE;MAChFjC,MAAM,EAAE,OAAO;MACfC,IAAI,EAAEC,IAAI,CAACC,SAAS,CAACR,IAAI,CAAC;MAC1BS,OAAO,EAAE;QACLC,aAAa,EAAE,UAAUf,KAAK,EAAE;QAChC,cAAc,EAAE;MACpB;IACJ,CAAC,CAAC;IAEF,IAAI,CAACQ,MAAM,CAACQ,EAAE,EAAE;MACZ,MAAMM,KAAK,GAAG,MAAMd,MAAM,CAACW,IAAI,CAAC,CAAC;MACjChB,OAAO,CAACmB,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;MAC5C,MAAM,IAAIL,KAAK,CAAC,0BAA0BK,KAAK,CAACyB,OAAO,IAAIvC,MAAM,CAACwC,UAAU,EAAE,CAAC;IACnF;IAEA,MAAMC,YAAY,GAAG,MAAMzC,MAAM,CAACW,IAAI,CAAC,CAAC;IACxChB,OAAO,CAACC,GAAG,CAAC,gBAAgB,EAAE6C,YAAY,CAAC;IAC3C,OAAOA,YAAY,CAAC,CAAC;EACzB,CAAC,CAAC,OAAO3B,KAAK,EAAE;IACZnB,OAAO,CAACmB,KAAK,CAAC,+BAA+B,EAAEA,KAAK,CAAC;IACrD,MAAMA,KAAK,CAAC,CAAC;EACjB;AACJ;;AAEA;AACA,eAAe4B,SAASA,CAACC,OAAO,EAAE;EAC9B,MAAMnD,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,gBAAgB,CAAC;EACpD,MAAM;IAAEyC,MAAM;IAAES,SAAS;IAAEC;EAAK,CAAC,GAAGF,OAAO;EAC3ChD,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAEgD,SAAS,EAAET,MAAM,EAAE,OAAO,EAAEU,IAAI,CAAC;EAEnE,IAAI;IACA,MAAM7C,MAAM,GAAG,MAAMC,KAAK,CAAC,GAAGZ,OAAO,uBAAuBuD,SAAS,IAAIT,MAAM,EAAE,EAAE;MAC/EjC,MAAM,EAAE,OAAO;MACfC,IAAI,EAAEC,IAAI,CAACC,SAAS,CAAC;QAAEwC;MAAK,CAAC,CAAC;MAC9BvC,OAAO,EAAE;QACLC,aAAa,EAAE,UAAUf,KAAK,EAAE;QAChC,cAAc,EAAE;MACpB;IACJ,CAAC,CAAC;IAEF,IAAI,CAACQ,MAAM,CAACQ,EAAE,EAAE;MACZ,MAAMM,KAAK,GAAG,MAAMd,MAAM,CAACW,IAAI,CAAC,CAAC;MACjC,MAAM,IAAIF,KAAK,CAAC,yBAAyBK,KAAK,CAACyB,OAAO,IAAIvC,MAAM,CAACwC,UAAU,EAAE,CAAC;IAClF;IAEA,OAAO,MAAMxC,MAAM,CAACW,IAAI,CAAC,CAAC;EAC9B,CAAC,CAAC,OAAOG,KAAK,EAAE;IACZnB,OAAO,CAACmB,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;IACpD,MAAMA,KAAK;EACf;AACJ;;AAEA;AACA,eAAegC,QAAQA,CAACH,OAAO,EAAE;EAC7B,MAAMnD,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,gBAAgB,CAAC;EACpD,MAAM;IAAEyC,MAAM;IAAES,SAAS;IAAEG,WAAW;IAAEF,IAAI;IAAEG;EAAa,CAAC,GAAGL,OAAO;EACtEhD,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAEgD,SAAS,EAAET,MAAM,EAAE,eAAe,EAAEY,WAAW,EAAE,OAAO,EAAEF,IAAI,CAAC;EAEhG,IAAI;IACA,MAAM7C,MAAM,GAAG,MAAMC,KAAK,CAAC,GAAGZ,OAAO,sBAAsBuD,SAAS,IAAIT,MAAM,EAAE,EAAE;MAC9EjC,MAAM,EAAE,OAAO;MACfC,IAAI,EAAEC,IAAI,CAACC,SAAS,CAAC;QACjB0C,WAAW;QACXF,IAAI;QACJG;MACJ,CAAC,CAAC;MACF1C,OAAO,EAAE;QACLC,aAAa,EAAE,UAAUf,KAAK,EAAE;QAChC,cAAc,EAAE;MACpB;IACJ,CAAC,CAAC;IAEF,IAAI,CAACQ,MAAM,CAACQ,EAAE,EAAE;MACZ,MAAMM,KAAK,GAAG,MAAMd,MAAM,CAACW,IAAI,CAAC,CAAC;MACjC,MAAM,IAAIF,KAAK,CAAC,wBAAwBK,KAAK,CAACyB,OAAO,IAAIvC,MAAM,CAACwC,UAAU,EAAE,CAAC;IACjF;IAEA,OAAO,MAAMxC,MAAM,CAACW,IAAI,CAAC,CAAC;EAC9B,CAAC,CAAC,OAAOG,KAAK,EAAE;IACZnB,OAAO,CAACmB,KAAK,CAAC,6BAA6B,EAAEA,KAAK,CAAC;IACnD,MAAMA,KAAK;EACf;AACJ;;AAEA;AACA,eAAemC,SAASA,CAACN,OAAO,EAAE;EAC9B,MAAMnD,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,gBAAgB,CAAC;EACpD,MAAM;IAAEyC,MAAM;IAAES,SAAS;IAAEG,WAAW;IAAEG,SAAS;IAAEL,IAAI;IAAEM;EAAU,CAAC,GAAGR,OAAO;EAC9EhD,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAEgD,SAAS,EAAET,MAAM,EAAE,eAAe,EAAEY,WAAW,EAAE,OAAO,EAAEF,IAAI,CAAC;EAEjG,IAAI;IACA,MAAM7C,MAAM,GAAG,MAAMC,KAAK,CAAC,GAAGZ,OAAO,uBAAuBuD,SAAS,IAAIT,MAAM,EAAE,EAAE;MAC/EjC,MAAM,EAAE,OAAO;MACfC,IAAI,EAAEC,IAAI,CAACC,SAAS,CAAC;QACjB0C,WAAW;QACXG,SAAS,EAAEA,SAAS,IAAI,IAAIE,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC;QAChDR,IAAI;QACJM;MACJ,CAAC,CAAC;MACF7C,OAAO,EAAE;QACLC,aAAa,EAAE,UAAUf,KAAK,EAAE;QAChC,cAAc,EAAE;MACpB;IACJ,CAAC,CAAC;IAEF,IAAI,CAACQ,MAAM,CAACQ,EAAE,EAAE;MACZ,MAAMM,KAAK,GAAG,MAAMd,MAAM,CAACW,IAAI,CAAC,CAAC;MACjC,MAAM,IAAIF,KAAK,CAAC,yBAAyBK,KAAK,CAACyB,OAAO,IAAIvC,MAAM,CAACwC,UAAU,EAAE,CAAC;IAClF;IAEA,OAAO,MAAMxC,MAAM,CAACW,IAAI,CAAC,CAAC;EAC9B,CAAC,CAAC,OAAOG,KAAK,EAAE;IACZnB,OAAO,CAACmB,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;IACpD,MAAMA,KAAK;EACf;AACJ;AAEA,eAAewC,4BAA4BA,CAAA,EAAG;EAC1C,MAAM9D,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,gBAAgB,CAAC;EACpD,MAAMM,MAAM,GAAG,MAAMC,KAAK,CAAC,GAAGZ,OAAO,wBAAwB,EAAE;IAC3Da,MAAM,EAAE,KAAK;IACbI,OAAO,EAAE;MACLC,aAAa,EAAE,UAAUf,KAAK,EAAE;MAChC,cAAc,EAAE;IACpB;EACJ,CAAC,CAAC;EACF,OAAOQ,MAAM;AACjB;AAEA,OAAO,MAAMuD,cAAc,GAAG;EAC1BjE,aAAa;EACbyB,aAAa;EACbE,aAAa;EACbC,kBAAkB;EAClBC,WAAW;EACXc,kBAAkB;EAClBG,cAAc;EACdC,iBAAiB;EACjBC,UAAU;EACVI,SAAS;EACTI,QAAQ;EACRQ,4BAA4B;EAC5BL;AACJ,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}