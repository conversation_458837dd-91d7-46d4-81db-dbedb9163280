import React, { useState, useEffect } from 'react';
import {
  Avatar,
  Box,
  Typography,
  Button
} from '@mui/material';
import AddIcon from '@mui/icons-material/Add';
import dayjs from 'dayjs';
import PropTypes from 'prop-types';
import { useDispatch, useSelector } from 'react-redux';
import { UserActions } from 'slices/actions';
import { UserSelector } from 'selectors';
import WorkScheduleForm from './WorkScheduleForm';

const hours = Array.from({ length: 24 }, (_, i) => `${i}:00`);

const SLOT_WIDTH = 60;
const USER_WIDTH = 200;
const ROW_HEIGHT = 60;

const DayWorkSchedule = ({ dateRange }) => {
  const dispatch = useDispatch();
  const users = useSelector(UserSelector.getUsers());
  const [open, setOpen] = useState(false);
  const [selectedUser, setSelectedUser] = useState(null);

  // Get the current date from dateRange or default to today
  const currentDate = dateRange?.startDate ? dayjs(dateRange.startDate) : dayjs();

  // Fetch users when component mounts
  useEffect(() => {
    dispatch(UserActions.getUsers());
  }, [dispatch]);

  const handleAddSchedule = () => {
    setOpen(true);
  };

  const handleClose = () => {
    setOpen(false);
    setSelectedUser(null);
  };

  return (
    <Box>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
        <Typography variant="h5">
          {currentDate.format("dddd, MMMM D, YYYY")}
        </Typography>
        <Button
          variant="contained"
          size="small"
          startIcon={<AddIcon />}
          onClick={handleAddSchedule}
          sx={{ bgcolor: '#1976d2' }}
        >
          Add Schedule
        </Button>
      </Box>

      {/* Schedule Display */}
      <Box
        sx={{
          width: '100%',
          overflowX: 'auto',
          border: '1px solid #ccc',
          borderRadius: 1,
          '&::-webkit-scrollbar': {
            height: 8
          },
          '&::-webkit-scrollbar-thumb': {
            backgroundColor: '#999',
            borderRadius: 4
          }
        }}
      >
        <Box sx={{ minWidth: `${USER_WIDTH + (hours.length * SLOT_WIDTH)}px` }}>
          {/* Header */}
          <Box sx={{ display: 'flex', position: 'sticky', top: 0, zIndex: 2 }}>
            <Box
              sx={{
                width: USER_WIDTH,
                height: ROW_HEIGHT,
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                fontWeight: 600,
                fontSize: 13,
                borderRight: '1px solid #ccc',
                borderBottom: '1px solid #ccc',
                backgroundColor: '#f0f0f0',
                position: 'sticky',
                left: 0,
                zIndex: 3
              }}
            >
              User
            </Box>
            {hours.map((hour, idx) => (
              <Box
                key={idx}
                sx={{
                  width: SLOT_WIDTH,
                  height: ROW_HEIGHT,
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  fontSize: 12,
                  fontWeight: 600,
                  borderRight: '1px solid #ccc',
                  borderBottom: '1px solid #ccc',
                  backgroundColor: '#f0f0f0'
                }}
              >
                {hour}
              </Box>
            ))}
          </Box>

          {/* User Rows */}
          <Box sx={{ maxHeight: 400, overflowY: 'auto' }}>
            {users.map((user, uIdx) => (
              <Box key={uIdx} sx={{ display: 'flex' }}>
                {/* User Info */}
                <Box
                  sx={{
                    width: USER_WIDTH,
                    minHeight: ROW_HEIGHT,
                    display: 'flex',
                    alignItems: 'center',
                    gap: 1,
                    px: 2,
                    backgroundColor: '#fff',
                    borderRight: '1px solid #eee',
                    borderBottom: '1px solid #eee',
                    position: 'sticky',
                    left: 0,
                    zIndex: 1
                  }}
                >
                  <Avatar sx={{ width: 32, height: 32 }}>
                    {user.name ? user.name[0].toUpperCase() : 'U'}
                  </Avatar>
                  <Box>
                    <Typography fontWeight={600} fontSize={13}>{user.name || 'Unknown User'}</Typography>
                    <Typography variant="caption" color="text.secondary">
                      {user.designation?.name || user.role || 'No Role'}
                    </Typography>
                  </Box>
                </Box>

                {/* Time Slots */}
                {hours.map((hour, hIdx) => {
                  // Check if user has work schedule for the current date
                  const hasSchedule = user.workSchedule &&
                    user.workSchedule.startTime &&
                    user.workSchedule.endTime &&
                    user.workSchedule.shiftStart &&
                    user.workSchedule.shiftEnd;

                  // Check if the current date falls within the schedule date range
                  const currentDateStr = currentDate.format('YYYY-MM-DD');
                  const shiftStartDate = hasSchedule ? dayjs(user.workSchedule.shiftStart).format('YYYY-MM-DD') : null;
                  const shiftEndDate = hasSchedule ? dayjs(user.workSchedule.shiftEnd).format('YYYY-MM-DD') : null;

                  const isScheduledDate = hasSchedule &&
                    currentDateStr >= shiftStartDate &&
                    currentDateStr <= shiftEndDate;

                  // Debug logging (remove in production)
                  if (hasSchedule && hIdx === 0) {
                    console.log(`User: ${user.name}, Current Date: ${currentDateStr}, Shift Start: ${shiftStartDate}, Shift End: ${shiftEndDate}, Is Scheduled: ${isScheduledDate}`);
                  }

                  // Check if this hour falls within user's work schedule
                  const hourNum = parseInt(hour.split(':')[0], 10);
                  const startHour = isScheduledDate ? parseInt(user.workSchedule.startTime.split(':')[0], 10) : null;
                  const endHour = isScheduledDate ? parseInt(user.workSchedule.endTime.split(':')[0], 10) : null;
                  const isWorkingHour = isScheduledDate && hourNum >= startHour && hourNum < endHour;

                  return (
                    <Box
                      key={hIdx}
                      sx={{
                        width: SLOT_WIDTH,
                        height: ROW_HEIGHT,
                        borderRight: '1px solid #eee',
                        borderBottom: '1px solid #eee',
                        position: 'relative',
                        backgroundColor: isWorkingHour ? '#4caf50' : '#fafafa',
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center'
                      }}
                    >
                      {/* Show schedule time range if it's working hour */}
                      {isWorkingHour && (
                        <Typography
                          variant="caption"
                          sx={{
                            fontSize: '8px',
                            color: 'white',
                            fontWeight: 600,
                            textAlign: 'center',
                            lineHeight: 1
                          }}
                        >
                          {user.workSchedule.startTime}-{user.workSchedule.endTime}
                        </Typography>
                      )}
                    </Box>
                  );
                })}
              </Box>
            ))}
          </Box>
        </Box>
      </Box>

      {/* Work Schedule Form */}
      <WorkScheduleForm
        open={open}
        onClose={handleClose}
        selectedUser={selectedUser}
        selectedDate={currentDate.format('YYYY-MM-DD')}
      />
    </Box>
  );
};

DayWorkSchedule.propTypes = {
  dateRange: PropTypes.shape({
    startDate: PropTypes.string,
    endDate: PropTypes.string
  })
};

export default DayWorkSchedule;
