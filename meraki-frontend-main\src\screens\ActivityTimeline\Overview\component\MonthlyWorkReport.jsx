import React, { useEffect } from "react";
import PropTypes from "prop-types";
import {
  Box,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Typography,
  Paper,
  LinearProgress,
  Avatar
} from "@mui/material";
import { useSelector, useDispatch } from "react-redux";
import { format, parseISO, getDaysInMonth } from "date-fns";
import { ActivityActions } from "../../../../slices/actions";

// Helper function to parse time strings like "8h 30m" to minutes
const parseTimeToMinutes = (timeStr) => {
  if (!timeStr || timeStr === "--" || timeStr === "Holiday" || timeStr === "-") {
    return 0;
  }

  // Use named capture groups to extract hours and minutes
  const hourMatch = timeStr.match(/(?<hours>\d+)h/);
  const minuteMatch = timeStr.match(/(?<minutes>\d+)m/);

  const hours = hourMatch?.groups?.hours ? parseInt(hourMatch.groups.hours, 10) : 0;
  const minutes = minuteMatch?.groups?.minutes ? parseInt(minuteMatch.groups.minutes, 10) : 0;

  return (hours * 60) + minutes;
};

const MonthlyWorkReport = ({ dateRange }) => {
  const dispatch = useDispatch();
  const { multiUserActivityArr } = useSelector((state) => state.activity || { multiUserActivityArr: [] });

  // Use multiUserActivityArr for the ActivityTimeline components
  const activityArr = multiUserActivityArr;
  
  // Format the selected month for display
  const displayMonth = dateRange?.startDate ? format(parseISO(dateRange.startDate), "MMMM yyyy") : format(new Date(), "MMMM yyyy");

  // Note: API call is handled by the parent Overview component to avoid duplicate requests
  // useEffect(() => {
  //   if (dateRange?.startDate && dateRange?.endDate) {
  //     dispatch(ActivityActions.getUserActivity({
  //       startDate: dateRange.startDate,
  //       endDate: dateRange.endDate,
  //       view: 'month'
  //     }));
  //   }
  // }, [dateRange, dispatch]);

  // If data is not available, show placeholder
  if (!activityArr || activityArr.length === 0) {
    return (
      <Box p={3}>
        <Typography variant="h6" gutterBottom>
          Monthly Work Report – {displayMonth}
        </Typography>
        <Typography>No employee data available</Typography>
      </Box>
    );
  }

  return (
    <Box p={3}>
      {/* <Typography variant="h6" gutterBottom>
        Monthly Work Report – {displayMonth}
      </Typography> */}
      <TableContainer component={Paper}>
        <Table>
          <TableHead>
            <TableRow sx={{ backgroundColor: "#f5f5f5" }}>
              <TableCell><strong>Name</strong></TableCell>
              <TableCell></TableCell>
              <TableCell><strong>Total Work Hours</strong></TableCell>
              <TableCell><strong>Worked Hours</strong></TableCell>
              <TableCell><strong>Focus Hours</strong></TableCell>
              <TableCell><strong>Productive Hours</strong></TableCell>
              <TableCell><strong>Idle + Private</strong></TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {activityArr.map((emp, idx) => {
              // Calculate monthly progress based on worked hours vs expected hours
              const workedMinutes = parseTimeToMinutes(emp.worked || "0h 0m");

              // Calculate expected monthly working hours
              // Get the number of days in the current month
              const currentMonth = dateRange?.startDate ? parseISO(dateRange.startDate) : new Date();
              const daysInMonth = getDaysInMonth(currentMonth);

              // Assume 22 working days per month (excluding weekends and holidays)
              // This is a standard approximation, you can adjust based on your business logic
              const workingDaysInMonth = Math.min(22, daysInMonth);
              const expectedMonthlyMinutes = workingDaysInMonth * 8 * 60; // working days * 8 hours * 60 minutes

              // Calculate progress percentage
              const monthlyProgress = expectedMonthlyMinutes > 0 ? Math.min((workedMinutes / expectedMonthlyMinutes) * 100, 100): 0;

              // Determine color based on progress
              let barColor = "inherit";
              if (monthlyProgress >= 90) { barColor = "success"; }
              else if (monthlyProgress >= 60) { barColor = "warning"; }
              else if (monthlyProgress > 0) { barColor = "error"; }

              return (
                <TableRow key={idx}>
                  <TableCell>
                    <Box display="flex" alignItems="center" gap={1}>
                      <Avatar>{(emp.name || "")[0]}</Avatar>
                      <Typography>{emp.name || ""}</Typography>
                    </Box>
                  </TableCell>
                  <TableCell sx={{ minWidth: 150 }}>
                    <LinearProgress
                      variant="determinate"
                      // value={Math.round(monthlyProgress)}
                      color={barColor}
                      sx={{ height: 6, borderRadius: 4, width: "140px" }}
                    />
                  </TableCell>
                  <TableCell>{emp.totalWork || "0h 0m"}</TableCell>
                  <TableCell>{emp.worked || "--"}</TableCell>
                  <TableCell>{emp.focus || "--"}</TableCell>
                  <TableCell>{emp.productive || "--"}</TableCell>
                  <TableCell>{emp.idle || "--"}</TableCell>
                </TableRow>
              );
            })}
          </TableBody>
        </Table>
      </TableContainer>
      <Typography variant="caption" color="gray" mt={2} display="block">
        ℹ️ Progress calculation based on <strong>Worked Hours</strong> vs expected monthly hours (22 working days × 8 hours)
      </Typography>
    </Box>
  );
};

MonthlyWorkReport.propTypes = {
  dateRange: PropTypes.shape({
    startDate: PropTypes.string,
    endDate: PropTypes.string
  })
};

export default MonthlyWorkReport;