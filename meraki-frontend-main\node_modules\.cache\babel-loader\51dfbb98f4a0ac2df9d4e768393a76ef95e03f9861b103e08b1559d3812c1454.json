{"ast": null, "code": "var _jsxFileName = \"E:\\\\Suraj Patil\\\\Organization_Management_System\\\\meraki-frontend-main\\\\src\\\\screens\\\\Dashboard\\\\components\\\\TaskProgressBar.jsx\",\n  _s = $RefreshSig$();\nimport PropTypes from \"prop-types\";\nimport React, { useState, useCallback, useEffect } from \"react\";\nimport \"../../../App.css\";\nimport { useDispatch, useSelector } from \"react-redux\";\nimport { TimelineSelector, UserSelector, ProductSelector } from \"selectors\";\nimport { Tooltip } from '@mui/material';\nimport { ProductActions } from \"slices/actions\";\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst TaskProgressBar = () => {\n  _s();\n  const profile = useSelector(UserSelector.profile());\n  const todayTimeLineRequests = useSelector(TimelineSelector.getTimelineRequestsToday());\n  const products = useSelector(ProductSelector.getOnGoingProductsTasksToday());\n  const dispatch = useDispatch();\n  useEffect(() => {\n    dispatch(ProductActions.getOnGoingProductsTasksToday());\n  }, []);\n  useEffect(() => {\n    console.log(\"Task Progress bar Products \", products);\n  }, [products]);\n  useEffect(() => {\n    console.log(\"Time Line Request \", todayTimeLineRequests);\n  }, [todayTimeLineRequests]);\n  let minArr = [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59];\n  let minArrRev = [...minArr].reverse();\n  const hours = Array.from({\n    length: 24\n  }, (_, i) => `${i} AM`);\n  hours[12] = \"12 PM\";\n  for (let i = 13; i < 24; i++) {\n    hours[i] = `${i - 12} PM`;\n  }\n  const [toolTipTitle, setTooltipTitle] = useState(\"\");\n  const [toolTipController, setToolTipController] = useState(false);\n  const getSlotColor = (hour, minute) => {\n    const slotTime = new Date(new Date().setHours(hour, minute, 0));\n    if (products > 0) {\n      for (let i = 0; i < products.length; i++) {\n        const task = products[i];\n        const taskStartDate = new Date(task.startTime);\n        let taskEndDate = \"\";\n        if (task.endTime !== null) {\n          taskEndDate = new Date(task.endTime);\n        }\n        taskEndDate = Date.now();\n        console.log(\"Task Found\", taskStartDate, taskEndDate, slotTime);\n        if (slotTime >= taskStartDate && slotTime <= taskEndDate) {\n          return \"green\";\n        }\n      }\n    }\n    return \"lightgrey\";\n  };\n  const normalizeRGB = useCallback(rgb => {\n    const result = rgb.match(/\\d+/g);\n    return result ? `rgb(${result[0]},${result[1]},${result[2]})` : rgb;\n  }, []);\n  const dateFormat = useCallback((startTime, endTime) => {\n    const startTimeStr = new Date(startTime);\n    const endTimeStr = new Date(endTime);\n    let result = (endTimeStr - startTimeStr) / 60000;\n    return result < 60 ? `${Math.floor(result)}m` : `${Math.floor(result / 60)}h ${Math.floor(result % 60)}m `;\n  }, []);\n  const handleMouseEnter = (event, hour, minute) => {\n    const divColor = getComputedStyle(event.currentTarget).backgroundColor;\n    console.log(\"Handle Mouse Eneter \", divColor);\n    switch (normalizeRGB(divColor)) {\n      case \"rgb(255,255,0)\":\n        {\n          setToolTipController(true);\n          const activityDate = new Date(new Date().setHours(hour, minute - 1, 0, 0));\n          let idleFound = false;\n          if (!idleFound) {\n            console.log(\"Not Idel FOund\");\n            setTooltipTitle(\"Idle\");\n          }\n          break;\n        }\n      case \"rgb(50,205,50)\":\n        {\n          setToolTipController(true);\n          const activityDate = new Date(new Date().setHours(hour, minute - 1, 0, 0));\n          let workFound = false;\n          if (!workFound) {\n            setTooltipTitle(\"Work\");\n          }\n          break;\n        }\n      case \"rgb(255,0,0)\":\n        {\n          setToolTipController(true);\n          const activityDate = new Date(new Date().setHours(hour, minute - 1, 0, 0));\n          let breakFound = false;\n          if (!breakFound) {\n            console.log(\"Breka N\");\n            setTooltipTitle(\"Break\");\n          }\n          break;\n        }\n      case \"rgb(0,0,255)\":\n        {\n          console.log(\"Blue\");\n          setToolTipController(true);\n          const activityDate = new Date(new Date().setHours(hour, minute - 1, 0, 0));\n          let timelineFound = false;\n          let status = \"Approved\";\n          if (!timelineFound) {\n            console.log(\"Timeline N\");\n            setTooltipTitle(\"Request\");\n          }\n          break;\n        }\n      default:\n        {\n          // console.log(\"Blue\")\n          setToolTipController(false);\n          break;\n        }\n    }\n  };\n  const handleMouseClick = (event, hour, minute) => {\n    const divColor = getComputedStyle(event.currentTarget).backgroundColor;\n    switch (normalizeRGB(divColor)) {\n      case \"rgb(255,255,0)\":\n        {\n          const activityDate = new Date(new Date().setHours(hour, minute - 1, 0, 0));\n          break;\n        }\n      case \"rgb(255,0,0)\":\n        {\n          const activityDate = new Date(new Date().setHours(hour, minute - 1, 0, 0));\n          break;\n        }\n      default:\n        console.log(\"Default\");\n        break;\n    }\n  };\n  const renderProgressBars = () => {\n    const progressBars = [];\n    let currentActivity = null;\n    let currentActivityStart = 0;\n    let currentActivityWidth = 0;\n    hours.forEach((hour, hourIndex) => {\n      minArr.forEach(minute => {\n        const activity = getSlotColor(hourIndex, minute);\n        if (activity !== currentActivity) {\n          if (currentActivity !== null) {\n            // Push the current accumulated div\n            progressBars.push(/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"progress-bar\",\n              role: \"progressbar\",\n              style: {\n                width: `${currentActivityWidth}%`,\n                backgroundColor: currentActivity\n              },\n              onMouseEnter: event => handleMouseEnter(event, hourIndex, minute),\n              onClick: event => handleMouseClick(event, hourIndex, minute),\n              children: toolTipController ? /*#__PURE__*/_jsxDEV(Tooltip, {\n                title: toolTipTitle,\n                arrow: true,\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    padding: \"20px\",\n                    display: \"inline-block\"\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 196,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 195,\n                columnNumber: 37\n              }, this) : null\n            }, `${hourIndex}-${minute}`, false, {\n              fileName: _jsxFileName,\n              lineNumber: 184,\n              columnNumber: 15\n            }, this));\n          }\n          // Start a new activity block\n          currentActivity = activity;\n          currentActivityStart = minute;\n          currentActivityWidth = 1.04;\n        } else {\n          // Accumulate width for the same activity\n          currentActivityWidth += 1.04;\n        }\n      });\n    });\n    if (currentActivity !== null) {\n      // console.log(\"Accumulated Cell\")\n      progressBars.push(/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"progress-bar\",\n        role: \"progressbar\",\n        style: {\n          width: `${currentActivityWidth}%`,\n          backgroundColor: currentActivity\n        },\n        onMouseEnter: event => handleMouseEnter(event, hours.length - 1, minArr.length - 1)\n        // onMouseLeave={handleMouseLeave}\n        ,\n        children: toolTipController ? /*#__PURE__*/_jsxDEV(Tooltip, {\n          title: toolTipTitle,\n          arrow: true,\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              padding: \"20px\",\n              display: \"inline-block\"\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 230,\n            columnNumber: 19\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 229,\n          columnNumber: 33\n        }, this) : null\n      }, `last-${currentActivityStart}`, false, {\n        fileName: _jsxFileName,\n        lineNumber: 218,\n        columnNumber: 9\n      }, this));\n    }\n    return progressBars;\n  };\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        marginBottom: \"1px\"\n      },\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"progress\",\n        style: {\n          height: \"10px\"\n        },\n        children: renderProgressBars()\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 245,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 244,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"d-flex justify-content-between mt-1\",\n      children: [/*#__PURE__*/_jsxDEV(\"li\", {\n        className: \"timeSlotLi\",\n        children: \"12AM\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 250,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n        className: \"timeSlotLi\",\n        children: \"1AM\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 251,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n        className: \"timeSlotLi\",\n        children: \"2AM\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 252,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n        className: \"timeSlotLi\",\n        children: \"3AM\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 253,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n        className: \"timeSlotLi\",\n        children: \"4AM\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 254,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n        className: \"timeSlotLi\",\n        children: \"5AM\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 255,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n        className: \"timeSlotLi\",\n        children: \"6AM\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 256,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n        className: \"timeSlotLi\",\n        children: \"7AM\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 257,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n        className: \"timeSlotLi\",\n        children: \"8AM\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 258,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n        className: \"timeSlotLi\",\n        children: \"9AM\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 259,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n        className: \"timeSlotLi\",\n        children: \"10AM\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 260,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n        className: \"timeSlotLi\",\n        children: \"11AM\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 261,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n        className: \"timeSlotLi\",\n        children: \"12PM\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 262,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n        className: \"timeSlotLi\",\n        children: \"1PM\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 263,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n        className: \"timeSlotLi\",\n        children: \"2PM\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 264,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n        className: \"timeSlotLi\",\n        children: \"3PM\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 265,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n        className: \"timeSlotLi\",\n        children: \"4PM\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 266,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n        className: \"timeSlotLi\",\n        children: \"5PM\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 267,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n        className: \"timeSlotLi\",\n        children: \"6PM\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 268,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n        className: \"timeSlotLi\",\n        children: \"7PM\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 269,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n        className: \"timeSlotLi\",\n        children: \"8PM\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 270,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n        className: \"timeSlotLi\",\n        children: \"9PM\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 271,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n        className: \"timeSlotLi\",\n        children: \"10PM\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 272,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n        className: \"timeSlotLi\",\n        children: \"11PM\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 273,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 249,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true);\n};\n\n// TaskProgressBar.propTypes = {\n//   products: PropTypes.array,\n// };\n_s(TaskProgressBar, \"qGyrNsgK5v65l9frPqa0T8ySXWg=\", false, function () {\n  return [useSelector, useSelector, useSelector, useDispatch];\n});\n_c = TaskProgressBar;\nexport default TaskProgressBar;\nvar _c;\n$RefreshReg$(_c, \"TaskProgressBar\");", "map": {"version": 3, "names": ["PropTypes", "React", "useState", "useCallback", "useEffect", "useDispatch", "useSelector", "TimelineSelector", "UserSelector", "ProductSelector", "<PERSON><PERSON><PERSON>", "ProductActions", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "TaskProgressBar", "_s", "profile", "todayTimeLineRequests", "getTimelineRequestsToday", "products", "getOnGoingProductsTasksToday", "dispatch", "console", "log", "minArr", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "reverse", "hours", "Array", "from", "length", "_", "i", "toolTipTitle", "setTooltipTitle", "toolTipController", "setToolTipController", "getSlotColor", "hour", "minute", "slotTime", "Date", "setHours", "task", "taskStartDate", "startTime", "taskEndDate", "endTime", "now", "normalizeRGB", "rgb", "result", "match", "dateFormat", "startTimeStr", "endTimeStr", "Math", "floor", "handleMouseEnter", "event", "divColor", "getComputedStyle", "currentTarget", "backgroundColor", "activityDate", "idleFound", "workFound", "breakFound", "timelineFound", "status", "handleMouseClick", "renderProgressBars", "progressBars", "currentActivity", "currentActivityStart", "currentActivityWidth", "for<PERSON>ach", "hourIndex", "activity", "push", "className", "role", "style", "width", "onMouseEnter", "onClick", "children", "title", "arrow", "padding", "display", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "marginBottom", "height", "_c", "$RefreshReg$"], "sources": ["E:/Suraj Patil/Organization_Management_System/meraki-frontend-main/src/screens/Dashboard/components/TaskProgressBar.jsx"], "sourcesContent": ["import PropTypes from \"prop-types\";\r\nimport React, { useState, useCallback, useEffect } from \"react\";\r\nimport \"../../../App.css\";\r\nimport { useDispatch, useSelector } from \"react-redux\";\r\n\r\nimport { TimelineSelector, UserSelector,ProductSelector } from \"selectors\";\r\n\r\n\r\nimport {\r\n  Tooltip\r\n} from '@mui/material';\r\nimport { ProductActions } from \"slices/actions\";\r\n\r\nconst TaskProgressBar = () => {\r\n\r\n    const profile = useSelector(UserSelector.profile());\r\n    const todayTimeLineRequests = useSelector(TimelineSelector.getTimelineRequestsToday());\r\n    const products = useSelector(ProductSelector.getOnGoingProductsTasksToday())\r\n    const dispatch = useDispatch()\r\n \r\n    useEffect(() => {\r\n        dispatch(ProductActions.getOnGoingProductsTasksToday())\r\n    },[])\r\n\r\n    useEffect(() => {\r\n        console.log(\"Task Progress bar Products \",products)\r\n    },[products])\r\n \r\n    useEffect(() => {\r\n      console.log(\"Time Line Request \",todayTimeLineRequests)\r\n     \r\n    },[todayTimeLineRequests])\r\n\r\n\r\n  let minArr = [\r\n    0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20,\r\n    21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39,\r\n    40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58,\r\n    59,\r\n  ];\r\n  let minArrRev = [...minArr].reverse();\r\n\r\n  const hours = Array.from({ length: 24 }, (_, i) => `${i} AM`);\r\n  hours[12] = \"12 PM\";\r\n  for (let i = 13; i < 24; i++) {\r\n    hours[i] = `${i - 12} PM`;\r\n  } \r\n  const [toolTipTitle, setTooltipTitle] = useState(\"\");\r\n  const [toolTipController,setToolTipController] = useState(false)\r\n  \r\n\r\n  const getSlotColor = (hour, minute) => {\r\n    const slotTime = new Date(new Date().setHours(hour, minute, 0));\r\n    if(products > 0) {\r\n      for (let i = 0; i < products.length; i++) {\r\n        const task = products[i];\r\n        const taskStartDate = new Date(task.startTime);\r\n        let taskEndDate=\"\";\r\n        if(task.endTime !== null) {\r\n\r\n             taskEndDate = new Date(task.endTime);\r\n        }\r\n        taskEndDate = Date.now()\r\n        console.log(\"Task Found\",taskStartDate,taskEndDate,slotTime)\r\n  \r\n        if (slotTime >= taskStartDate && slotTime <= taskEndDate) {\r\n          return \"green\";\r\n        }\r\n      }\r\n    }\r\n    return \"lightgrey\";\r\n  };\r\n\r\n  const normalizeRGB = useCallback((rgb) => {\r\n    const result = rgb.match(/\\d+/g);\r\n    return result ? `rgb(${result[0]},${result[1]},${result[2]})` : rgb;\r\n  },[]);\r\n\r\n  const dateFormat = useCallback((startTime, endTime) => {\r\n    const startTimeStr = new Date(startTime);\r\n    const endTimeStr = new Date(endTime);\r\n    let result = (endTimeStr - startTimeStr) / 60000;\r\n    return result < 60 ? `${Math.floor(result)}m` : `${Math.floor(result / 60)}h ${Math.floor(result % 60)}m `;\r\n  },[]);\r\n\r\n  const handleMouseEnter = (event, hour, minute) => {\r\n    const divColor = getComputedStyle(event.currentTarget).backgroundColor;\r\n    console.log(\"Handle Mouse Eneter \",divColor)\r\n\r\n    switch (normalizeRGB(divColor)) {\r\n      case \"rgb(255,255,0)\": {\r\n        setToolTipController(true)\r\n        const activityDate = new Date(new Date().setHours(hour, minute-1, 0, 0));\r\n        let idleFound = false;\r\n        \r\n        if (!idleFound) {\r\n          console.log(\"Not Idel FOund\")\r\n          setTooltipTitle(\"Idle\");\r\n        }\r\n        break;\r\n      }\r\n      case \"rgb(50,205,50)\": {\r\n        setToolTipController(true)\r\n        const activityDate = new Date(\r\n          new Date().setHours(hour, minute-1, 0, 0)\r\n        );\r\n        let workFound = false;\r\n        \r\n        if (!workFound) {\r\n          setTooltipTitle(\"Work\");\r\n        }\r\n        break;\r\n      }\r\n      case \"rgb(255,0,0)\": {\r\n        setToolTipController(true)\r\n        const activityDate = new Date(new Date().setHours(hour, minute-1, 0, 0));\r\n        let breakFound = false;\r\n        \r\n        if (!breakFound) {\r\n          console.log(\"Breka N\")\r\n          setTooltipTitle(\"Break\");\r\n        }\r\n        break;\r\n      }\r\n\r\n      case \"rgb(0,0,255)\": {\r\n        console.log(\"Blue\")\r\n        setToolTipController(true)\r\n        const activityDate = new Date(new Date().setHours(hour, minute-1, 0, 0));\r\n        let timelineFound = false;\r\n        let status = \"Approved\"\r\n      \r\n        if (!timelineFound) {\r\n          console.log(\"Timeline N\")\r\n          setTooltipTitle(\"Request\");\r\n        }\r\n        break;\r\n      }\r\n                                \r\n      default: {\r\n        // console.log(\"Blue\")\r\n        setToolTipController(false)\r\n      \r\n        break;\r\n      }\r\n    }\r\n  };\r\n\r\n  const handleMouseClick = (event, hour, minute) => {\r\n    const divColor = getComputedStyle(event.currentTarget).backgroundColor;\r\n  \r\n    switch (normalizeRGB(divColor)) {\r\n      case \"rgb(255,255,0)\": {\r\n        const activityDate = new Date(new Date().setHours(hour, minute - 1, 0, 0));\r\n        break;\r\n      }\r\n  \r\n      case \"rgb(255,0,0)\": {\r\n        const activityDate = new Date(new Date().setHours(hour, minute - 1, 0, 0));\r\n        \r\n        break;\r\n      }\r\n      \r\n      default:  console.log(\"Default\")\r\n        break;\r\n    }\r\n   \r\n  }\r\n  \r\n\r\n  const renderProgressBars = () => {\r\n    const progressBars = [];\r\n    let currentActivity = null;\r\n    let currentActivityStart = 0;\r\n    let currentActivityWidth = 0;\r\n\r\n    hours.forEach((hour, hourIndex) => {\r\n      minArr.forEach((minute) => {\r\n        const activity = getSlotColor(hourIndex, minute);\r\n        if (activity !== currentActivity) {\r\n          if (currentActivity !== null) {\r\n            // Push the current accumulated div\r\n            progressBars.push(\r\n              <div\r\n                key={`${hourIndex}-${minute}`}\r\n                className=\"progress-bar\"\r\n                role=\"progressbar\"\r\n                style={{\r\n                  width: `${currentActivityWidth}%`,\r\n                  backgroundColor: currentActivity,\r\n                }}\r\n                onMouseEnter={(event) => handleMouseEnter(event, hourIndex, minute)}\r\n                onClick={(event) => handleMouseClick(event, hourIndex, minute)}\r\n              >\r\n               {toolTipController ? <Tooltip title={toolTipTitle} arrow>\r\n                  <div\r\n                    style={{ padding: \"20px\", display: \"inline-block\" }}\r\n                  ></div>\r\n                </Tooltip> : null }\r\n              </div>\r\n            );\r\n          }\r\n          // Start a new activity block\r\n          currentActivity = activity;\r\n          currentActivityStart = minute;\r\n          currentActivityWidth = 1.04;\r\n        } else {\r\n          // Accumulate width for the same activity\r\n          currentActivityWidth += 1.04;\r\n        }\r\n      });\r\n    });\r\n\r\n\r\n    if (currentActivity !== null) {\r\n      // console.log(\"Accumulated Cell\")\r\n      progressBars.push(\r\n        <div\r\n          key={`last-${currentActivityStart}`}\r\n          className=\"progress-bar\"\r\n          role=\"progressbar\"\r\n          style={{\r\n            width: `${currentActivityWidth}%`,\r\n            backgroundColor: currentActivity,\r\n          }}\r\n          onMouseEnter={(event) => handleMouseEnter(event, hours.length - 1, minArr.length - 1) }\r\n          // onMouseLeave={handleMouseLeave}\r\n        >\r\n           {toolTipController ? <Tooltip title={toolTipTitle} arrow >\r\n                  <div \r\n                    style={{ padding: \"20px\", display: \"inline-block\" }}\r\n                    ></div>\r\n                </Tooltip> : null }\r\n        </div>\r\n      );\r\n    }\r\n\r\n    return progressBars;\r\n  };\r\n\r\n  return (\r\n    <>\r\n   \r\n      <div style={{ marginBottom: \"1px\" }}>\r\n        <div className=\"progress\" style={{ height: \"10px\" }}>\r\n          {renderProgressBars()}\r\n        </div>\r\n      </div>\r\n      <div className=\"d-flex justify-content-between mt-1\">\r\n        <li className=\"timeSlotLi\">12AM</li>\r\n        <li className=\"timeSlotLi\">1AM</li>\r\n        <li className=\"timeSlotLi\">2AM</li>\r\n        <li className=\"timeSlotLi\">3AM</li>\r\n        <li className=\"timeSlotLi\">4AM</li>\r\n        <li className=\"timeSlotLi\">5AM</li>\r\n        <li className=\"timeSlotLi\">6AM</li>\r\n        <li className=\"timeSlotLi\">7AM</li>\r\n        <li className=\"timeSlotLi\">8AM</li>\r\n        <li className=\"timeSlotLi\">9AM</li>\r\n        <li className=\"timeSlotLi\">10AM</li>\r\n        <li className=\"timeSlotLi\">11AM</li>\r\n        <li className=\"timeSlotLi\">12PM</li>\r\n        <li className=\"timeSlotLi\">1PM</li>\r\n        <li className=\"timeSlotLi\">2PM</li>\r\n        <li className=\"timeSlotLi\">3PM</li>\r\n        <li className=\"timeSlotLi\">4PM</li>\r\n        <li className=\"timeSlotLi\">5PM</li>\r\n        <li className=\"timeSlotLi\">6PM</li>\r\n        <li className=\"timeSlotLi\">7PM</li>\r\n        <li className=\"timeSlotLi\">8PM</li>\r\n        <li className=\"timeSlotLi\">9PM</li>\r\n        <li className=\"timeSlotLi\">10PM</li>\r\n        <li className=\"timeSlotLi\">11PM</li>\r\n      </div>\r\n    </>\r\n  );\r\n};\r\n\r\n// TaskProgressBar.propTypes = {\r\n//   products: PropTypes.array,\r\n// };\r\n\r\nexport default TaskProgressBar;\r\n"], "mappings": ";;AAAA,OAAOA,SAAS,MAAM,YAAY;AAClC,OAAOC,KAAK,IAAIC,QAAQ,EAAEC,WAAW,EAAEC,SAAS,QAAQ,OAAO;AAC/D,OAAO,kBAAkB;AACzB,SAASC,WAAW,EAAEC,WAAW,QAAQ,aAAa;AAEtD,SAASC,gBAAgB,EAAEC,YAAY,EAACC,eAAe,QAAQ,WAAW;AAG1E,SACEC,OAAO,QACF,eAAe;AACtB,SAASC,cAAc,QAAQ,gBAAgB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAEhD,MAAMC,eAAe,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAE1B,MAAMC,OAAO,GAAGZ,WAAW,CAACE,YAAY,CAACU,OAAO,CAAC,CAAC,CAAC;EACnD,MAAMC,qBAAqB,GAAGb,WAAW,CAACC,gBAAgB,CAACa,wBAAwB,CAAC,CAAC,CAAC;EACtF,MAAMC,QAAQ,GAAGf,WAAW,CAACG,eAAe,CAACa,4BAA4B,CAAC,CAAC,CAAC;EAC5E,MAAMC,QAAQ,GAAGlB,WAAW,CAAC,CAAC;EAE9BD,SAAS,CAAC,MAAM;IACZmB,QAAQ,CAACZ,cAAc,CAACW,4BAA4B,CAAC,CAAC,CAAC;EAC3D,CAAC,EAAC,EAAE,CAAC;EAELlB,SAAS,CAAC,MAAM;IACZoB,OAAO,CAACC,GAAG,CAAC,6BAA6B,EAACJ,QAAQ,CAAC;EACvD,CAAC,EAAC,CAACA,QAAQ,CAAC,CAAC;EAEbjB,SAAS,CAAC,MAAM;IACdoB,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAACN,qBAAqB,CAAC;EAEzD,CAAC,EAAC,CAACA,qBAAqB,CAAC,CAAC;EAG5B,IAAIO,MAAM,GAAG,CACX,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EACxE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAC1E,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAC1E,EAAE,CACH;EACD,IAAIC,SAAS,GAAG,CAAC,GAAGD,MAAM,CAAC,CAACE,OAAO,CAAC,CAAC;EAErC,MAAMC,KAAK,GAAGC,KAAK,CAACC,IAAI,CAAC;IAAEC,MAAM,EAAE;EAAG,CAAC,EAAE,CAACC,CAAC,EAAEC,CAAC,KAAK,GAAGA,CAAC,KAAK,CAAC;EAC7DL,KAAK,CAAC,EAAE,CAAC,GAAG,OAAO;EACnB,KAAK,IAAIK,CAAC,GAAG,EAAE,EAAEA,CAAC,GAAG,EAAE,EAAEA,CAAC,EAAE,EAAE;IAC5BL,KAAK,CAACK,CAAC,CAAC,GAAG,GAAGA,CAAC,GAAG,EAAE,KAAK;EAC3B;EACA,MAAM,CAACC,YAAY,EAAEC,eAAe,CAAC,GAAGlC,QAAQ,CAAC,EAAE,CAAC;EACpD,MAAM,CAACmC,iBAAiB,EAACC,oBAAoB,CAAC,GAAGpC,QAAQ,CAAC,KAAK,CAAC;EAGhE,MAAMqC,YAAY,GAAGA,CAACC,IAAI,EAAEC,MAAM,KAAK;IACrC,MAAMC,QAAQ,GAAG,IAAIC,IAAI,CAAC,IAAIA,IAAI,CAAC,CAAC,CAACC,QAAQ,CAACJ,IAAI,EAAEC,MAAM,EAAE,CAAC,CAAC,CAAC;IAC/D,IAAGpB,QAAQ,GAAG,CAAC,EAAE;MACf,KAAK,IAAIa,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGb,QAAQ,CAACW,MAAM,EAAEE,CAAC,EAAE,EAAE;QACxC,MAAMW,IAAI,GAAGxB,QAAQ,CAACa,CAAC,CAAC;QACxB,MAAMY,aAAa,GAAG,IAAIH,IAAI,CAACE,IAAI,CAACE,SAAS,CAAC;QAC9C,IAAIC,WAAW,GAAC,EAAE;QAClB,IAAGH,IAAI,CAACI,OAAO,KAAK,IAAI,EAAE;UAErBD,WAAW,GAAG,IAAIL,IAAI,CAACE,IAAI,CAACI,OAAO,CAAC;QACzC;QACAD,WAAW,GAAGL,IAAI,CAACO,GAAG,CAAC,CAAC;QACxB1B,OAAO,CAACC,GAAG,CAAC,YAAY,EAACqB,aAAa,EAACE,WAAW,EAACN,QAAQ,CAAC;QAE5D,IAAIA,QAAQ,IAAII,aAAa,IAAIJ,QAAQ,IAAIM,WAAW,EAAE;UACxD,OAAO,OAAO;QAChB;MACF;IACF;IACA,OAAO,WAAW;EACpB,CAAC;EAED,MAAMG,YAAY,GAAGhD,WAAW,CAAEiD,GAAG,IAAK;IACxC,MAAMC,MAAM,GAAGD,GAAG,CAACE,KAAK,CAAC,MAAM,CAAC;IAChC,OAAOD,MAAM,GAAG,OAAOA,MAAM,CAAC,CAAC,CAAC,IAAIA,MAAM,CAAC,CAAC,CAAC,IAAIA,MAAM,CAAC,CAAC,CAAC,GAAG,GAAGD,GAAG;EACrE,CAAC,EAAC,EAAE,CAAC;EAEL,MAAMG,UAAU,GAAGpD,WAAW,CAAC,CAAC4C,SAAS,EAAEE,OAAO,KAAK;IACrD,MAAMO,YAAY,GAAG,IAAIb,IAAI,CAACI,SAAS,CAAC;IACxC,MAAMU,UAAU,GAAG,IAAId,IAAI,CAACM,OAAO,CAAC;IACpC,IAAII,MAAM,GAAG,CAACI,UAAU,GAAGD,YAAY,IAAI,KAAK;IAChD,OAAOH,MAAM,GAAG,EAAE,GAAG,GAAGK,IAAI,CAACC,KAAK,CAACN,MAAM,CAAC,GAAG,GAAG,GAAGK,IAAI,CAACC,KAAK,CAACN,MAAM,GAAG,EAAE,CAAC,KAAKK,IAAI,CAACC,KAAK,CAACN,MAAM,GAAG,EAAE,CAAC,IAAI;EAC5G,CAAC,EAAC,EAAE,CAAC;EAEL,MAAMO,gBAAgB,GAAGA,CAACC,KAAK,EAAErB,IAAI,EAAEC,MAAM,KAAK;IAChD,MAAMqB,QAAQ,GAAGC,gBAAgB,CAACF,KAAK,CAACG,aAAa,CAAC,CAACC,eAAe;IACtEzC,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAACqC,QAAQ,CAAC;IAE5C,QAAQX,YAAY,CAACW,QAAQ,CAAC;MAC5B,KAAK,gBAAgB;QAAE;UACrBxB,oBAAoB,CAAC,IAAI,CAAC;UAC1B,MAAM4B,YAAY,GAAG,IAAIvB,IAAI,CAAC,IAAIA,IAAI,CAAC,CAAC,CAACC,QAAQ,CAACJ,IAAI,EAAEC,MAAM,GAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;UACxE,IAAI0B,SAAS,GAAG,KAAK;UAErB,IAAI,CAACA,SAAS,EAAE;YACd3C,OAAO,CAACC,GAAG,CAAC,gBAAgB,CAAC;YAC7BW,eAAe,CAAC,MAAM,CAAC;UACzB;UACA;QACF;MACA,KAAK,gBAAgB;QAAE;UACrBE,oBAAoB,CAAC,IAAI,CAAC;UAC1B,MAAM4B,YAAY,GAAG,IAAIvB,IAAI,CAC3B,IAAIA,IAAI,CAAC,CAAC,CAACC,QAAQ,CAACJ,IAAI,EAAEC,MAAM,GAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAC1C,CAAC;UACD,IAAI2B,SAAS,GAAG,KAAK;UAErB,IAAI,CAACA,SAAS,EAAE;YACdhC,eAAe,CAAC,MAAM,CAAC;UACzB;UACA;QACF;MACA,KAAK,cAAc;QAAE;UACnBE,oBAAoB,CAAC,IAAI,CAAC;UAC1B,MAAM4B,YAAY,GAAG,IAAIvB,IAAI,CAAC,IAAIA,IAAI,CAAC,CAAC,CAACC,QAAQ,CAACJ,IAAI,EAAEC,MAAM,GAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;UACxE,IAAI4B,UAAU,GAAG,KAAK;UAEtB,IAAI,CAACA,UAAU,EAAE;YACf7C,OAAO,CAACC,GAAG,CAAC,SAAS,CAAC;YACtBW,eAAe,CAAC,OAAO,CAAC;UAC1B;UACA;QACF;MAEA,KAAK,cAAc;QAAE;UACnBZ,OAAO,CAACC,GAAG,CAAC,MAAM,CAAC;UACnBa,oBAAoB,CAAC,IAAI,CAAC;UAC1B,MAAM4B,YAAY,GAAG,IAAIvB,IAAI,CAAC,IAAIA,IAAI,CAAC,CAAC,CAACC,QAAQ,CAACJ,IAAI,EAAEC,MAAM,GAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;UACxE,IAAI6B,aAAa,GAAG,KAAK;UACzB,IAAIC,MAAM,GAAG,UAAU;UAEvB,IAAI,CAACD,aAAa,EAAE;YAClB9C,OAAO,CAACC,GAAG,CAAC,YAAY,CAAC;YACzBW,eAAe,CAAC,SAAS,CAAC;UAC5B;UACA;QACF;MAEA;QAAS;UACP;UACAE,oBAAoB,CAAC,KAAK,CAAC;UAE3B;QACF;IACF;EACF,CAAC;EAED,MAAMkC,gBAAgB,GAAGA,CAACX,KAAK,EAAErB,IAAI,EAAEC,MAAM,KAAK;IAChD,MAAMqB,QAAQ,GAAGC,gBAAgB,CAACF,KAAK,CAACG,aAAa,CAAC,CAACC,eAAe;IAEtE,QAAQd,YAAY,CAACW,QAAQ,CAAC;MAC5B,KAAK,gBAAgB;QAAE;UACrB,MAAMI,YAAY,GAAG,IAAIvB,IAAI,CAAC,IAAIA,IAAI,CAAC,CAAC,CAACC,QAAQ,CAACJ,IAAI,EAAEC,MAAM,GAAG,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;UAC1E;QACF;MAEA,KAAK,cAAc;QAAE;UACnB,MAAMyB,YAAY,GAAG,IAAIvB,IAAI,CAAC,IAAIA,IAAI,CAAC,CAAC,CAACC,QAAQ,CAACJ,IAAI,EAAEC,MAAM,GAAG,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;UAE1E;QACF;MAEA;QAAUjB,OAAO,CAACC,GAAG,CAAC,SAAS,CAAC;QAC9B;IACJ;EAEF,CAAC;EAGD,MAAMgD,kBAAkB,GAAGA,CAAA,KAAM;IAC/B,MAAMC,YAAY,GAAG,EAAE;IACvB,IAAIC,eAAe,GAAG,IAAI;IAC1B,IAAIC,oBAAoB,GAAG,CAAC;IAC5B,IAAIC,oBAAoB,GAAG,CAAC;IAE5BhD,KAAK,CAACiD,OAAO,CAAC,CAACtC,IAAI,EAAEuC,SAAS,KAAK;MACjCrD,MAAM,CAACoD,OAAO,CAAErC,MAAM,IAAK;QACzB,MAAMuC,QAAQ,GAAGzC,YAAY,CAACwC,SAAS,EAAEtC,MAAM,CAAC;QAChD,IAAIuC,QAAQ,KAAKL,eAAe,EAAE;UAChC,IAAIA,eAAe,KAAK,IAAI,EAAE;YAC5B;YACAD,YAAY,CAACO,IAAI,cACfpE,OAAA;cAEEqE,SAAS,EAAC,cAAc;cACxBC,IAAI,EAAC,aAAa;cAClBC,KAAK,EAAE;gBACLC,KAAK,EAAE,GAAGR,oBAAoB,GAAG;gBACjCZ,eAAe,EAAEU;cACnB,CAAE;cACFW,YAAY,EAAGzB,KAAK,IAAKD,gBAAgB,CAACC,KAAK,EAAEkB,SAAS,EAAEtC,MAAM,CAAE;cACpE8C,OAAO,EAAG1B,KAAK,IAAKW,gBAAgB,CAACX,KAAK,EAAEkB,SAAS,EAAEtC,MAAM,CAAE;cAAA+C,QAAA,EAE/DnD,iBAAiB,gBAAGxB,OAAA,CAACH,OAAO;gBAAC+E,KAAK,EAAEtD,YAAa;gBAACuD,KAAK;gBAAAF,QAAA,eACrD3E,OAAA;kBACEuE,KAAK,EAAE;oBAAEO,OAAO,EAAE,MAAM;oBAAEC,OAAO,EAAE;kBAAe;gBAAE;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAChD;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACA,CAAC,GAAG;YAAI,GAdZ,GAAGjB,SAAS,IAAItC,MAAM,EAAE;cAAAoD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAe1B,CACP,CAAC;UACH;UACA;UACArB,eAAe,GAAGK,QAAQ;UAC1BJ,oBAAoB,GAAGnC,MAAM;UAC7BoC,oBAAoB,GAAG,IAAI;QAC7B,CAAC,MAAM;UACL;UACAA,oBAAoB,IAAI,IAAI;QAC9B;MACF,CAAC,CAAC;IACJ,CAAC,CAAC;IAGF,IAAIF,eAAe,KAAK,IAAI,EAAE;MAC5B;MACAD,YAAY,CAACO,IAAI,cACfpE,OAAA;QAEEqE,SAAS,EAAC,cAAc;QACxBC,IAAI,EAAC,aAAa;QAClBC,KAAK,EAAE;UACLC,KAAK,EAAE,GAAGR,oBAAoB,GAAG;UACjCZ,eAAe,EAAEU;QACnB,CAAE;QACFW,YAAY,EAAGzB,KAAK,IAAKD,gBAAgB,CAACC,KAAK,EAAEhC,KAAK,CAACG,MAAM,GAAG,CAAC,EAAEN,MAAM,CAACM,MAAM,GAAG,CAAC;QACpF;QAAA;QAAAwD,QAAA,EAEEnD,iBAAiB,gBAAGxB,OAAA,CAACH,OAAO;UAAC+E,KAAK,EAAEtD,YAAa;UAACuD,KAAK;UAAAF,QAAA,eACjD3E,OAAA;YACEuE,KAAK,EAAE;cAAEO,OAAO,EAAE,MAAM;cAAEC,OAAO,EAAE;YAAe;UAAE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9C;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC,GAAG;MAAI,GAdlB,QAAQpB,oBAAoB,EAAE;QAAAiB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAehC,CACP,CAAC;IACH;IAEA,OAAOtB,YAAY;EACrB,CAAC;EAED,oBACE7D,OAAA,CAAAE,SAAA;IAAAyE,QAAA,gBAEE3E,OAAA;MAAKuE,KAAK,EAAE;QAAEa,YAAY,EAAE;MAAM,CAAE;MAAAT,QAAA,eAClC3E,OAAA;QAAKqE,SAAS,EAAC,UAAU;QAACE,KAAK,EAAE;UAAEc,MAAM,EAAE;QAAO,CAAE;QAAAV,QAAA,EACjDf,kBAAkB,CAAC;MAAC;QAAAoB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAClB;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eACNnF,OAAA;MAAKqE,SAAS,EAAC,qCAAqC;MAAAM,QAAA,gBAClD3E,OAAA;QAAIqE,SAAS,EAAC,YAAY;QAAAM,QAAA,EAAC;MAAI;QAAAK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACpCnF,OAAA;QAAIqE,SAAS,EAAC,YAAY;QAAAM,QAAA,EAAC;MAAG;QAAAK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACnCnF,OAAA;QAAIqE,SAAS,EAAC,YAAY;QAAAM,QAAA,EAAC;MAAG;QAAAK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACnCnF,OAAA;QAAIqE,SAAS,EAAC,YAAY;QAAAM,QAAA,EAAC;MAAG;QAAAK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACnCnF,OAAA;QAAIqE,SAAS,EAAC,YAAY;QAAAM,QAAA,EAAC;MAAG;QAAAK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACnCnF,OAAA;QAAIqE,SAAS,EAAC,YAAY;QAAAM,QAAA,EAAC;MAAG;QAAAK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACnCnF,OAAA;QAAIqE,SAAS,EAAC,YAAY;QAAAM,QAAA,EAAC;MAAG;QAAAK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACnCnF,OAAA;QAAIqE,SAAS,EAAC,YAAY;QAAAM,QAAA,EAAC;MAAG;QAAAK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACnCnF,OAAA;QAAIqE,SAAS,EAAC,YAAY;QAAAM,QAAA,EAAC;MAAG;QAAAK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACnCnF,OAAA;QAAIqE,SAAS,EAAC,YAAY;QAAAM,QAAA,EAAC;MAAG;QAAAK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACnCnF,OAAA;QAAIqE,SAAS,EAAC,YAAY;QAAAM,QAAA,EAAC;MAAI;QAAAK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACpCnF,OAAA;QAAIqE,SAAS,EAAC,YAAY;QAAAM,QAAA,EAAC;MAAI;QAAAK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACpCnF,OAAA;QAAIqE,SAAS,EAAC,YAAY;QAAAM,QAAA,EAAC;MAAI;QAAAK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACpCnF,OAAA;QAAIqE,SAAS,EAAC,YAAY;QAAAM,QAAA,EAAC;MAAG;QAAAK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACnCnF,OAAA;QAAIqE,SAAS,EAAC,YAAY;QAAAM,QAAA,EAAC;MAAG;QAAAK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACnCnF,OAAA;QAAIqE,SAAS,EAAC,YAAY;QAAAM,QAAA,EAAC;MAAG;QAAAK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACnCnF,OAAA;QAAIqE,SAAS,EAAC,YAAY;QAAAM,QAAA,EAAC;MAAG;QAAAK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACnCnF,OAAA;QAAIqE,SAAS,EAAC,YAAY;QAAAM,QAAA,EAAC;MAAG;QAAAK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACnCnF,OAAA;QAAIqE,SAAS,EAAC,YAAY;QAAAM,QAAA,EAAC;MAAG;QAAAK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACnCnF,OAAA;QAAIqE,SAAS,EAAC,YAAY;QAAAM,QAAA,EAAC;MAAG;QAAAK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACnCnF,OAAA;QAAIqE,SAAS,EAAC,YAAY;QAAAM,QAAA,EAAC;MAAG;QAAAK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACnCnF,OAAA;QAAIqE,SAAS,EAAC,YAAY;QAAAM,QAAA,EAAC;MAAG;QAAAK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACnCnF,OAAA;QAAIqE,SAAS,EAAC,YAAY;QAAAM,QAAA,EAAC;MAAI;QAAAK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACpCnF,OAAA;QAAIqE,SAAS,EAAC,YAAY;QAAAM,QAAA,EAAC;MAAI;QAAAK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACjC,CAAC;EAAA,eACN,CAAC;AAEP,CAAC;;AAED;AACA;AACA;AAAA/E,EAAA,CA3QMD,eAAe;EAAA,QAEDV,WAAW,EACGA,WAAW,EACxBA,WAAW,EACXD,WAAW;AAAA;AAAA8F,EAAA,GAL1BnF,eAAe;AA6QrB,eAAeA,eAAe;AAAC,IAAAmF,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}