{"ast": null, "code": "import { createSlice } from \"@reduxjs/toolkit\";\nexport const TimelineSlice = createSlice({\n  name: \"Timeline\",\n  initialState: {\n    timelineArr: [],\n    timelineToday: {},\n    pagination: {}\n  },\n  reducers: {\n    createTimelineRequest: () => {},\n    updateTimelineRequest: () => {},\n    getTimelineRequests: () => {},\n    getTimelineRequestsSuccess: (state, action) => {\n      if (action.payload.length === 0) {\n        state.timelineArr = [];\n        state.pagination = {};\n      } else {\n        state.timelineArr = action.payload.data;\n        state.pagination = action.payload.pagination;\n      }\n    },\n    getTimelineRequestByDate: () => {},\n    getTimelineRequestsTodaySuccess: (state, action) => {\n      state.timelineToday = action.payload.data;\n    },\n    updateTaskTimelineRequest: () => {},\n    deleteTaskTimelineRequest: () => {}\n  }\n});\nexport default TimelineSlice;", "map": {"version": 3, "names": ["createSlice", "TimelineSlice", "name", "initialState", "timelineArr", "timelineToday", "pagination", "reducers", "createTimelineRequest", "updateTimelineRequest", "getTimelineRequests", "getTimelineRequestsSuccess", "state", "action", "payload", "length", "data", "getTimelineRequestByDate", "getTimelineRequestsTodaySuccess", "updateTaskTimelineRequest", "deleteTaskTimelineRequest"], "sources": ["E:/Suraj Patil/Organization_Management_System/meraki-frontend-main/src/slices/slice/TimelineSlice.js"], "sourcesContent": ["import { createSlice } from \"@reduxjs/toolkit\";\r\n\r\n\r\nexport const TimelineSlice = createSlice({\r\n    name: \"Timeline\",\r\n    initialState: {\r\n        timelineArr: [],\r\n        timelineToday: {},\r\n        pagination: {}\r\n    },\r\n    reducers: {\r\n        createTimelineRequest: () => {},\r\n        updateTimelineRequest: () => {},\r\n        getTimelineRequests: () => {},\r\n        getTimelineRequestsSuccess: (state,action) => {\r\n            if(action.payload.length === 0) {\r\n                state.timelineArr = []\r\n                state.pagination = {}\r\n            } else {\r\n                state.timelineArr = action.payload.data\r\n                state.pagination = action.payload.pagination\r\n            }\r\n        },\r\n        getTimelineRequestByDate: () => {\r\n\r\n        },\r\n        getTimelineRequestsTodaySuccess: (state,action) => {\r\n            state.timelineToday = action.payload.data\r\n        },\r\n        updateTaskTimelineRequest: () => {},\r\n        deleteTaskTimelineRequest: () => {}\r\n\r\n    }\r\n});\r\n\r\nexport default TimelineSlice;"], "mappings": "AAAA,SAASA,WAAW,QAAQ,kBAAkB;AAG9C,OAAO,MAAMC,aAAa,GAAGD,WAAW,CAAC;EACrCE,IAAI,EAAE,UAAU;EAChBC,YAAY,EAAE;IACVC,WAAW,EAAE,EAAE;IACfC,aAAa,EAAE,CAAC,CAAC;IACjBC,UAAU,EAAE,CAAC;EACjB,CAAC;EACDC,QAAQ,EAAE;IACNC,qBAAqB,EAAEA,CAAA,KAAM,CAAC,CAAC;IAC/BC,qBAAqB,EAAEA,CAAA,KAAM,CAAC,CAAC;IAC/BC,mBAAmB,EAAEA,CAAA,KAAM,CAAC,CAAC;IAC7BC,0BAA0B,EAAEA,CAACC,KAAK,EAACC,MAAM,KAAK;MAC1C,IAAGA,MAAM,CAACC,OAAO,CAACC,MAAM,KAAK,CAAC,EAAE;QAC5BH,KAAK,CAACR,WAAW,GAAG,EAAE;QACtBQ,KAAK,CAACN,UAAU,GAAG,CAAC,CAAC;MACzB,CAAC,MAAM;QACHM,KAAK,CAACR,WAAW,GAAGS,MAAM,CAACC,OAAO,CAACE,IAAI;QACvCJ,KAAK,CAACN,UAAU,GAAGO,MAAM,CAACC,OAAO,CAACR,UAAU;MAChD;IACJ,CAAC;IACDW,wBAAwB,EAAEA,CAAA,KAAM,CAEhC,CAAC;IACDC,+BAA+B,EAAEA,CAACN,KAAK,EAACC,MAAM,KAAK;MAC/CD,KAAK,CAACP,aAAa,GAAGQ,MAAM,CAACC,OAAO,CAACE,IAAI;IAC7C,CAAC;IACDG,yBAAyB,EAAEA,CAAA,KAAM,CAAC,CAAC;IACnCC,yBAAyB,EAAEA,CAAA,KAAM,CAAC;EAEtC;AACJ,CAAC,CAAC;AAEF,eAAenB,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}